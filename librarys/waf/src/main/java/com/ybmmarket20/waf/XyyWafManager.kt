package com.ybmmarket20.waf

import android.app.Activity
import android.content.Context
import android.util.Log
import com.aliyun.TigerTally.TigerTallyAPI
import com.aliyun.TigerTally.captcha.api.TTCaptcha
import com.aliyun.TigerTally.captcha.api.TTCaptcha.TTListener
import com.aliyun.TigerTally.captcha.api.TTCaptcha.TTOption


/**
 * TODO: 1、初始化失败如何处理？
 * 2、弹窗重叠如何处理？如何判断当前已有弹窗？
 * 3、hash和签名是input为""或null如何处理？
 * 4、二次验证失败是否会返回数据？弹窗验证后需要重新发起请求？
 *
 */

//appkey代表阿里云客户平台分配的认证密钥
const val XYY_WAF_APP_KEY = "ByCrFhHb0ZEEM39ORIstHOTffw4AniVCvxHaQeDU34g2dOXX3XhiAdY5E-5GeB4sMY5PD9NU3h3dNezoY6828zow5Ykw45DG0JPfZNLUBoUsGkoKO7DgKk8Jux-13xl2nGhGhQE5OJ4GezPH0wTeY92w9UE20zP42CCFUakZfA8="
//二次验证成功
const val CHECK_RESPONSE_SUCCESS = 1
//二次验证失败
const val CHECK_RESPONSE_FAILURE = 0

/**
 * WAF管理类
 */
class XyyWafManager private constructor() {

    companion object {
        var instance: XyyWafManager? = null
            get() {
                if (field == null) {
                    field = XyyWafManager()
                }
                return field
            }
    }

    /**
     * 设置业务自定义的终端用户标识
     */
    fun setAccount(account: String) {
        TigerTallyAPI.setAccount(account)
    }

    /**
     * 一次初始化采集，代表一次设备信息采集，可以根据业务的不同，重新调用函数init初始化采集
     * 全量采集
     * @return int类型，返回初始化结果，0表示成功，-1表示失败。
     */
    fun initForAllInfo(context: Context) {
        val ret = TigerTallyAPI.init(
            context,
            XYY_WAF_APP_KEY,
            TigerTallyAPI.CollectType.DEFAULT
        )
        Log.i("XyyWaf_initForAllInfo", "$ret")
    }

    /**
     * 不采集隐私字段
     * @return int类型，返回初始化结果，0表示成功，-1表示失败。
     */
    fun initWithoutPrivacyInfo(context: Context) {
        val ret = TigerTallyAPI.init(
            context,
            XYY_WAF_APP_KEY,
            TigerTallyAPI.CollectType.NOT_GRANTED
        )
        Log.i("XyyWaf_initWithoutPrivacyInfo", "$ret")
    }

    /**
     * 获取数据签名
     */
    fun getWToken(wHash: String?): String? {
        val wToken = wHash?.let { TigerTallyAPI.vmpSign(1, it.toByteArray(Charsets.UTF_8)) }
//        val wToken = TigerTallyAPI.vmpSign(1, wHash!!.toByteArray(Charsets.UTF_8))
        Log.i("XyyWaf_getPostWToken", "$wToken")
        return wToken
    }

    /**
     * 获取Post HASH
     */
    fun getPostWHASH(input: String): String {
        return TigerTallyAPI.vmpHash(TigerTallyAPI.RequestType.POST, input.toByteArray(Charsets.UTF_8))
    }

    /**
     * 获取GET HASH
     */
    fun getGetWHASH(input: String): String {
        return TigerTallyAPI.vmpHash(TigerTallyAPI.RequestType.GET, input.toByteArray(Charsets.UTF_8))
    }

    /**
     * 二次校验
     */
    fun checkResponse(cookie: String, body: String): Int {
        return if (TigerTallyAPI.cptCheck(cookie, body) == 1) {
            CHECK_RESPONSE_SUCCESS
        } else CHECK_RESPONSE_FAILURE
    }

    /**
     * 验证当前已有弹窗
     */
    fun isShowCaptchaAlert(): Boolean {
        //TODO: 未实现
        return true
    }

    /**
     * 滑块校验
     */
    fun showCaptchaAlert(activity: Activity, callback: CaptchaCallback) {
        val option = TTOption()
//        option.titleText = "测试 Title"
//        option.descText = "测试 Description"
        option.language = "cn"
        option.cancelable = false
        option.hideError = true
        option.slideColor = "#007FFF"
        option.hideTraceId = true

        val captcha: TTCaptcha = TigerTallyAPI.cptCreate(activity, option, object : TTListener{
            override fun success(p0: TTCaptcha?, p1: String?) {
                captcha = p0
                callback.success(p1)
            }

            override fun failed(p0: TTCaptcha?, p1: String?) {
                captcha = p0
                callback.failed(p1)
            }

            override fun error(p0: TTCaptcha?, p1: Int, p2: String?) {
                captcha = p0
                callback.error(p1, p2)
            }
        })
        captcha.show()
    }

    var captcha: TTCaptcha? = null

    fun dismissCaptcha() {
        captcha?.dismiss()
    }

    interface CaptchaCallback {
        fun success(str: String?)

        fun failed(str: String?)

        fun error(code: Int, str: String?)
    }

}