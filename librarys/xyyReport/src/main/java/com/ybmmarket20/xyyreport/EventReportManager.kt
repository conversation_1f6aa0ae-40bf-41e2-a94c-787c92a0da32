package com.ybmmarket20.xyyreport

import android.content.Context
import android.util.Log
import com.google.gson.Gson
import com.quick.qt.analytics.QtTrackAgent
import com.ybmmarket20.common.util.Abase

/**
 * 事件埋点
 */
class EventReportManager {


    companion object {

        /**
         * 页面开始
         */
        @JvmStatic
        fun onPageStart(viewName: String) {
            QtTrackAgent.onPageStart(viewName)
        }

        /**
         * 页面结束
         */
        @JvmStatic
        fun onPageEnd(viewName: String) {
            QtTrackAgent.onPageEnd(viewName)
        }

        /**
         * 事件埋点，不带页面编码
         */
        @JvmStatic
        fun trackEvent(context: Context, eventId: String, eventParams: Map<String, Any?>) {
            QtTrackAgent.onEventObject(context, eventId, eventParams)
            SpmLogUtil.print("======")
            SpmLogUtil.print("eventId = $eventId")
            try {
                SpmLogUtil.print(Gson().toJson(eventParams))
            } catch (e: Exception) {
                e.printStackTrace()
            }
            SpmLogUtil.print("==========================================")
        }

        /**
         * 事件埋点，带页面编码
         */
        @JvmStatic
        fun trackEvent(context: Context, eventId: String, eventParams: Map<String, Any?>, pageName: String) {
            QtTrackAgent.onEventObject(context, eventId, eventParams, pageName)
        }

    }
}