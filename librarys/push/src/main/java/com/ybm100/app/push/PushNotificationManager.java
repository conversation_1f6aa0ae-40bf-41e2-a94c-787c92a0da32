package com.ybm100.app.push;

import android.content.Context;
import android.os.Looper;

import com.google.gson.Gson;
import com.ybm.app.bean.PushBean;
import com.ybm.app.common.BaseYBMApp;
import com.ybm.app.common.SmartExecutorManager;
import com.ybm.app.utils.BugUtil;

/**
 * 推送消息管理类，处理推送消息
 */
public class PushNotificationManager {

    private static Gson gson;
    public final static int HANDLEPASS = 1;//透传
    public final static int NOTIFICATIONMSG = 2;//通知
    public final static int MSGCLICKED = 3;//用户点击
    //消息透传处理（端内消息处理）
    public  static void handlePassThroughNotify(Context context,@PushManager.PushType String type,String msg){
        handlerBaseBg(msg,HANDLEPASS);
    }

    //通知栏消息处理
    public  static void handleNotificationMsg(Context context,@PushManager.PushType String type,String msg){
        handlerBaseBg(msg,NOTIFICATIONMSG);
    }

    //消息处理点击了
    public  static void handleMsgClicked(Context context,@PushManager.PushType String type,String msg){
        handlerBaseBg(msg,MSGCLICKED);
    }

    //处理通用，消息确认，启动应用
    private static void handlerBase(String content,int from){
        if(content == null){
            return;
        }
        if(gson == null){
            gson = new Gson();
        }
        try {
            PushBean bean = gson.fromJson(content,PushBean.class);
            if(bean !=null){
                //通知消息已经收到
                BaseYBMApp.getApp().handlerPush(content,bean.type,from,bean);
            }
        }catch (Exception e){
            BugUtil.sendBug(e);
        }
    }

    private static void handlerBaseBg(final String content, final int from){
        if(Looper.myLooper() == Looper.getMainLooper()){
            SmartExecutorManager.getInstance().execute(new Runnable() {
                @Override
                public void run() {
                    handlerBase(content,from);
                }
            });
        }else {
            handlerBase(content,from);
        }
    }

}
