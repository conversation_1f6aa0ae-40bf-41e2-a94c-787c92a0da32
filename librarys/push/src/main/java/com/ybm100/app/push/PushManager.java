package com.ybm100.app.push;

import android.app.ActivityManager;
import android.content.Context;
import android.os.Process;
import android.text.TextUtils;
import android.util.Log;

import com.ybm.app.common.BaseYBMApp;
import com.ybm.app.utils.SpUtil;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.util.HashSet;
import java.util.List;

import cn.jiguang.api.utils.JCollectionAuth;
import cn.jpush.android.api.JPushInterface;

/**
 * 推送管理类，管理第三方推送，注册，接收消息
 */
public class PushManager {
    private static final String TAG = "PushManager";
    private static final String JPUSH_KEY = "SP_JPUSH_KEY";

    public static String tokenType; // token类型的缓存

    public static String token; // token的缓存

    public final static int REGISTERTYPE = 3; // 1 全部集成 2 小米 3 极光

    @Retention(RetentionPolicy.SOURCE)
    public @interface PushType {

        String MI = "MI", JPUSH = "JPUSH";
    }

    public static void setAuth(Context context, boolean auth) {
        JCollectionAuth.setAuth(context, auth);
    }

    public static void register(Context context) {
//        if (RomUtils.isMiui()) {
//            registerMiPush(context);
//        } else {
//            registerMiPush(context);
//        }
        switch (REGISTERTYPE) {
            case 1:
                registerMiPush(context);
                registerJPush(context);
                break;
            case 2:
                registerMiPush(context);
                break;
            case 3:
                registerJPush(context);
                break;
        }

        String registionId = JPushInterface.getRegistrationID(context);
        Log.i("registionId", registionId);

    }

    /**
     * 注册小米push服务
     * 注册成功后会向MiMessageReceiver发送广播
     */
    private static void registerMiPush(Context context) {
//        final String APP_ID = "2882303761517508421";
//        final String APP_KEY = "5541750838421";
//        if (isInMainProcess(context)) {
//            MiPushClient.registerPush(context, APP_ID, APP_KEY);
//        }
//
//        LoggerInterface newLogger = new LoggerInterface() {
//            @Override
//            public void setTag(String tag) {
//                // ignore
//            }
//
//            @Override
//            public void log(String content, Throwable t) {
//                if(TextUtils.isEmpty(content)){
//                    return;
//                }
//                LogUtils.d(content); // log
//            }
//
//            @Override
//            public void log(String content) {
//
//            }
//        };
//        Logger.setLogger(context, newLogger);
//        if (!BuildConfig.DEBUG) {
//            Logger.disablePushFileLog(context);
//        }
    }


    /**
     * 注册极光推送
     *
     * @param context
     */
    private static void registerJPush(final Context context) {
        JPushInterface.setDebugMode(BaseYBMApp.getApp().isDebug());
        JPushInterface.init(context);
    }

    /**
     * 注意：因为推送服务等设置为运行在另外一个进程，这导致本Application会被实例化两次。
     * 而有些操作我们需要让应用的主进程时才进行，所以用到了这个方法
     */
    private static boolean isInMainProcess(Context context) {
        ActivityManager am = ((ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE));
        List<ActivityManager.RunningAppProcessInfo> processes = am.getRunningAppProcesses();
        String mainProcessName = context.getPackageName();
        int myPid = Process.myPid();
        if (processes == null || processes.size() <= 0 || mainProcessName == null) {
            return false;
        }
        for (ActivityManager.RunningAppProcessInfo info : processes) {
            if (info != null && info.pid == myPid && mainProcessName.equals(info.processName)) {
                return true;
            }
        }
        return false;
    }


    public static void setAlias(String alias) {
        if (TextUtils.isEmpty(alias)) {
            return;
        }
        switch (REGISTERTYPE) {
            case 1:
//                MiPushClient.setAlias(BaseYBMApp.getAppContext(),alias,null);
//                JPushInterface.setAlias(BaseYBMApp.getAppContext(),alias,null);
                break;
            case 2:
//                MiPushClient.setAlias(BaseYBMApp.getAppContext(),alias,null);
                break;
            case 3:
                JPushInterface.setAlias(BaseYBMApp.getAppContext(), 100, alias);
                break;
        }
    }

    public static void setTags(String tags) {
        if (TextUtils.isEmpty(tags)) {
            return;
        }
        switch (REGISTERTYPE) {
            case 1:
//                MiPushClient.subscribe(BaseYBMApp.getAppContext(),tags,null);
//                HashSet<String> jpushTags =  new HashSet<>();
//                jpushTags.add(tags);
//                JPushInterface.setTags(BaseYBMApp.getAppContext(),jpushTags,null);
                break;
            case 2:
//                MiPushClient.subscribe(BaseYBMApp.getAppContext(),tags,null);
                break;
            case 3:
                HashSet<String> jpushTags2 = new HashSet<>();
                jpushTags2.add(tags);
                JPushInterface.setTags(BaseYBMApp.getAppContext(), 100, jpushTags2);
                break;
        }
    }


    public static void setTags(List<String> tags) {
        if (tags == null || tags.isEmpty()) {
            return;
        }
        switch (REGISTERTYPE) {
            case 1:
//                MiPushClient.subscribe(BaseYBMApp.getAppContext(),tags,null);
//                HashSet<String> jpushTags =  new HashSet<>();
//                jpushTags.add(tags);
//                JPushInterface.setTags(BaseYBMApp.getAppContext(),jpushTags,null);
                break;
            case 2:
//                MiPushClient.subscribe(BaseYBMApp.getAppContext(),tags,null);
                break;
            case 3:
                HashSet<String> jpushTags2 = new HashSet<>();
                for (String str : tags) {
                    jpushTags2.add(str);
                }
                JPushInterface.setTags(BaseYBMApp.getAppContext(), 100, jpushTags2);
                break;
        }
    }

    public static void closePush() {
        switch (REGISTERTYPE) {
            case 1:

                break;
            case 2:
                break;
            case 3:
                JPushInterface.stopPush(BaseYBMApp.getAppContext());
                break;
        }
    }

    public static void openPush() {
        switch (REGISTERTYPE) {
            case 1:

                break;
            case 2:
                break;
            case 3:
                JPushInterface.resumePush(BaseYBMApp.getAppContext());
                break;
        }
    }


    public static void sendToken(String tokenStr) {
        token = tokenStr;
        SpUtil.writeString(JPUSH_KEY, token);
    }

    public static String getToken() {
        if (TextUtils.isEmpty(token)) {
            token = SpUtil.readString(JPUSH_KEY, "");
        }
        if (TextUtils.isEmpty(token)) {
            token = JPushInterface.getRegistrationID(BaseYBMApp.getAppContext());
        }
        return token;
    }
}
