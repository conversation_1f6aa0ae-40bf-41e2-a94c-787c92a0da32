package com.ydmmarket.report;


import android.content.Context;
import android.text.TextUtils;

import com.analysys.AnalysysAgent;
import com.analysys.AnalysysConfig;
import com.analysys.process.AgentProcess;
import com.ybm.app.BuildConfig;
import com.ydmmarket.report.annotation.ReportEventName;
import com.ydmmarket.report.annotation.ReportParamsKey;
import com.ydmmarket.report.manager.TrackManager;
import com.ydmmarket.report.utils.SpUtil;
import com.ydmmarket.report.utils.Utils;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 设置 打开 debug 模式，上线时请屏蔽
 * debug 模式，默认关闭状态。发布版本时 debugMode 模式设置为0
 * ○  0：表示关闭 Debug 模式
 * ○  1：表示打开 Debug 模式，但该模式下发送的数据仅用于调试，不计入平台数据统计
 * ○  2：表示打开 Debug 模式，该模式下发送的数据可计入平台数据统计
 * 注意：若设置其他值则不生效,使用默认值。
 *
 * <AUTHOR>
 */
public class ReportManager {
    private static String TAG = "ReportManager";
    private Context context;
    private static ReportManager instance;

    private ReportManager() {
    }

    public static ReportManager getInstance() {
        if (instance == null) {
            synchronized (ReportManager.class) {
                if (instance == null) {
                    instance = new ReportManager();
                }
            }
        }
        return instance;
    }

    public void init(Context context) {
        this.context = context;
        int debugMode = 0;
        String appKey = "";
        if (BuildConfig.DEBUG) {
            debugMode = 2;
            appKey = "e6f27194adb12753";
        } else {
            debugMode = 0;
            appKey = "a775ff9e4e84bc9a";
        }

        AnalysysAgent.setDebugMode(context, debugMode);
        //  对SDK开始初始化
        AnalysysConfig config = new AnalysysConfig();
        config.setAppKey(appKey);
        // 设置渠道
        config.setChannel("Android");
        // 设置追踪新用户的首次属性
        config.setAutoProfile(true);
        // 设置控件全埋点 点击自动上报总开关
//        config.setAutoTrackClick(true);
        config.setAutoTrackPageView(true);
        config.setAutoPageViewDuration(true);
        // 调用SDK初始接口
        AgentProcess.getInstance().setDataCollectEnable(false);
        AnalysysAgent.init(context, config);
        //  设置上传地址
        AnalysysAgent.setUploadURL(context, "http://jgmd.ybm100.com");
        HashMap<String, Object> mPropertyMap = new HashMap<>();
        mPropertyMap.put(TrackManager.FIELD_PLATFORM, "Android");
        mPropertyMap.put(TrackManager.FIELD_PROJECT_NAME, "药帮忙App");
        mPropertyMap.put(TrackManager.FIELD_UP_DATE, Utils.transform2Date());
        TrackManager.setSuperProperties(context, mPropertyMap);
        if (Utils.isLogin()) {
            //重新设置到通用属性里面  解决覆盖没有登录导致没数据的问题
            TrackManager.setSuperProperty(context, TrackManager.FIELD_ACCOUNT_ID, SpUtil.getAccountId());
            SpUtil.setMerchantid(context, SpUtil.getMerchantid());
            SpUtil.setMerchantInfo(context, SpUtil.getMerchantInfo());
            SpUtil.setMerchantBaseInfo(context, SpUtil.getMerchantBaseInfo());
        } else {
            TrackManager.setSuperProperty(context, TrackManager.FIELD_ACCOUNT_ID, "");
            SpUtil.setMerchantid(context, null);
            SpUtil.setMerchantInfo(context, null);
            SpUtil.setMerchantBaseInfo(context, null);
        }
        //可能用户直接从搜索页退出app  但是重新init初始化时，sdk内部本地已经存了 要去掉
        TrackManager.unRegisterSuperProperty(context, TrackManager.FIELD_SEARCH_SORT_STRATEGY_ID);
    }

    public <T> void report(T reportBean) {
        Class<?> clazz = reportBean.getClass();
        String eventName = "";
        if (clazz.getAnnotation(ReportEventName.class) == null) {
            return;
        } else {
            eventName = clazz.getAnnotation(ReportEventName.class).value();
            if (TextUtils.isEmpty(eventName)) {
                eventName = clazz.getSimpleName();
            }
        }
        HashMap<String, Object> params = new HashMap<>();
        reflectAllField(reportBean, params);
//        Log.d(TAG, "eventName>>" + eventName + "  params>>" + params.toString());
        TrackManager.eventTrack(context, eventName, params);
    }

    /**
     * 反射所有的字段填充到map
     *
     * @param bean
     * @param <T>
     * @return
     */
    private <T> HashMap<String, Object> reflectAllField(T bean, HashMap<String, Object> params) {
        Class clazz = bean.getClass();
        while (!Object.class.equals(clazz) && clazz != null) {
            Field[] declaredFields = clazz.getDeclaredFields();
            for (Field field : declaredFields) {
                try {
                    field.setAccessible(true);
                    String fileName = "";
                    Object filedValue = field.get(bean);
                    ReportParamsKey paramsKey = field.getAnnotation(ReportParamsKey.class);
                    if (paramsKey != null) {
                        boolean isSkip = paramsKey.isSkip();
                        if (isSkip) {
                            continue;
                        }
                        fileName = paramsKey.value();
                        if (TextUtils.isEmpty(fileName)) {
                            fileName = field.getName();
                        }
                        params.put(fileName, filedValue);
                    } else if (filedValue instanceof Integer ||
                            filedValue instanceof Double ||
                            filedValue instanceof  Long ||
                            filedValue instanceof Boolean ||
                            filedValue instanceof  String ||
                            filedValue instanceof List<?> ||
                            filedValue instanceof Map) {
                        continue;
                    } else if (filedValue != null) {
                        reflectAllField(filedValue, params);
                    }
                } catch (IllegalAccessException e) {
                    e.printStackTrace();
                }
            }
            clazz = clazz.getSuperclass();
        }
        return params;
    }

}
