package com.ydmmarket.report;

import com.ydmmarket.report.annotation.ReportEventName;
import com.ydmmarket.report.annotation.ReportParamsKey;

import java.util.ArrayList;

@ReportEventName("report")
public class ReportJavaBean {
    @ReportParamsKey("name")
    public String name;
    @ReportParamsKey("age")
    public String age;
    @ReportParamsKey("list")
    public ArrayList<String> list;
    public ExtendsBean extendsBean;
}

class ExtendsBean {
    @ReportParamsKey("parent")
    private String parent;
}