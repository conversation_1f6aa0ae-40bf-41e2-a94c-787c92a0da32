package com.ybmmarket20.common.widget;

import android.content.Context;
import android.util.AttributeSet;
import android.widget.LinearLayout;

import androidx.annotation.Px;

/** 用于需要圆角矩形框背景的LinearLayout的情况,减少直接使用LinearLayout时引入的shape资源文件 */
public class RoundLinearLayout extends LinearLayout {
    private RoundViewDelegate delegate;

    public RoundLinearLayout(Context context) {
        this(context, null);
    }

    public RoundLinearLayout(Context context, AttributeSet attrs) {
        super(context, attrs);
        delegate = new RoundViewDelegate(this, context, attrs);
    }

    /** use delegate to set attr */
    public RoundViewDelegate getDelegate() {
        return delegate;
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        if (delegate.isWidthHeightEqual() && getWidth() > 0 && getHeight() > 0) {
            int max = Math.max(getWidth(), getHeight());
            int measureSpec = MeasureSpec.makeMeasureSpec(max, MeasureSpec.EXACTLY);
            super.onMeasure(measureSpec, measureSpec);
            return;
        }

        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
    }

    @Override
    protected void onLayout(boolean changed, int left, int top, int right, int bottom) {
        super.onLayout(changed, left, top, right, bottom);
        if (delegate.isRadiusHalfHeight()) {
            delegate.setCornerRadius(getHeight() / 2);
        }else {
            delegate.setBgSelector();
        }
    }

    @Override
    public void setBackgroundColor(int color) {
        delegate.setBackgroundColor(color);
    }

    public void setCornerRadius_TL(int cornerRadius_TL) {
        this.delegate.setCornerRadius_TL(cornerRadius_TL);
    }

    public void setCornerRadius_TR(int cornerRadius_TR) {
        this.delegate.setCornerRadius_TR(cornerRadius_TR);
    }

    public void setCornerRadius_BL(int cornerRadius_BL) {
        this.delegate.setCornerRadius_BL(cornerRadius_BL);
    }

    public void setCornerRadius_BR(int cornerRadius_BR) {
        this.delegate.setCornerRadius_BR(cornerRadius_BR);
    }

    public void setCornerRadius(int cornerRadius_TL, int cornerRadius_TR, int cornerRadius_BL, int cornerRadius_BR) {
        this.delegate.setCornerRadius(cornerRadius_TL, cornerRadius_TR, cornerRadius_BL, cornerRadius_BR);
    }
    public void setCornerRadiusWithDp(int cornerRadius_TL, int cornerRadius_TR, int cornerRadius_BL, int cornerRadius_BR) {
        this.delegate.setCornerRadius(
                delegate.dp2px(cornerRadius_TL),
                delegate.dp2px(cornerRadius_TR),
                delegate.dp2px(cornerRadius_BL),
                delegate.dp2px(cornerRadius_BR));
    }
}
