package com.ybmmarket20.common.util;

import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.drawable.Drawable;
import android.text.style.ImageSpan;

/**
 * @author: yuhai<PERSON>
 * @time: 2018/9/13 下午2:14.
 * Description: textView 图片文字混排
 */
public class TextImage extends ImageSpan {

    public TextImage(Drawable drawable) {
        super(drawable);
    }

    public TextImage(Bitmap b) {
        super(b);
    }

    @Override
    public void draw(Canvas canvas, CharSequence text, int start, int end, float x, int top, int y, int bottom, Paint paint) {
        Drawable b = getDrawable();
        Paint.FontMetricsInt fm = paint.getFontMetricsInt();
        int transY = (y + fm.descent + y + fm.ascent) / 2 - b.getBounds().bottom / 2;//计算y方向的位移
        canvas.save();
        canvas.translate(x, transY);//绘制图片位移一段距离
        b.draw(canvas);
        canvas.restore();
    }

}
