package com.ybmmarket20.common.widget;

import android.content.Context;
import androidx.appcompat.widget.AppCompatEditText;
import android.util.AttributeSet;

/**
 * @author: yuhaibo
 * @time: 2018/11/19 下午6:58.
 * projectName: YBMMarket.
 * Description:
 */
public class RoundEditText extends AppCompatEditText {
    private RoundViewDelegate delegate;

    public RoundEditText(Context context) {
        this(context, null);
    }

    public RoundEditText(Context context, AttributeSet attrs) {
        super(context, attrs);
        delegate = new RoundViewDelegate(this, context, attrs);
    }

    public RoundEditText(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        delegate = new RoundViewDelegate(this, context, attrs);
    }

    /**
     * use delegate to set attr
     */
    public RoundViewDelegate getDelegate() {
        return delegate;
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        if (delegate.isWidthHeightEqual() && getWidth() > 0 && getHeight() > 0) {
            int max = Math.max(getWidth(), getHeight());
            int measureSpec = MeasureSpec.makeMeasureSpec(max, MeasureSpec.EXACTLY);
            super.onMeasure(measureSpec, measureSpec);
            return;
        }
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
    }

    @Override
    protected void onLayout(boolean changed, int left, int top, int right, int bottom) {
        super.onLayout(changed, left, top, right, bottom);
        if (delegate.isRadiusHalfHeight()) {
            delegate.setCornerRadius(getHeight() / 2);
        } else {
            delegate.setBgSelector();
        }
    }

    @Override
    public void setBackgroundColor(int color) {
        delegate.setBackgroundColor(color);
    }

    public void setStrokeWidth(int width) {
        delegate.setStrokeWidth(width);
    }

    public void setDash(int dashGap, int dashWidth) {
        delegate.setDash(dashGap, dashWidth);
    }


    public void setStrokeColor(int color) {
        delegate.setStrokeColor(color);
    }

    public void setCornerRadius(int radius) {
        delegate.setCornerRadius(radius);
    }
}
