package com.ybmmarket20.common.util;

import android.app.Activity;
import android.content.Context;
import android.content.res.AssetManager;
import android.content.res.Configuration;
import android.content.res.Resources;
import androidx.annotation.NonNull;
import android.util.DisplayMetrics;

import java.lang.ref.WeakReference;

/**
 * 获取 {@link Context}, {@link Resources}, {@link AssetManager}, {@link Configuration} , {@link DisplayMetrics} 基类
 *
 * @author: yuhaibo
 * @time: 2018/12/10 下午7:11.
 * projectName: YBMMarket.
 */
public final class Abase {

    public static final String TAG = "Abase";
    private static WeakReference<Activity> wActivity;

    private static Context sContext;

    public static void initialize(@NonNull Context context) {
        sContext = context.getApplicationContext();
    }

    public static Context getContext() {
        return sContext;
    }

    public static Resources getResources() {
        return Abase.getContext().getResources();
    }

    public static Resources.Theme getTheme() {
        return Abase.getContext().getTheme();
    }

    public static AssetManager getAssets() {
        return Abase.getContext().getAssets();
    }

    public static Configuration getConfiguration() {
        return Abase.getResources().getConfiguration();
    }

    public static DisplayMetrics getDisplayMetrics() {
        return Abase.getResources().getDisplayMetrics();
    }

    public static void setCurrentActivity(Activity activity) {
        wActivity = new WeakReference<>(activity);
    }

    public static Activity getCurrentActivity() {
        if (wActivity == null) return null;
        return wActivity.get();
    }
}