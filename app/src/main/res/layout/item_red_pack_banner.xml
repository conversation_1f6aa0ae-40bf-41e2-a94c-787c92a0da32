<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="36dp"
        android:layout_marginStart="10dp"
        android:layout_marginTop="10dp"
        android:layout_marginEnd="10dp"
        android:background="@drawable/shape_mine2_4_ffeeef">

        <ImageView
            android:id="@+id/iv_red_pack"
            android:layout_width="20dp"
            android:layout_height="23dp"
            android:layout_marginStart="6dp"
            android:src="@drawable/icon_red_packet"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_tips"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="6dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@color/black"
            android:textSize="13dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/tv_go_top_up"
            app:layout_constraintStart_toEndOf="@id/iv_red_pack"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="购物金充值最高返200元红包" />

        <com.ybmmarket20.common.widget.RoundTextView
            android:id="@+id/tv_go_top_up"
            android:layout_width="56dp"
            android:layout_height="22dp"
            android:layout_marginEnd="7dp"
            android:gravity="center"
            android:text="去充值"
            android:textColor="@color/white"
            android:textSize="12dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:rv_backgroundColor="@color/color_FE0F23"
            app:rv_cornerRadius="11dp" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
