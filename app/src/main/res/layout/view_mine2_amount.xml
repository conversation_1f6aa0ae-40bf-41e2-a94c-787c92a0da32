<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_height="wrap_content"
    android:layout_width="match_parent"
    android:paddingBottom="10dp"
    android:background="@drawable/shape_mine2_amount"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_mine2_amount"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:paddingTop="15dp"
        app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:itemCount="4"
        tools:listitem="@layout/item_mine2_amount_new" />

    <ImageView
        android:id="@+id/iv_bg_tip"
        android:layout_width="13dp"
        android:src="@drawable/icon_shopping_gold_tips_up"
        app:layout_constraintTop_toBottomOf="@id/rv_mine2_amount"
        android:layout_marginTop="4dp"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_marginStart="35dp"
        android:visibility="gone"
        tools:visibility="visible"
        android:layout_height="7dp"/>
    
<!--    <androidx.constraintlayout.widget.ConstraintLayout-->
<!--        android:id="@+id/cl_red_pack"-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="36dp"-->
<!--        android:visibility="gone"-->
<!--        tools:visibility="visible"-->
<!--        android:layout_marginStart="10dp"-->
<!--        android:layout_marginTop="10dp"-->
<!--        android:layout_marginEnd="10dp"-->
<!--        android:background="@drawable/shape_mine2_4_ffeeef"-->
<!--        app:layout_constraintBottom_toBottomOf="parent"-->
<!--        app:layout_constraintStart_toStartOf="parent"-->
<!--        app:layout_constraintTop_toBottomOf="@id/rv_mine2_amount">-->

<!--        <ImageView-->
<!--            android:id="@+id/iv_red_pack"-->
<!--            android:layout_width="20dp"-->
<!--            android:layout_height="23dp"-->
<!--            android:layout_marginStart="6dp"-->
<!--            android:src="@drawable/icon_red_packet"-->
<!--            app:layout_constraintBottom_toBottomOf="parent"-->
<!--            app:layout_constraintStart_toStartOf="parent"-->
<!--            app:layout_constraintTop_toTopOf="parent" />-->

<!--        <TextView-->
<!--            android:id="@+id/tv_tips"-->
<!--            android:layout_width="0dp"-->
<!--            android:layout_height="wrap_content"-->
<!--            tools:text="购物金充值最高返200元红包"-->
<!--            android:textColor="@color/black"-->
<!--            android:textSize="13dp"-->
<!--            android:ellipsize="end"-->
<!--            android:maxLines="1"-->
<!--            app:layout_constraintBottom_toBottomOf="parent"-->
<!--            app:layout_constraintEnd_toStartOf="@id/tv_go_top_up"-->
<!--            app:layout_constraintStart_toEndOf="@id/iv_red_pack"-->
<!--            android:layout_marginHorizontal="6dp"-->
<!--            app:layout_constraintTop_toTopOf="parent" />-->

<!--        <com.ybmmarket20.common.widget.RoundTextView-->
<!--            android:id="@+id/tv_go_top_up"-->
<!--            android:layout_width="56dp"-->
<!--            android:layout_height="22dp"-->
<!--            app:layout_constraintTop_toTopOf="parent"-->
<!--            app:layout_constraintBottom_toBottomOf="parent"-->
<!--            app:layout_constraintEnd_toEndOf="parent"-->
<!--            android:textSize="12dp"-->
<!--            android:text="去充值"-->
<!--            android:textColor="@color/white"-->
<!--            android:gravity="center"-->
<!--            android:layout_marginEnd="7dp"-->
<!--            app:rv_backgroundColor="@color/color_FE0F23"-->
<!--            app:rv_cornerRadius="11dp" />-->

<!--    </androidx.constraintlayout.widget.ConstraintLayout>-->
<!--    <androidx.constraintlayout.widget.ConstraintLayout-->
<!--        android:id="@+id/cl_red_pack_wechat"-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="36dp"-->
<!--        android:visibility="gone"-->
<!--        tools:visibility="visible"-->
<!--        android:layout_marginStart="10dp"-->
<!--        android:layout_marginTop="10dp"-->
<!--        android:layout_marginEnd="10dp"-->
<!--        android:background="@drawable/shape_mine2_4_ffeeef"-->
<!--        app:layout_constraintBottom_toBottomOf="parent"-->
<!--        app:layout_constraintStart_toStartOf="parent"-->
<!--        app:layout_constraintTop_toBottomOf="@id/cl_red_pack">-->

<!--        <ImageView-->
<!--            android:id="@+id/iv_red_pack_wechat"-->
<!--            android:layout_width="20dp"-->
<!--            android:layout_height="23dp"-->
<!--            android:layout_marginStart="6dp"-->
<!--            android:src="@drawable/icon_wechat_login_guide_bg"-->
<!--            app:layout_constraintBottom_toBottomOf="parent"-->
<!--            app:layout_constraintStart_toStartOf="parent"-->
<!--            app:layout_constraintTop_toTopOf="parent" />-->

<!--        <TextView-->
<!--            android:id="@+id/tv_tips_wechat"-->
<!--            android:layout_width="0dp"-->
<!--            android:layout_height="wrap_content"-->
<!--            tools:text="购物金充值最高返200元红包"-->
<!--            android:textColor="@color/black"-->
<!--            android:textSize="13dp"-->
<!--            android:ellipsize="end"-->
<!--            android:maxLines="1"-->
<!--            app:layout_constraintBottom_toBottomOf="parent"-->
<!--            app:layout_constraintEnd_toStartOf="@id/tv_go_top_up_wechat"-->
<!--            app:layout_constraintStart_toEndOf="@id/iv_red_pack_wechat"-->
<!--            android:layout_marginHorizontal="6dp"-->
<!--            app:layout_constraintTop_toTopOf="parent" />-->

<!--        <com.ybmmarket20.common.widget.RoundTextView-->
<!--            android:id="@+id/tv_go_top_up_wechat"-->
<!--            android:layout_width="56dp"-->
<!--            android:layout_height="22dp"-->
<!--            app:layout_constraintTop_toTopOf="parent"-->
<!--            app:layout_constraintBottom_toBottomOf="parent"-->
<!--            app:layout_constraintEnd_toEndOf="parent"-->
<!--            android:textSize="12dp"-->
<!--            android:text="去充值"-->
<!--            android:textColor="@color/white"-->
<!--            android:gravity="center"-->
<!--            android:layout_marginEnd="7dp"-->
<!--            app:rv_backgroundColor="@color/color_FE0F23"-->
<!--            app:rv_cornerRadius="11dp" />-->

<!--    </androidx.constraintlayout.widget.ConstraintLayout>-->

    <!-- 红包轮播Banner -->
    <com.youth.banner.Banner
        android:id="@+id/banner_red_pack_container"
        android:layout_width="match_parent"
        android:layout_height="36dp"
        android:layout_marginStart="10dp"
        android:layout_marginTop="10dp"
        android:layout_marginEnd="10dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/rv_mine2_amount"
        tools:visibility="visible" />

</androidx.constraintlayout.widget.ConstraintLayout>