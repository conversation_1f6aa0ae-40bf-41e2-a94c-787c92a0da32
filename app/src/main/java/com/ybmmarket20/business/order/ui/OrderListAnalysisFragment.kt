package com.ybmmarket20.business.order.ui

import com.ybmmarket20.bean.Coupon
import com.ybmmarket20.common.LazyFragment
import com.ybmmarket20.constant.IntentCanst
import com.ybmmarket20.home.MainActivity
import com.ybmmarket20.xyyreport.page.orderList.OrderListReport
import com.ybmmarket20.xyyreport.page.orderList.OrderListReportUtil
import com.ybmmarket20.xyyreport.page.rebateVoucher.RebateVoucherReport
import com.ybmmarket20.xyyreport.spm.SpmUtil

abstract class OrderListAnalysisFragment: LazyFragment(), IOrderListAnalysisParams {

    private var mOrderStatus = 0
    private var isRefreshed = false
    var mAnalysisCouponInfo: Coupon? = null
    var mIsShownRebateVoucher = false

    /**
     * pv
     */
    fun pvTrack() {
//        if (!isRefreshed) {
//            isRefreshed = true
//            return
//        }
        val orderStatus = arguments?.getString(IntentCanst.ORDER_STATE)?.toInt()?: 1
        mOrderStatus = orderStatus
        val orderStatusText = OrderListReportUtil.getOrderStatusInfoByStatus(orderStatus)?.orderReportTag
        OrderListReport.pvOrderList(requireActivity(), orderStatusText)
        SpmUtil.checkAnalysisContext(requireActivity()) {
            (notNullActivity as MainActivity).trackCommonTabComponentExposure(it)
            //有优惠券信息才埋点
            if (mAnalysisCouponInfo != null) {
                OrderListReport.onOrderListCouponExposure(it, mAnalysisCouponInfo?.sceneGroupId, "去使用", mAnalysisCouponInfo?.couponId)
            }
            //消费返
            if (mIsShownRebateVoucher) {
                RebateVoucherReport.trackRebateVoucherComponentExposureForOrderList(it)
            }
        }
    }

    override fun getOrderStatus(): Int = mOrderStatus
}

interface IOrderListAnalysisParams {
    fun getOrderStatus(): Int
}

