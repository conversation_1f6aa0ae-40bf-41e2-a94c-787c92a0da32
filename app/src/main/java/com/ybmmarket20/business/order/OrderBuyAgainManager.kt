package com.ybmmarket20.business.order

import android.content.Context
import android.content.Intent
import android.widget.TextView
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.google.gson.Gson
import com.ybm.app.bean.NetError
import com.ybm.app.common.BaseYBMApp
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.EmptyBean
import com.ybmmarket20.bean.OrderActionBean
import com.ybmmarket20.bean.OrderBuyAgainProduct
import com.ybmmarket20.bean.OrderStockState
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.common.BaseResponse
import com.ybmmarket20.common.RequestParams
import com.ybmmarket20.common.util.Abase
import com.ybmmarket20.common.util.ToastUtils
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.constant.IntentCanst
import com.ybmmarket20.network.HttpManager
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.utils.SpUtil
import com.ybmmarket20.view.BuyAgainCantBuyBottomDialog
import com.ybmmarket20.view.BuyAgainCantBuyCenterDialog
import com.ybmmarket20.xyyreport.page.order.ChangeCartUtil
import com.ybmmarketkotlin.utils.RouterJump.jump2ShopCar

/**
 * @class   OrderBuyAgainManager
 * <AUTHOR>
 * @date  2025/2/9
 * @description 点击再次购买按钮流程
 */
class OrderBuyAgainManager {

    companion object{

        //再次购买
        fun buyAgain(textView: TextView,bean: OrderActionBean) {
            showProgress()
            requestOrderStockState(textView,bean.orderNo)
        }

        //再次购买
        fun buyAgain(textView: TextView,orderNo: String?,skuId:String?,productQuantity:String?) {
            showProgress()
            requestOrderStockState(textView,orderNo,skuId,productQuantity)
        }

        /**
         * 订单商品可购买状态
         */
        private fun requestOrderStockState(textView: TextView,orderNo:String?="",skuId:String? = "",productQuantity:String? = "") {
            textView.isEnabled = false
            val merchantid = SpUtil.getMerchantid()
            val params = RequestParams()
            params.put("merchantId", merchantid)
            params.put("orderNo", orderNo?:"")
            params.put("skuId", skuId?:"")
            params.put("productQuantity", productQuantity?:"")
            HttpManager.getInstance().post(AppNetConfig.ORDER_STOCK_STATE, params, object : BaseResponse<OrderStockState?>() {
                override fun onSuccess(content: String?, obj: BaseBean<OrderStockState?>?, data: OrderStockState?) {
                    dismissProgress()
                    if (obj != null && obj.isSuccess && data != null) {
                        var code: String? = ""
                        if (data.stockCode != null) {
                            code = data.stockCode
                        }
                        when (code) {
                            "1" -> orderProductCartAdd(data.availableWareList,orderNo)
                            "2" -> {
                                var title: String? = ""
                                if (data.message != null) {
                                    title = data.message
                                }
                                val dialog = BuyAgainCantBuyCenterDialog(textView.context, title!!, BuyAgainCantBuyCenterDialog.TYPE_2, data.noAvailableWareList)
                                dialog.mClickLeftListener = { buyAgainCantBuyCenterDialog: BuyAgainCantBuyCenterDialog ->
                                    buyAgainCantBuyCenterDialog.dismiss()
                                    null
                                }
                                dialog.mClickRightListener = { buyAgainCantBuyCenterDialog: BuyAgainCantBuyCenterDialog ->
                                    orderProductCartAdd(data.availableWareList,orderNo)
                                    buyAgainCantBuyCenterDialog.dismiss()
                                    null
                                }
                                dialog.show()
                            }

                            "3" -> {
                                    val isOneProduct = data.noAvailableWareList?.size == 1
                                    if (isOneProduct) {
                                        val orderBuyAgainProduct: OrderBuyAgainProduct? = data.noAvailableWareList?.get(0)
                                        val bottomDialog = BuyAgainCantBuyBottomDialog(textView.context, orderBuyAgainProduct)
                                        bottomDialog.show()
                                    } else {
                                        var title2: String? = ""
                                        if (data.message != null) {
                                            title2 = data.message
                                        }
                                        val dialog1 = BuyAgainCantBuyCenterDialog(textView.context, title2!!, BuyAgainCantBuyCenterDialog.TYPE_1, data.noAvailableWareList)
                                        dialog1.mClickLeftListener = { buyAgainCantBuyCenterDialog: BuyAgainCantBuyCenterDialog ->
                                            buyAgainCantBuyCenterDialog.dismiss()
                                        }
                                        dialog1.mClickRightListener = { buyAgainCantBuyCenterDialog: BuyAgainCantBuyCenterDialog ->
                                            val action = "ybmpage://main/0"
                                            RoutersUtils.open(action)
                                            buyAgainCantBuyCenterDialog.dismiss()
                                        }
                                        dialog1.show()
                                    }
                            }

                            "4" -> {
                                var mSkuId = ""
                                if (data.availableWareList?.size == 1) { //跳详情去可购买的List去拿
                                    mSkuId = data.availableWareList?.get(0)?.skuId?:""
                                }
                                val mUrl = "ybmpage://productdetail?" + IntentCanst.PRODUCTID + "=" + mSkuId
                                RoutersUtils.open(mUrl)
                            }

                            else -> {}
                        }
                    }
                    textView.isEnabled = true
                }

                override fun onFailure(error: NetError) {
                    dismissProgress()
                    textView.isEnabled = true
                }
            })
        }

        /**
         * 剩余商品加入购物车
         */
        private fun orderProductCartAdd(availableWareList: ArrayList<OrderBuyAgainProduct>?,orderNo:String? = "") {
            val pMap = ChangeCartUtil.addQtDataToParams(Abase.getCurrentActivity(), mutableMapOf())
            val params = RequestParams()
            pMap?.forEach {
                params.put(it.key, it.value)
            }
            val availableWareListJson = Gson().toJson(availableWareList)
            params.put("orderNo", orderNo?:"")
            params.put("availableWareList", availableWareListJson)
            HttpManager.getInstance().post(AppNetConfig.ORDER_CART_ADD, params, object : BaseResponse<EmptyBean?>() {
                override fun onSuccess(content: String, obj: BaseBean<EmptyBean?>, data: EmptyBean?) {
                    dismissProgress()
                    if (obj != null && obj.isSuccess) {
                        jump2ShopCar()
                        LocalBroadcastManager.getInstance(BaseYBMApp.getAppContext()).sendBroadcast(Intent(IntentCanst.ACTION_CHANG_CAR_NUMBER))
                    }
                }

                override fun onFailure(error: NetError) {
                    dismissProgress()
                }
            })
        }


        private fun showProgress() {
            if (BaseYBMApp.getApp().currActivity != null) {
                (BaseYBMApp.getApp().currActivity as BaseActivity).showProgress()
            }
        }

        private fun getMContext(): Context? {
            return if (BaseYBMApp.getApp().currActivity != null) {
                BaseYBMApp.getApp().currActivity
            } else BaseYBMApp.getAppContext()
        }

        private fun dismissProgress() {
            if (BaseYBMApp.getApp().currActivity != null) {
                (BaseYBMApp.getApp().currActivity as BaseActivity).dismissProgress()
            }
        }
    }
}