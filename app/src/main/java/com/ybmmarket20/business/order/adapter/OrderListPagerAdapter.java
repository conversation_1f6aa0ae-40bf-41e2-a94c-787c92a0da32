package com.ybmmarket20.business.order.adapter;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentPagerAdapter;
import androidx.fragment.app.FragmentStatePagerAdapter;


import com.ybmmarket20.business.order.ui.OrderListFragment;

import java.util.List;

public class OrderListPagerAdapter extends FragmentPagerAdapter {

    private String[] titles={"全部","待支付","待配送","配送中","待收货","完成","待领取","售后"};

    private List<OrderListFragment> fragments;

    public OrderListPagerAdapter(FragmentManager fm,List<OrderListFragment> fragments) {
        super(fm);
        this.fragments=fragments;
    }


    @Override
    public int getCount() {
        return fragments.size();
    }

    @Override
    public CharSequence getPageTitle(int position) {
        return fragments.get(position).getTitle();
    }

    @Override
    public Fragment getItem(int position) {
        return fragments.get(position);
    }


}
