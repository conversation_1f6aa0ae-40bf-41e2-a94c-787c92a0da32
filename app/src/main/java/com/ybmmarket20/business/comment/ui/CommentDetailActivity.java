package com.ybmmarket20.business.comment.ui;

import android.content.Context;
import android.content.Intent;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.ybm.app.bean.NetError;
import com.ybm.app.view.WrapLinearLayoutManager;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.CommentDetailBean;
import com.ybmmarket20.business.comment.adapter.SimpleLabelAdapter;
import com.ybmmarket20.business.comment.adapter.SimplePicAdapter;
import com.ybmmarket20.common.BaseActivity;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.utils.SpUtil;

import java.util.List;

import butterknife.Bind;

public class CommentDetailActivity extends BaseActivity {

    @Bind(R.id.content)
    public LinearLayout container;
    @Bind(R.id.iv_back)
    public ImageView ivBack;

    @Bind(R.id.errorView)
    public LinearLayout errorView;


    public static void launch(Context mContext, String orderNo) {

        Intent intent = new Intent(mContext, CommentDetailActivity.class);
        intent.putExtra("orderNo", orderNo);
        mContext.startActivity(intent);
    }

    @Override
    protected void initData() {
        setTitle("评价详情");

        Intent intent = getIntent();
        String orderNo = intent.getStringExtra("orderNo");
        ivBack.setImageResource(R.drawable.icon_comment_dialog_delete);
        RequestParams params = new RequestParams();
        params.put("orderNo", orderNo);
        params.put("merchantId", SpUtil.getMerchantid());
        HttpManager.getInstance().post(AppNetConfig.COMMENT_DETAIL, params, new BaseResponse<CommentDetailBean>() {

            @Override
            public void onFailure(NetError error) {
                errorView.setVisibility(View.VISIBLE);
            }

            @Override
            public void onSuccess(String content, BaseBean<CommentDetailBean> response, CommentDetailBean detail) {
                if (response.isSuccess()) {
                    if (detail.saleServiceLabelProperty >= 21 && detail.saleServiceLabelProperty <= 23) {
                        //销售评价显示
                        CommentDetailItem commentDetailItem = new CommentDetailItem(container);
                        commentDetailItem.bindData("销售服务质量", detail.saleServiceLabelProperty, detail.saleServiceText, detail.saleServicePhotoUrl, detail.saleServiceLabelList);
                        container.addView(commentDetailItem.getView());
                    }
                    if (detail.clientServiceLabelProperty >= 21 && detail.clientServiceLabelProperty <= 23) {
                        //客户服务评价显示
                        CommentDetailItem commentDetailItem = new CommentDetailItem(container);
                        commentDetailItem.bindData("客服服务质量", detail.clientServiceLabelProperty, detail.clientServiceText, detail.clientServicePhotoUrl, detail.clientServiceLabelList);
                        container.addView(commentDetailItem.getView());
                    }
                    if (detail.transportServiceLabelProperty >= 21 && detail.transportServiceLabelProperty <= 23) {
                        //物流服务评价显示
                        CommentDetailItem commentDetailItem = new CommentDetailItem(container);
                        commentDetailItem.bindData("物流服务质量", detail.transportServiceLabelProperty, detail.transportServiceText, detail.transportServicePhotoUrl, detail.transportServiceLabelList);
                        container.addView(commentDetailItem.getView());
                    }
                }else {
                    errorView.setVisibility(View.VISIBLE);

                }

            }
        });
    }


    @Override
    public int getContentViewId() {
        return R.layout.activity_comment_detail;
    }


    public class CommentDetailItem {

        private View view;
        private TextView tvType;//评价类型
        private TextView tvLevel;
        private ImageView ivLevel;
        private TextView tvContent;

        private RecyclerView rvPic;

        private RecyclerView rvLabel;

        private View divider;

        public CommentDetailItem(ViewGroup parent) {
            Context context = parent.getContext();
            view = LayoutInflater.from(context).inflate(R.layout.item_comment_detail, parent, false);
            tvType = view.findViewById(R.id.tv_type);
            tvLevel = view.findViewById(R.id.tv_level);
            ivLevel = view.findViewById(R.id.iv_level);
            tvContent = view.findViewById(R.id.tv_comment_content);
            divider = view.findViewById(R.id.divider);
            rvPic = view.findViewById(R.id.rv_pic);
            rvLabel = view.findViewById(R.id.rv_label);
        }

        public void bindData(String title, int commentLevel, String commentContent, List<String> picList, List<String> labelList) {
            tvType.setText(title);
            switch (commentLevel) {
                case 21:
                    tvLevel.setText("吐槽");
                    ivLevel.setImageResource(R.drawable.icon_comment_face_level1);

                    break;
                case 22:
                    tvLevel.setText("满意");

                    ivLevel.setImageResource(R.drawable.icon_comment_face_level2);

                    break;
                case 23:
                    tvLevel.setText("超赞");
                    ivLevel.setImageResource(R.drawable.icon_comment_face_level3);

                    break;
            }
            if (labelList != null && labelList.size() > 0) {
                divider.setVisibility(View.VISIBLE);
                rvLabel.setVisibility(View.VISIBLE);
                SimpleLabelAdapter simpleLabelAdapter = new SimpleLabelAdapter(CommentDetailActivity.this, labelList);
                GridLayoutManager gridLayoutManager = new GridLayoutManager(CommentDetailActivity.this, 3);
                rvLabel.setLayoutManager(gridLayoutManager);
                rvLabel.setAdapter(simpleLabelAdapter);
            }

            if (!TextUtils.isEmpty(commentContent)) {
                tvContent.setVisibility(View.VISIBLE);
                tvContent.setText(commentContent);
            }

            if (picList != null && picList.size() > 0) {
                rvPic.setVisibility(View.VISIBLE);
                SimplePicAdapter simplePicAdapter = new SimplePicAdapter(CommentDetailActivity.this, picList);
                WrapLinearLayoutManager linearLayoutManager = new WrapLinearLayoutManager(CommentDetailActivity.this);
                linearLayoutManager.setOrientation(WrapLinearLayoutManager.HORIZONTAL);
                rvPic.setLayoutManager(linearLayoutManager);
                rvPic.setAdapter(simplePicAdapter);
            }

        }

        public View getView() {
            return view;
        }

    }


}
