package com.ybmmarket20.business.comment.ui;

import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RadioGroup;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.ybm.app.view.WrapLinearLayoutManager;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.CommentLabelBean;
import com.ybmmarket20.business.comment.adapter.LabelAdapter;
import com.ybmmarket20.business.comment.adapter.PicAdapter;

import java.util.List;

/**
 * 评论的item
 */
public class CommentItemFactory {

    public static final int TYPE_BD = 1;

    public static final int TYPE_SERVICE = 2;

    public static final int TYPE_EXPRESS = 3;

    public static CommentItemView getCommentItemView(int type, List<List<CommentLabelBean>> data, ViewGroup parent) {

        return new CommentItemView(parent, type, data);
    }

    public static class CommentItemView {

        public int commentItemType;

        public CommentActivity context;

        public EditText etComment;

        public RelativeLayout rlComment;

        public RecyclerView rvLabel;

        public RecyclerView rvPickPicture;

        public RadioGroup rgLevel;

        public TextView tvTitle;

        public ImageView ivTitle;

        public TextView tvCounter;

        public LinearLayout view;

        public int type;

        private LabelAdapter labelAdapter;

        public PicAdapter picAdapter;

        private List<List<CommentLabelBean>> data;


        public CommentItemView(ViewGroup parent, int type, List<List<CommentLabelBean>> data) {
            if (parent == null) {
                return;
            }
            this.context = (CommentActivity) parent.getContext();
            this.data = data;
            this.type = type;
            addEditItem();
            initView(parent);
            initTitleByType();
        }

        private void addEditItem() {
            for (List<CommentLabelBean> bean : data) {
                CommentLabelBean commentLabelBean = new CommentLabelBean();
                commentLabelBean.evaluateLabelName = "其他评价";
                commentLabelBean.isEditLabel = true;
                bean.add(commentLabelBean);
            }

        }

        public interface DataChangeListener {
            void dataChange();
        }

        private DataChangeListener listener;

        public void setListener(DataChangeListener listener) {
            this.listener = listener;
        }

        private void initTitleByType() {
            switch (type) {
                case TYPE_BD:
                    ivTitle.setImageResource(R.drawable.icon_comment_bd);
                    tvTitle.setText("销售服务质量");

                    break;
                case TYPE_SERVICE:
                    ivTitle.setImageResource(R.drawable.icon_comment_service);
                    tvTitle.setText("客服服务质量");
                    break;
                case TYPE_EXPRESS:
                    ivTitle.setImageResource(R.drawable.icon_comment_express);
                    tvTitle.setText("物流服务质量");
                    break;


            }

        }

        private ViewGroup parent;

        private void initView(ViewGroup parent) {
            this.parent = parent;
            view = (LinearLayout) LayoutInflater.from(context).inflate(R.layout.item_comment, parent, false);
            etComment = view.findViewById(R.id.et_comment);
            rvLabel = view.findViewById(R.id.rv_label);
            rvPickPicture = view.findViewById(R.id.rv_pick_pic);
            rgLevel = view.findViewById(R.id.rg_level);
            tvTitle = view.findViewById(R.id.tv_title);
            ivTitle = view.findViewById(R.id.iv_title);
            tvCounter = view.findViewById(R.id.tv_counter);
            rlComment = view.findViewById(R.id.rl_comment);
            picAdapter = new PicAdapter(context);
            rvLabel.setLayoutManager(new GridLayoutManager(context, 3));
            WrapLinearLayoutManager linearLayoutManager = new WrapLinearLayoutManager(context);
            linearLayoutManager.setOrientation(WrapLinearLayoutManager.HORIZONTAL);
            rvPickPicture.setLayoutManager(linearLayoutManager);
            rvPickPicture.setAdapter(picAdapter);

            rgLevel.setOnCheckedChangeListener(new RadioGroup.OnCheckedChangeListener() {
                @Override
                public void onCheckedChanged(RadioGroup radioGroup, int i) {

                    if (labelAdapter == null) {
                        labelAdapter = new LabelAdapter(context);
                        labelAdapter.setListener(new LabelAdapter.CheckChangeListener() {
                            @Override
                            public void onCheckChange(CommentLabelBean bean) {

                                if (bean.isEditLabel) {
                                    if (bean.isChecked) {
                                        rlComment.setVisibility(View.VISIBLE);
                                        rvPickPicture.setVisibility(View.VISIBLE);
                                    } else {
                                        rlComment.setVisibility(View.GONE);
                                        rvPickPicture.setVisibility(View.GONE);
                                    }
                                }
                            }
                        });
                        rvLabel.setAdapter(labelAdapter);
                    }

                    List<CommentLabelBean> commentLabelBeans = null;
                    switch (i) {
                        case R.id.rb_level_1:
                            if (data != null && data.size() > 0) {
                                commentLabelBeans = data.get(0);
                                level = 21;
                                etComment.setHint("您的建议会督促我做的更好~");
                            }
                            break;
                        case R.id.rb_level_2:
                            if (data != null && data.size() > 1) {

                                commentLabelBeans = data.get(1);
                                etComment.setHint("说说哪里好，鼓励一下我吧~");
                                level = 22;
                            }
                            break;
                        case R.id.rb_level_3:
                            if (data != null && data.size() > 2) {
                                commentLabelBeans = data.get(2);
                                etComment.setHint("说说哪里好，鼓励一下我吧~");
                                level = 23;
                            }
                            break;

                    }
                    if (commentLabelBeans != null) {
                        labelAdapter.setData(commentLabelBeans);
                        boolean showEdit = false;
                        for (CommentLabelBean commentLabelBean : commentLabelBeans) {

                            if (commentLabelBean.isEditLabel && commentLabelBean.isChecked) {
                                showEdit = true;
                                break;
                            }

                        }
                        if (showEdit) {
                            rlComment.setVisibility(View.VISIBLE);
                            rvPickPicture.setVisibility(View.VISIBLE);
                        } else {
                            rlComment.setVisibility(View.GONE);
                            rvPickPicture.setVisibility(View.GONE);
                        }
                    }

                    if (rvLabel.getVisibility() != View.VISIBLE) {
                        rvLabel.setVisibility(View.VISIBLE);
                    }

                    if (listener != null) {
                        listener.dataChange();
                    }

                }
            });


            etComment.setOnFocusChangeListener(new View.OnFocusChangeListener() {
                @Override
                public void onFocusChange(View v, boolean hasFocus) {
                    if (hasFocus) {

                        context.myTreeObserver.setEditText(etComment, tvCounter);

                    }
                }
            });

        }


        public View getView() {
            return view;
        }


        public ResultData getResultData() {
            //包含多个选择的标签  文本 等级 图片
            if (level == 0) {
                return null;
            } else {
                ResultData data = new ResultData();
                data.level = level;
                boolean editItemBeChecked = false;
                List<CommentLabelBean> labelList = labelAdapter.getData();
                if (labelList != null && labelList.size() > 0) {

                    StringBuilder labelSb = new StringBuilder();
                    for (CommentLabelBean commentLabelBean : labelList) {
                        if (commentLabelBean.isEditLabel) {
                            editItemBeChecked = commentLabelBean.isChecked;
                        } else if (commentLabelBean.isChecked) {
                            labelSb.append(commentLabelBean.id).append(',');
                        }
                    }
                    if (labelSb.length() > 0) {
                        data.labels = labelSb.substring(0, labelSb.length() - 1);
                    }
                }

                if (editItemBeChecked) {
                    //只有编辑选项被勾选时才上传文本内容及图片
                    List<PicAdapter.PicInfo> picInfoList = picAdapter.getPicInfoList();
                    if (picInfoList != null && picInfoList.size() > 0 && !picInfoList.get(0).isAddItem) {
                        //认为集合里面有实际数据
                        StringBuilder labelSb = new StringBuilder();
                        for (PicAdapter.PicInfo bean : picInfoList) {
                            if (!TextUtils.isEmpty(bean.netUrl)) {
                                labelSb.append(bean.netUrl).append(',');
                            }
                        }
                        if (labelSb.length() > 0) {
                            data.images = labelSb.substring(0, labelSb.length() - 1);
                        }

                    }
                    data.content = etComment.getText().toString().trim();
                }

                return data;
            }
        }

        private int level;

    }

    public static class ResultData {

        public int level; // 21 22 23

        public String labels;//标签id 以逗号隔开

        public String images;//图片集合

        public String content = "";//评价内容

    }

}
