package com.ybmmarket20.business.comment.ui;

import android.content.Context;
import androidx.annotation.NonNull;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.RadioGroup;

import com.ybmmarket20.R;
import com.ybmmarket20.common.util.ToastUtils;
import com.ybmmarket20.view.TransparentDialog;

public class CommentExtraInfoDialog extends TransparentDialog {

    private EditText etPhone;

    private EditText etName;

    private Button btCommit;

    private RadioGroup rg;

    private int gender=1;

    private String name;

    private String phoneNumber;


    public CommentExtraInfoDialog(@NonNull Context context) {
        super(context);
        setCancelable(false);
        etPhone = findViewById(R.id.et_phone);
        etName = findViewById(R.id.et_name);
        btCommit = findViewById(R.id.bt_commit);

        etName.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                setCommitButtonEnable();

            }
        });
        etPhone.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                setCommitButtonEnable();

            }
        });

        rg = findViewById(R.id.rg);
        View ivClose = findViewById(R.id.iv_close);
        ivClose.setOnClickListener((view) -> {
            if (listener != null) {
                listener.itemSelect(null);
            }
            dismiss();
        });


        rg.setOnCheckedChangeListener((group, checkedId) -> {

            switch (checkedId) {
                case R.id.man:
                    gender = 1;
                    break;
                case R.id.female:
                    gender = 2;
                    break;
            }

        });

        btCommit.setOnClickListener((view) -> {

            name = etName.getText().toString().trim();
            phoneNumber = etPhone.getText().toString().trim();

            if (TextUtils.isEmpty(name)) {
                ToastUtils.showShort("怎么称呼您呢？");
                return;
            }

            if (TextUtils.isEmpty(phoneNumber)) {
                ToastUtils.showShort("请留下电话，以便回访哦！");
                return;
            }

            if (!(phoneNumber.matches("^0\\d{2,3}-?\\d{7,8}$")||phoneNumber.matches("^1[34578]\\d{9}$"))){
                ToastUtils.showShort("联系方式有误，请重填!");
                etPhone.setText("");
                return;

            }

            if (listener != null) {
                ItemInfo itemInfo = new ItemInfo();
                ExtraInfo extraInfo = new ExtraInfo();
                extraInfo.name = name;
                extraInfo.phoneNumber = phoneNumber;
                extraInfo.gender = gender;
                itemInfo.extraInfo = extraInfo;
                listener.itemSelect(itemInfo);
            }
            dismiss();

        });


    }

    public static class ExtraInfo {

        public String name;

        public String phoneNumber;

        public int gender;


    }

    @Override
    public int getLayoutId() {
        return R.layout.dialog_comment_extra_info;
    }

    @Override
    public boolean centerInWindow() {
        return true;
    }

    private void setCommitButtonEnable() {


        String phone = etPhone.getText().toString().trim();
        String name = etName.getText().toString().trim();

        if (!TextUtils.isEmpty(phone) && !TextUtils.isEmpty(name)) {
            btCommit.setEnabled(true);
        } else {
            btCommit.setEnabled(false);
        }


    }
}
