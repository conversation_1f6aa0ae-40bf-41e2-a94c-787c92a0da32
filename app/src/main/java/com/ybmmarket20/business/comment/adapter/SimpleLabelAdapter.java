package com.ybmmarket20.business.comment.adapter;

import android.content.Context;
import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;

import com.ybmmarket20.R;
import com.ybmmarket20.bean.CommentLabelBean;

import java.util.ArrayList;
import java.util.List;

public class SimpleLabelAdapter extends RecyclerView.Adapter<SimpleLabelAdapter.MyHolder> {


    private List<String> data;

    private Context context;

    public SimpleLabelAdapter(Context context, List<String> data) {
        this.context = context;
        this.data = data;
    }


    @Override
    public MyHolder onCreateViewHolder(ViewGroup parent, int viewType) {

        View view = LayoutInflater.from(context).inflate(R.layout.item_comment_label, parent, false);

        return new MyHolder(view);
    }

    @Override
    public void onBindViewHolder(MyHolder holder, int position) {
        holder.cb.setText(data.get(position));
    }


    @Override
    public int getItemCount() {
        return data.size();
    }

    public class MyHolder extends RecyclerView.ViewHolder {

        public CheckBox cb;

        public MyHolder(View itemView) {
            super(itemView);
            cb = itemView.findViewById(R.id.cb);
            cb.setEnabled(false);

        }

    }


}
