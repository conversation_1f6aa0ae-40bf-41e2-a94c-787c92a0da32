package com.ybmmarket20.business.comment.adapter;

import android.content.Context;
import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;

import com.ybmmarket20.R;
import com.ybmmarket20.bean.CommentLabelBean;

import java.util.ArrayList;
import java.util.List;

public class CommentDetailAdapter extends RecyclerView.Adapter<CommentDetailAdapter.MyHolder> {

    public interface CheckChangeListener{

        void onCheckChange(CommentLabelBean bean);

    }

    private CheckChangeListener listener;

    public void setListener(CheckChangeListener listener){
        this.listener=listener;
    }

    private List<CommentLabelBean> data = new ArrayList<>();

    private Context context;

    public CommentDetailAdapter(Context context) {
        this.context = context;
    }

    public void setData(List<CommentLabelBean> data) {
        this.data.clear();
        this.data.addAll(data);
        notifyDataSetChanged();
    }

    @Override
    public MyHolder onCreateViewHolder(ViewGroup parent, int viewType) {

        View view = LayoutInflater.from(context).inflate(R.layout.item_comment_detail, parent, false);

        return new MyHolder(view);
    }

    @Override
    public void onBindViewHolder(MyHolder holder, int position) {
        CommentLabelBean labelInfo = data.get(position);
    }


    @Override
    public int getItemCount() {
        return data.size();
    }

    public class MyHolder extends RecyclerView.ViewHolder {


        public MyHolder(View itemView) {
            super(itemView);

        }

    }




}
