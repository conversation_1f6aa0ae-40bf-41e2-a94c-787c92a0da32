package com.ybmmarket20.business.comment.adapter;

import android.content.Context;
import android.graphics.drawable.Drawable;
import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;

import com.ybmmarket20.R;
import com.ybmmarket20.bean.CommentLabelBean;
import com.ybmmarket20.utils.UiUtils;

import java.util.ArrayList;
import java.util.List;

public class LabelAdapter extends RecyclerView.Adapter<LabelAdapter.MyHolder> {

    public interface CheckChangeListener {

        void onCheckChange(CommentLabelBean bean);

    }

    private CheckChangeListener listener;

    public void setListener(CheckChangeListener listener) {
        this.listener = listener;
    }

    private List<CommentLabelBean> data = new ArrayList<>();

    private Context context;

    public LabelAdapter(Context context) {
        this.context = context;
    }

    public void setData(List<CommentLabelBean> data) {
        this.data.clear();
        this.data.addAll(data);
        notifyDataSetChanged();
    }


    public List<CommentLabelBean> getData() {
        return data;
    }

    @Override
    public MyHolder onCreateViewHolder(ViewGroup parent, int viewType) {

        View view = LayoutInflater.from(context).inflate(R.layout.item_comment_label, parent, false);

        return new MyHolder(view);
    }

    @Override
    public void onBindViewHolder(MyHolder holder, int position) {
        CommentLabelBean labelInfo = data.get(position);
        holder.bindData(labelInfo);
    }


    @Override
    public int getItemCount() {
        return data.size();
    }

    public class MyHolder extends RecyclerView.ViewHolder {

        private CheckBox cb;

        public MyHolder(View itemView) {
            super(itemView);
            cb = itemView.findViewById(R.id.cb);

        }

        public void bindData(CommentLabelBean info) {
            cb.setText(info.evaluateLabelName);
            cb.setChecked(info.isChecked);
            cb.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    info.isChecked = cb.isChecked();
                    if (listener != null) {
                        listener.onCheckChange(info);
                    }
                }
            });
            if (info.isEditLabel) {

                Drawable drawable = context.getResources().getDrawable(R.drawable.selector_comment_label_edit);
                drawable.setBounds(0,0,UiUtils.sp2px(8),UiUtils.sp2px(8));
                cb.setCompoundDrawablesWithIntrinsicBounds(drawable,null,null,null);


            }else {
                cb.setCompoundDrawables(null,null,null,null);
            }
        }
    }


}
