package com.ybmmarket20.business.comment.ui;

import android.content.Context;
import android.graphics.drawable.AnimationDrawable;
import androidx.annotation.NonNull;
import android.text.TextUtils;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import com.apkfuns.logutils.LogUtils;
import com.iflytek.cloud.ErrorCode;
import com.ybmmarket20.R;
import com.ybmmarket20.common.VoiceSearchManager;
import com.ybmmarket20.view.TransparentDialog;

public class CommentRecorderDialog extends TransparentDialog {

    private EditText editText;

    private VoiceSearchManager recorder;

    private ImageView iv;

    private TextView title;
    private Button btEnd;

    public CommentRecorderDialog(@NonNull Context context, EditText editText) {
        super(context);
        this.editText = editText;
        setCancelable(false);

        iv = findViewById(R.id.iv_loading);
        title = findViewById(R.id.title);
        recorder = VoiceSearchManager.getInstance();
        ImageView btClose = findViewById(R.id.bt_close);
        btClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                stopAnimation();
                dismiss();

            }
        });
        btEnd = findViewById(R.id.bt_end);
        btEnd.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (isEnd) {
                    isEnd = false;
                    recorder.startListeningForComment();
                    title.setText("说出你想要输入的内容");
                    btEnd.setText("说完了");
                    startAnimation();

                } else {
                    stopAnimation();
                    dismiss();
                }

            }
        });
    }

    public boolean isEnd;

    public void startAnimation() {
        AnimationDrawable animationDrawable = (AnimationDrawable) iv.getDrawable();
        animationDrawable.start();

    }

    public void stopAnimation() {
        AnimationDrawable animationDrawable = (AnimationDrawable) iv.getDrawable();
        animationDrawable.stop();

    }


    @Override
    public int getLayoutId() {
        return R.layout.dialog_comment_recorder;
    }

    @Override
    public boolean centerInWindow() {
        return false;
    }

    private String oldText;
    @Override
    public void show() {
        super.show();
        startAnimation();
        getOldText();
        recorder.startListeningForComment();
        if (recorder.getRet() != ErrorCode.SUCCESS) {
            LogUtils.i("recorder", "听写失败");
            title.setText("听写失败");
            btEnd.setText("再一次");
            getOldText();
            isEnd = true;
        }
        recorder.setOnDismissVoiceListener(new VoiceSearchManager.onDismissVoiceListener() {

            @Override
            public void DismissVoice() {
                // 取消语音
                LogUtils.i("recorder", "录音取消");
                title.setText("好像没听清你说的话");
                btEnd.setText("再一次");
                isEnd = true;
                getOldText();
                stopAnimation();
            }

            @Override
            public void onResult(String result, boolean isLast) {
                LogUtils.i("recorder", result);

                if (!TextUtils.isEmpty(result) && editText != null && !isLast) {
                    editText.setText(oldText+result);
                    editText.setSelection(editText.getText().length());

                }
            }

            @Override
            public void onErrorCode(String error) {

                if (ERRORCODE.equals(error)) {
                    //未检测到语音;
                    LogUtils.i("recorder", "未检测到语言");

                }
            }
        });
    }

    private void getOldText() {
        if (editText==null){
            return;
        }
        oldText=editText.getText().toString().trim();
        if (!oldText.isEmpty()){
            oldText+=",";
        }
    }

    private String ERRORCODE = "10118";

    @Override
    public void dismiss() {
        super.dismiss();
        stopAnimation();
        String s = editText.getText().toString();

        if (!s.endsWith("。")&&s.length()!=0) {
            editText.append("。");

        }
        editText = null;
        recorder.stopListening();

    }


}
