package com.ybmmarket20.business.comment.adapter;

import android.app.Activity;
import android.content.Intent;
import android.graphics.Bitmap;
import android.net.Uri;
import android.os.Bundle;
import androidx.recyclerview.widget.RecyclerView;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import com.apkfuns.logutils.LogUtils;
import com.ybm.app.bean.NetError;
import com.ybm.app.common.ImageLoader.ImageHelper;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.CommentUploadPicBean;
import com.ybmmarket20.business.comment.ui.ChoicePicDialog;
import com.ybmmarket20.business.comment.ui.CommentActivity;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.common.util.ToastUtils;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.utils.BitmapUtil;
import com.ybmmarket20.utils.FileUtil;
import com.ybmmarket20.utils.GalleryUtil;
import com.ybmmarket20.utils.SpUtil;
import com.ybmmarket20.view.ShowBigBitmapPopPublish;
import com.ybmmarket20.view.TransparentDialog;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

import static android.app.Activity.RESULT_OK;

public class PicAdapter extends RecyclerView.Adapter<PicAdapter.MyHolder> {
    public static final int CAMERA = 100;
    public static final int PICTURE = 200;
    private CommentActivity context;

    public List<PicInfo> picInfoList;
    public PicInfo addItem;

    public PicAdapter(CommentActivity context) {
        this.context = context;
        picInfoList = new ArrayList<>();
        addItem = new PicInfo();
        addItem.isAddItem = true;
        picInfoList.add(addItem);

    }

    public List<PicInfo> getPicInfoList() {
        return picInfoList;
    }

    @Override
    public PicAdapter.MyHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_comment_pic, parent, false);
        return new MyHolder(view);
    }

    @Override
    public void onBindViewHolder(PicAdapter.MyHolder holder, int position) {

        holder.bindData(picInfoList.get(position));
    }

    @Override
    public int getItemCount() {
        return picInfoList.size();
    }

    private ChoicePicDialog choiceDialog;

    public class MyHolder extends RecyclerView.ViewHolder {
        private ImageView ivPic;
        private ImageView ivDelete;

        public MyHolder(View itemView) {
            super(itemView);
            ivDelete = itemView.findViewById(R.id.iv_delete);
            ivDelete.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    int adapterPosition = getAdapterPosition();
                    PicAdapter.this.picInfoList.remove(adapterPosition);
                    if (!picInfoList.contains(addItem)) {
                        picInfoList.add(addItem);
                    }
                    notifyDataSetChanged();
                }
            });
            ivPic = itemView.findViewById(R.id.iv_pic);
            ivPic.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    int adapterPosition = getAdapterPosition();
                    PicInfo picInfo = PicAdapter.this.picInfoList.get(adapterPosition);
                    if (picInfo.isAddItem) {
                        context.setPicAdapter(PicAdapter.this);
                        choiceDialog = new ChoicePicDialog(context);

                        choiceDialog.setItemSelectListener(new TransparentDialog.ItemSelectListener() {
                            @Override
                            public void itemSelect(TransparentDialog.ItemInfo itemInfo) {
                                if (itemInfo.id == R.id.tv_take_photo) {
                                    //调用系统相机程序
                                    photoFile = getPhotoFile();
                                    FileUtil.startCameraForResult(context,photoFile.getAbsolutePath());

                                }

                                if (itemInfo.id == R.id.tv_pic_photo) {
                                    // 调用系统相册
                                    FileUtil.startPicturesForResult(context);
                                }

                            }
                        });
                        choiceDialog.show();
                    } else {

                        ShowBigBitmapPopPublish bigBitmapPop = new ShowBigBitmapPopPublish(picInfo.localUrl);
                        bigBitmapPop.show(ivPic);
                    }

                }
            });
        }

        public void bindData(PicInfo picInfo) {
            if (picInfo.isAddItem) {
                ivDelete.setVisibility(View.GONE);
                ivPic.setScaleType(ImageView.ScaleType.FIT_XY);
                ivPic.setImageResource(R.drawable.icon_comment_add_pic);
            } else {
                ivDelete.setVisibility(View.VISIBLE);
                ivPic.setScaleType(ImageView.ScaleType.CENTER_INSIDE);
                ImageHelper.with(context).load(new File(picInfo.localUrl)).skipMemoryCache(true).into(ivPic);
            }
        }


    }

    private File photoFile;

    public static class PicInfo {

        public String netUrl;

        public String localUrl;

        public boolean isAddItem;

    }


    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        if (resultCode == Activity.RESULT_CANCELED) {
            photoFile = null;
            return;
        }
        String path = context.getCacheDir().getAbsolutePath() + "/ybm_" + System.currentTimeMillis() + ".png";
        if (requestCode == CAMERA) {
            //来自于相机返回的结果
            File file = null;
            if (data == null) {//设置的图片存储的uri
                file = photoFile;
                if (file != null && file.exists()) {
                    if (BitmapUtil.compressFile(file.getAbsolutePath(), path)) {
                        file = new File(path);
                    }
                }
            } else {//没有设置图片的uri
                Bundle bundle = data.getExtras();
                if (bundle != null) {
                    Bitmap bitmap = (Bitmap) bundle.get("data");
                    file = BitmapUtil.bitmapToFile(bitmap, path);
                } else {//android 5.0 系统
                    if (data.getData() != null && !TextUtils.isEmpty(data.getData().getEncodedPath())) {
                        if (BitmapUtil.compressFile(data.getData().getEncodedPath(), path)) {
                            file = new File(path);
                        }
                    }
                }
            }
            if (file == null || !file.exists()) {
                path = null;
                ToastUtils.showShort("没有找到图片");
                return;
            }
        } else if (requestCode == PICTURE && resultCode == RESULT_OK && data != null) {
            //来自于图库返回的结果
            Uri selectedImage = data.getData();
            String pathResult = GalleryUtil.getFilePathByUri(context, selectedImage);
            if (TextUtils.isEmpty(pathResult) || !BitmapUtil.compressFile(pathResult, path)) {
                path = null;
                ToastUtils.showShort("没有找到图片");
                return;
            }
        }
        PicInfo picInfo = new PicInfo();
        picInfo.localUrl = path;
        if (picInfoList.size() == 4) {
            //认为已经添加了三张图片
            picInfoList.remove(3);
            picInfoList.add(picInfo);
            notifyItemChanged(3);
        } else {
            picInfoList.add(picInfoList.size() - 1, picInfo);
            notifyItemInserted(picInfoList.indexOf(picInfo));
        }
        uploadFile(picInfo);


    }

    private static final int MAX_SIZE = 4;

    //生成相机来的图片
    public File getPhotoFile() {

        File file = new File(context.getExternalCacheDir().getAbsolutePath(), "xyy.jpg");
        if (file.exists()) {
            try {
                file.delete();
                file.createNewFile();
            } catch (Exception e) {
                LogUtils.d(e);
            }
        }
        LogUtils.d(file.getAbsolutePath());
        return file;
    }

    public void uploadFile(PicInfo picInfo) {
        File file = new File(picInfo.localUrl);
        if (file.exists() && file.length() > 0) {
            RequestParams params = new RequestParams();
            final String fileName = picInfo.localUrl.substring(picInfo.localUrl.lastIndexOf("/") + 1, picInfo.localUrl.lastIndexOf("."));
            params.put("targetFileName", fileName);
            params.put("file", file);
            params.put("merchantId", SpUtil.getMerchantid());
            HttpManager.getInstance().post(AppNetConfig.UPLOAD_COMMENT_PICTURE, params, new BaseResponse<CommentUploadPicBean>() {

                @Override
                public void onSuccess(String content, BaseBean<CommentUploadPicBean> data, CommentUploadPicBean obj) {
                    if (data != null && data.isSuccess()) {
                        LogUtils.d("上传完成，开始更新");
                        picInfo.netUrl = obj.fileUrl;
                    } else {
                        ToastUtils.showShort("上传失败");
                    }
                }

                @Override
                public void onFailure(NetError error) {
                    super.onFailure(error);
                    ToastUtils.showShort("图片添加失败");
                    int i = picInfoList.indexOf(picInfo);
                    picInfoList.remove(i);
                    if (!picInfoList.contains(addItem)) {
                        picInfoList.add(addItem);
                    }
                    notifyDataSetChanged();


                }
            });

        } else {
            ToastUtils.showShort("文件不存在");
        }
    }
}
