package com.ybmmarket20.business.comment.adapter;

import android.content.Context;
import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import com.ybm.app.common.ImageLoader.ImageHelper;
import com.ybmmarket20.R;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.view.ShowBigBitmapPopPublish;

import java.util.List;

public class SimplePicAdapter extends RecyclerView.Adapter<SimplePicAdapter.MyHolder> {


    public List<String> picInfoList;

    private Context context;

    public SimplePicAdapter(Context context, List<String> picInfoList) {
        this.picInfoList = picInfoList;
        this.context=context;

    }


    @Override
    public SimplePicAdapter.MyHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_comment_pic, parent, false);
        return new MyHolder(view);
    }

    @Override
    public void onBindViewHolder(SimplePicAdapter.MyHolder holder, int position) {
        ImageHelper.with(context).load( AppNetConfig.getCDNHost()+picInfoList.get(position)).skipMemoryCache(true).placeholder(R.drawable.icon_comment_place_holder).into(holder.ivPic);
        holder.ivPic.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                ShowBigBitmapPopPublish bigBitmapPop = new ShowBigBitmapPopPublish(AppNetConfig.getCDNHost()+picInfoList.get(position));
                bigBitmapPop.show(holder.ivPic);
            }
        });

    }

    @Override
    public int getItemCount() {
        return picInfoList.size();
    }


    public class MyHolder extends RecyclerView.ViewHolder {
        public ImageView ivPic;
        private ImageView ivDelete;

        public MyHolder(View itemView) {
            super(itemView);
            ivDelete = itemView.findViewById(R.id.iv_delete);
            ivDelete.setVisibility(View.GONE);
            ivPic=itemView.findViewById(R.id.iv_pic);


        }

    }

}
