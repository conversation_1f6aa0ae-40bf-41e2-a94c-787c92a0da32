package com.ybmmarket20.business.shop.ui

import android.annotation.SuppressLint
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Color
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.TextUtils
import android.text.style.ForegroundColorSpan
import android.view.View
import android.widget.TextView
import androidx.arch.core.executor.ArchTaskExecutor
import androidx.fragment.app.FragmentPagerAdapter
import androidx.fragment.app.viewModels
import com.analysys.ANSAutoPageTracker
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.flyco.tablayout.listener.OnTabSelectListener
import com.ybm.app.common.ImageLoader.ImageHelper
import com.ybmmarket20.R
import com.ybmmarket20.bean.ShopTab
import com.ybmmarket20.bean.TagBean
import com.ybmmarket20.business.shop.adapter.ShopAllAdapter
import com.ybmmarket20.business.shop.ui.ShopAllActvity.Companion.jgBtnClickTrack
import com.ybmmarket20.common.BaseFragment
import com.ybmmarket20.common.JGTrackManager
import com.ybmmarket20.common.JgTrackBean
import com.ybmmarket20.common.RequestParams
import com.ybmmarket20.common.getFullClassName
import com.ybmmarket20.common.util.Abase
import com.ybmmarket20.constant.IntentCanst
import com.ybmmarket20.fragments.ShopSwitchFragment
import com.ybmmarket20.fragments.SwitchState
import com.ybmmarket20.utils.ImageUtil.Companion.load
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.utils.ShareUtil
import com.ybmmarket20.utils.SpUtil
import com.ybmmarket20.utils.UiUtils
import com.ybmmarket20.utils.analysis.XyyIoUtil
import com.ybmmarket20.view.ShopSwitchPopupWindow
import com.ybmmarket20.viewmodel.ShopHomeViewModel
import kotlinx.android.synthetic.main.fragment_shop_all.iv_link_shop_user
import kotlinx.android.synthetic.main.fragment_shop_all.iv_share_shop
import kotlinx.android.synthetic.main.fragment_shop_all.iv_shop_icon
import kotlinx.android.synthetic.main.fragment_shop_all.snwtv_shop_tag1
import kotlinx.android.synthetic.main.fragment_shop_all.space_shop_tag
import kotlinx.android.synthetic.main.fragment_shop_all.sv_shop_tag1
import kotlinx.android.synthetic.main.fragment_shop_all.tabLayout
import kotlinx.android.synthetic.main.fragment_shop_all.tv_on_line
import kotlinx.android.synthetic.main.fragment_shop_all.tv_qualification_afterSale
import kotlinx.android.synthetic.main.fragment_shop_all.tv_shop_name
import kotlinx.android.synthetic.main.fragment_shop_all.tv_shop_tag2
import kotlinx.android.synthetic.main.fragment_shop_all.tv_shop_tips
import kotlinx.android.synthetic.main.fragment_shop_all.vp
import org.json.JSONObject

/**
 * 店铺
 */
class ShopAllFragment: BaseFragment(), ANSAutoPageTracker {

    var shopPatternCode: String? = null

    private var shopCode: String? = null
    private var orgId: String? = null
    private var shopName: String? = null
    private var vpAdapter: ShopAllAdapter? = null
    private var ordIdShopCode: String? = null
    private var mAnchorCsuId: String? = null
    private var mEntryShopAction: String? = null
    private var mEntrance = ""
    private var jgReferrerTitle = ""
    private var jgReferrer = ""
    private val mViewModel: ShopHomeViewModel by viewModels()
    //店铺接口返回的标签数据回调
    private var mShopTabsCallback: ((tabList: List<ShopTab>?, shopCode: String?, orgId: String?)->Unit)? = null
    //pop店铺shopCode回调（用于搜索）
    private var mPopShopCodeCallback: ((shopCode: String?) -> Unit)? = null
    private var source:String? = "0" //1表示来自购物车  后端下发路由跳过来
    private var isFilterUnableAddCart:String? = "0" //1表示来自购物车  后端下发路由跳过来

    @SuppressLint("RestrictedApi")
    override fun initData(content: String?) {
        val intent = activity?.intent
        orgId = arguments?.getString("orgId")
        shopCode = arguments?.getString("shopCode")
        source = arguments?.getString("source")
        isFilterUnableAddCart = arguments?.getString("isFilterUnableAddCart")
        mAnchorCsuId = intent?.getStringExtra("anchorCsuId")
        mEntryShopAction = intent?.getStringExtra("isActionProduct")
        jgReferrerTitle = intent?.getStringExtra(IntentCanst.JG_REFERRER_TITLE)?:""
        jgReferrer = intent?.getStringExtra(IntentCanst.JG_REFERRER)?:""
        mEntrance = intent?.getStringExtra(IntentCanst.JG_ENTRANCE)?:""
        mEntrance = if (mEntrance.isEmpty())
            shopCode?.let { "${JGTrackManager.TrackShopMain.TITLE}($it)" }?: JGTrackManager.TrackShopMain.TITLE
        else
            shopCode?.let { "${mEntrance}-${JGTrackManager.TrackShopMain.TITLE}($it)" }?:"${mEntrance}-${JGTrackManager.TrackShopMain.TITLE}"

        mViewModel.shopInfoPopLiveData.observe(this) { bean ->
            mShopTabsCallback?.invoke(bean.shopTabList, null, orgId)
            ordIdShopCode = bean.shopCode
            mPopShopCodeCallback?.invoke(ordIdShopCode)
            activity?.let { load(it, bean.orgLogo, iv_shop_icon) }
            shopPatternCode = bean.shopPatternCode
            handleBtn(shopPatternCode)
            shopName = bean.orgName
            vpAdapter?.setShopName(shopName)
            showHome()
            handleTags(bean.shopPropTags, bean.shopServiceQualityTags)
            tv_shop_name?.text = bean.orgName
            tv_shop_tips?.visibility =
                if (bean?.freightInfo.isNullOrBlank() && bean.shelvesDesc.isNullOrEmpty()) View.GONE else View.VISIBLE
            if (!bean.shelvesDesc.isNullOrEmpty()) {
                tv_shop_tips?.text = getActivityStr(bean.shelvesDesc, bean.shelves.toString())
                tv_shop_tips?.append(", ")
                tv_shop_tips?.append(getActivityStr(bean.salesVolumeDesc, bean.salesVolume ?: ""))
                tv_shop_tips?.append("; ")
            }
            if (!bean.freightInfo.isNullOrEmpty()) {
                tv_shop_tips?.append(bean.freightInfo ?: "")
            }
            iv_share_shop?.setOnClickListener {
                ArchTaskExecutor.getInstance().executeOnDiskIO {
                    try {
                        val bitmap: Bitmap =
                            ImageHelper.with(activity).load(bean.orgLogo).asBitmap()
                                .diskCacheStrategy(
                                    DiskCacheStrategy.SOURCE
                                ).into(100, 100).get()
                        ShareUtil.shareWXPage(
                            0,
                            bean.orgName,
                            bean.shareLink,
                            bean.shareDesc,
                            bitmap
                        )
                    } catch (e: Exception) {
                        e.printStackTrace()
                        val bitmap = BitmapFactory.decodeResource(
                            Abase.getResources(),
                            R.drawable.aptitude04
                        )
                        ShareUtil.shareWXPage(
                            0,
                            bean.orgName,
                            bean.shareLink,
                            bean.shareDesc,
                            bitmap
                        )
                    }
                }
                activity?.let { it1 -> jgBtnClickTrack(it1, "顶部功能", "分享") }
            }
            tv_qualification_afterSale.setOnClickListener {
                var isSelf = 0
                val tShopCode = if (shopCode != null) {
                    //自营
                    isSelf = 1
                    shopCode ?: ""
                } else orgId ?: ""
                RoutersUtils.open("ybmpage://qualificationandaftersale?shopCode=$tShopCode&isSelf=$isSelf")
                XyyIoUtil.track(
                    "shopHome_licenseAfterSale_Click",
                    hashMapOf("shop_code" to (shopCode ?: orgId))
                )
                activity?.let { it1 -> jgBtnClickTrack(it1, "顶部功能", "资质/配送") }
            }
            initTabView()
        }

        orgId?.let {
            mViewModel.onLineMessageLiveData.observe(this) { t ->
                tv_on_line?.visibility =
                    if (t?.IM_PACK_URL?.isBlank() == true) View.GONE else View.VISIBLE
                tv_on_line?.setOnClickListener {
                    //LogUtils.e("xyd pop imurl = ${RoutersUtils.getRouterPopCustomerServiceUrl(t.IM_PACK_URL, orgId, "", shopName)}")
                    RoutersUtils.open(
                        RoutersUtils.getRouterPopCustomerServiceUrl(
                            t.IM_PACK_URL,
                            orgId,
                            "",
                            shopName
                        )
                    )
                    XyyIoUtil.track("shopHome_customerService", hashMapOf("shop_code" to orgId))
                    activity?.let { it1 -> jgBtnClickTrack(it1, "顶部功能", "在线客服") }
                }
            }
        }

        mViewModel.shopInfoSelfLiveData.observe(this) { bean ->
            activity?.let { load(it, bean.shopLogoUrl, iv_shop_icon) }
            shopPatternCode = bean.shopPatternCode
            handleBtn(shopPatternCode)
            shopName = bean.shopName
            vpAdapter?.setShopName(bean.shopName)
            showHome()
            handleTags(bean.shopPropTags, bean.shopServiceQualityTags)
            tv_shop_name?.text = bean.shopName
            tv_shop_tips?.visibility =
                if (bean?.freightTips.isNullOrBlank() && bean.shelvesDesc.isNullOrEmpty()) View.GONE else View.VISIBLE
            if (!bean.shelvesDesc.isNullOrEmpty()) {
                tv_shop_tips?.text = getActivityStr(bean.shelvesDesc, bean.shelves.toString())
                tv_shop_tips?.append(", ")
                tv_shop_tips?.append(getActivityStr(bean.salesVolumeDesc, bean.salesVolume ?: ""))
                tv_shop_tips?.append("; ")
            }
            if (!bean.freightTips.isNullOrEmpty()) {
                tv_shop_tips?.append(bean.freightTips ?: "")
            }
            iv_share_shop?.setOnClickListener {
                ArchTaskExecutor.getInstance().executeOnDiskIO {
                    try {
                        val bitmap: Bitmap =
                            ImageHelper.with(activity).load(bean.shopLogoUrl).asBitmap()
                                .diskCacheStrategy(
                                    DiskCacheStrategy.SOURCE
                                ).into(100, 100).get()
                        ShareUtil.shareWXPage(
                            0,
                            bean.shopName,
                            bean.shareLink,
                            bean.shareDesc,
                            bitmap
                        )
                    } catch (e: Exception) {
                        e.printStackTrace()
                        val bitmap = BitmapFactory.decodeResource(
                            Abase.getResources(),
                            R.drawable.aptitude04
                        )
                        ShareUtil.shareWXPage(
                            0,
                            bean.shopName,
                            bean.shareLink,
                            bean.shareDesc,
                            bitmap
                        )
                    }
                }
                activity?.let { it1 -> jgBtnClickTrack(it1, "顶部功能", "分享") }
            }
            tv_on_line.visibility =
                if (TextUtils.isEmpty(bean.iMPackUrl)) View.GONE else View.VISIBLE
            iv_link_shop_user?.setOnClickListener { RoutersUtils.open(bean.iMPackUrl) }
            tv_on_line?.setOnClickListener {
                RoutersUtils.open(bean.iMPackUrl)
                activity?.let { it1 -> jgBtnClickTrack(it1, "顶部功能", "在线客服") }
            }
            tv_qualification_afterSale.setOnClickListener {
                var isSelf = 0
                val tShopCode = if (shopCode != null) {
                    //自营
                    isSelf = 1
                    shopCode ?: ""
                } else orgId ?: ""
                RoutersUtils.open("ybmpage://qualificationandaftersale?shopCode=$tShopCode&isSelf=$isSelf")
                XyyIoUtil.track(
                    "shopHome_licenseAfterSale_Click",
                    hashMapOf("shop_code" to (shopCode ?: orgId))
                )
                activity?.let { it1 -> jgBtnClickTrack(it1, "顶部功能", "资质/配送") }
            }
            initTabView()
        }
        orgId?.let { mViewModel.getShopInfoPop(SpUtil.getMerchantid(), orgId!!) }
        shopCode?.let { mViewModel.getSelfShopInfo(SpUtil.getMerchantid(), shopCode!!) }
    }

    override fun initTitle() {

    }

    override fun getParams(): RequestParams = RequestParams()

    override fun getUrl(): String = ""

    override fun getLayoutId(): Int = R.layout.fragment_shop_all

    private fun handleBtn(shopPatternCode: String?) {
        if (shopPatternCode == "virtual") {
            tv_qualification_afterSale.visibility = View.GONE
        }
    }

    private fun handleTags(shopPropTags: List<TagBean>?, shopServiceQualityTags: List<TagBean>?) {
        var isShopTags1 = false
        var isShopTags2 = false
        shopPropTags?.apply {
            if (isNotEmpty()) {
                isShopTags1 = true
                snwtv_shop_tag1.bindData(shopPropTags, "", Integer.MAX_VALUE)
                sv_shop_tag1.visibility = View.VISIBLE
            }
        }
        shopServiceQualityTags?.apply {
            if (isNotEmpty()) {
                isShopTags2 = true
                tv_shop_tag2.visibility = View.VISIBLE
                val tagStrBuilder = SpannableStringBuilder("")
                forEachIndexed {index, tagBean ->
                    tagStrBuilder.append(tagBean.text)
                    if (index != size - 1) {
                        val symbolBuilder = SpannableStringBuilder("|");
                        symbolBuilder.setSpan(ForegroundColorSpan(Color.parseColor("#EEEEEE")), 0, 1, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
                        tagStrBuilder.append(" ")
                            .append(symbolBuilder)
                            .append(" ")
                    }
                }
                tv_shop_tag2.text = tagStrBuilder

            }
        }
        space_shop_tag.visibility = if (isShopTags1 || isShopTags2) View.VISIBLE
        else View.GONE
    }

    private fun initTabView() {
        tabLayout.setViewPager(vp.apply {
            offscreenPageLimit = 4 //预加载页数 现在有3个fragment 3+1  防止fragment not attached to activity 重新加载情况
            vpAdapter = ShopAllAdapter(childFragmentManager, orgId, shopCode, shopPatternCode,source,isFilterUnableAddCart ,mFlowData, shopName?: "", mAnchorCsuId,
                JgTrackBean(
                    jgReferrer = activity?.getFullClassName()?: "",
                    jgReferrerTitle = JGTrackManager.TrackShopMain.TITLE,
                    jgReferrerModule = JGTrackManager.TrackShopMain.TITLE,
                    module = JGTrackManager.Common.MODULE_PRODUCT_LIST,
                    pageId = JGTrackManager.TrackShopMain.PAGE_ID,
                    title = JGTrackManager.TrackShopMain.TITLE,
                    entrance = mEntrance
                )
            )
            adapter = vpAdapter
        })
        tabLayout?.setOnTabSelectListener(object : OnTabSelectListener {
            override fun onTabSelect(position: Int) {
                val obj = JSONObject().apply {
                    put("title", vpAdapter?.titles?.get(position) ?: "")
                    put("text", shopName)
                    put("shop_code", shopCode?: orgId)
                }
                XyyIoUtil.track(XyyIoUtil.SHOPHOME_MENU_CLICK, obj)

                activity?.let { jgBtnClickTrack(it,"导航",vpAdapter?.titles?.get(position)?.let { "导航-$it" } ?: "") }
            }

            override fun onTabReselect(position: Int) {
            }
        })
        initSwitchFragment()

        try {
            //从凑单页过来显示商品tab
            if (mEntryShopAction == ENTRY_SHOP_ACTION_PRODUCT) {
                tabLayout.currentTab = 1
            }
            if (!mAnchorCsuId.isNullOrEmpty()) {
                tabLayout.currentTab = 1
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }

//        setUnSelectArrow()
    }

    private fun showHome() {
//        val obj = JSONObject().apply {
//            put("title", vpAdapter?.titles?.get(0) ?: "")
//            put("text", shopName)
//            put("shop_code", shopCode?: orgId)
//        }
//        XyyIoUtil.track(XyyIoUtil.SHOPHOME_MENU_CLICK, obj)
    }

    private fun initSwitchFragment() {
        try {
            val fragment = (vp.adapter as FragmentPagerAdapter).getItem(2)
            val switchFragment: ShopSwitchFragment
            if (fragment is ShopSwitchFragment) {
                switchFragment = fragment
            } else return
            shopCode?.let { switchFragment.initSwitchState(SwitchState.SHOP_QUALIFACTION_SELF(it)) }
            orgId?.let { switchFragment.initSwitchState(SwitchState.SHOP_QUALIFACTION_POP(it)) }
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
        }
    }

    var mPw: ShopSwitchPopupWindow? = null

    private fun getActivityStr(shelvesDesc: String?, toString: String): CharSequence {
        val activityStr = SpannableStringBuilder()
        try {
            activityStr.append(shelvesDesc?.replace("xxx", toString, true) ?: "")
            val startIndex = shelvesDesc?.indexOf("xxx") ?: 0
            val endIndex = startIndex + toString.length
            activityStr.setSpan(ForegroundColorSpan(UiUtils.getColor(R.color.text_00B377)), startIndex, endIndex, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return activityStr
    }

    private fun getSwitchTabTextView(): TextView? = tabLayout?.getTitleView(2)

    private fun changeOtherPage(position: Int) {
        val fragment = (vp.adapter as FragmentPagerAdapter).getItem(2)
        when {
            shopCode != null -> {
                ShopType.SelfShop(shopCode!!, position)
            }
            orgId != null -> {
                ShopType.PopShop(orgId!!, position)
            }
            else -> null
        }?.let {
            var switchFragment: ShopSwitchFragment? = null
            if (fragment is ShopSwitchFragment) {
                switchFragment = fragment
            }
            switchFragment?.switchFragment(it.getState())
        }
    }

    /**
     * 监听店铺tab
     */
    fun setOnTabListCallback(callback: ((tabList: List<ShopTab>?, shopCode: String?, orgId: String?)->Unit)?) {
        mShopTabsCallback = callback
    }

    /**
     * 监听pop的shopcode
     */
    fun setOnPopShopCodeCallback(callback: ((shopCode: String?) -> Unit)?) {
        mPopShopCodeCallback = callback
    }

    //因为这里需要取值，所以暂时先不动
    override fun registerPageProperties(): MutableMap<String, Any> {
        val properties: MutableMap<String, Any> = HashMap()
        properties[JGTrackManager.FIELD.FIELD_PAGE_ID] = JGTrackManager.TrackShopMain.PAGE_ID
        properties[JGTrackManager.FIELD.FIELD_TITLE] = JGTrackManager.TrackShopMain.TITLE
        return properties
    }

    override fun registerPageUrl(): String = "com.ybmmarket20.business.shop.ui.ShopAllActvity"
}

sealed class ShopType {

    abstract fun getState(): SwitchState

    class SelfShop(val shopCode: String, val position: Int) : ShopType() {

        override fun getState(): SwitchState = when (position) {
            0 -> SwitchState.SHOP_QUALIFACTION_SELF(shopCode)
            1 -> SwitchState.SHOP_AFTER_SALE_DISTRIBUTION_SELF(shopCode)
            else -> SwitchState.SHOP_QUALIFACTION_SELF(shopCode)
        }
    }

    class PopShop(val orgId: String, val position: Int) : ShopType() {

        override fun getState(): SwitchState = when (position) {
            0 -> SwitchState.SHOP_QUALIFACTION_POP(orgId)
            1 -> SwitchState.SHOP_OPEN_ACCOUNT(orgId)
            2 -> SwitchState.SHOP_AFTER_SALE_DISTRIBUTION_POP(orgId)
            else -> SwitchState.SHOP_QUALIFACTION_POP(orgId)
        }
    }
}