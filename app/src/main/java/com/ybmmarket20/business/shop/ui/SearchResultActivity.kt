package com.ybmmarket20.business.shop.ui

import android.content.Intent
import android.graphics.drawable.GradientDrawable
import android.text.InputType
import android.text.TextUtils
import android.view.Gravity
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.EditorInfo
import android.view.inputmethod.InputMethodManager
import android.widget.LinearLayout
import android.widget.PopupWindow
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.core.widget.doAfterTextChanged
import androidx.lifecycle.ViewModelProvider
import com.analysys.ANSAutoPageTracker
import com.chad.library.adapter.base.BaseQuickAdapter
import com.github.mzule.activityrouter.annotation.Router
import com.luck.picture.lib.tools.StringUtils
import com.ybm.app.view.WrapGridLayoutManager
import com.ybmmarket20.R
import com.ybmmarket20.activity.BaseProductActivity
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.EmptyBean
import com.ybmmarket20.bean.HistoryKeyWord
import com.ybmmarket20.bean.RecommendKeyWord
import com.ybmmarket20.bean.StartWord
import com.ybmmarket20.common.BaseResponse
import com.ybmmarket20.common.JGTrackManager
import com.ybmmarket20.common.RequestParams
import com.ybmmarket20.common.getFullClassName
import com.ybmmarket20.common.splicingPageTitle2Entrance
import com.ybmmarket20.common.util.ToastUtils
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.constant.IntentCanst
import com.ybmmarket20.network.HttpManager
import com.ybmmarket20.search.SearchRecommendAdapter
import com.ybmmarket20.search.SearchRecommendItemDecoration
import com.ybmmarket20.search.SuggestNewPopWindowNew
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.utils.UiUtils
import com.ybmmarket20.utils.analysis.XyyIoUtil
import com.ybmmarket20.view.CommonSearchView
import com.ybmmarket20.viewmodel.SearchDataViewModel
import kotlinx.android.synthetic.main.activity_search_result.common_search_view
import kotlinx.android.synthetic.main.activity_search_result.flex_box_history
import kotlinx.android.synthetic.main.activity_search_result.ll_history
import kotlinx.android.synthetic.main.activity_search_result.ll_pre
import kotlinx.android.synthetic.main.activity_search_result.ll_recommend
import kotlinx.android.synthetic.main.activity_search_result.placeHolder
import kotlinx.android.synthetic.main.activity_search_result.rv_recommed
import kotlinx.android.synthetic.main.activity_search_result.tv_history_clear

@Router("shop_search_result")
class SearchResultActivity : BaseProductActivity(),ANSAutoPageTracker {

    private var orgId: String? = null
    private var shopCode: String? = null
    private var isThirdCompany: String? = null

    private lateinit var shopGoodsFragment: ShopGoodsFragment
    private var searchKey: String? = null
    private var scanShopCode: String? = null
    private val historyList = mutableListOf<HistoryKeyWord>() //历史搜索
    private var isSuggest = true
    private var orgIdShopCode: String? = null
    private var mViewMode: SearchDataViewModel? = null
    private var preSearchKey: String? = ""
    private var entrance: String? = ""


    override fun initData() {
        super.initData()
        initHistory()
        orgId = intent.getStringExtra("orgId")
        if (TextUtils.isEmpty(orgId)) {
            isThirdCompany = "0"
        } else {
            isThirdCompany = "1"
        }
        shopCode = intent.getStringExtra("shopCode")
        searchKey = intent.getStringExtra("searchKey")
        scanShopCode = intent.getStringExtra("scanShopCode")
        orgIdShopCode = intent.getStringExtra("orgId_shopCode")
        entrance = intent.getStringExtra(IntentCanst.JG_ENTRANCE)
        if (entrance.isNullOrEmpty()){
            entrance = JGTrackManager.TrackShopSearch.TITLE
        }else{
            entrance = splicingPageTitle2Entrance(entrance?:"",JGTrackManager.TrackShopSearch.TITLE)
        }
        shopGoodsFragment = ShopGoodsFragment.getInstance(orgId, shopCode, searchKey, isThirdCompany, orgIdShopCode,entrance)
        supportFragmentManager.beginTransaction().add(R.id.placeHolder, shopGoodsFragment).commitAllowingStateLoss()

        // 初始化搜索头相关
        common_search_view?.apply {

            entry = "search_shop_result"
            shopCode?.let { openNextPageUrl = "ybmpage://shop_search_result?shopCode=${shopCode}&&searchKey=" }
            orgId?.let { openNextPageUrl = "ybmpage://shop_search_result?orgId=${orgId}&&searchKey=" }
            searchKey?.let { tvTitleEdit.setText(searchKey) }
            tvTitleEdit.setOnEditorActionListener { _, actionId, _ ->
                if (actionId == EditorInfo.IME_ACTION_SEARCH) {
                    searchKey = tvTitleEdit.text?.toString()?.trim()
                    if (!searchKey.isNullOrEmpty()) {
                        (getSystemService(INPUT_METHOD_SERVICE) as InputMethodManager).hideSoftInputFromWindow(
                            <EMAIL>?.windowToken, InputMethodManager.HIDE_NOT_ALWAYS
                        )
                        shopGoodsFragment.startSearch(searchKey)
                        getHotSearch()
                        switchPage(false)
                        XyyIoUtil.trackSug(hashMapOf(
                            "spid" to "2",
                            "wq" to searchKey,
                            "keyword" to searchKey,
                            "pkw" to (preSearchKey?: "")
                        ), "2")
                        preSearchKey = searchKey
                    } else {
                        ToastUtils.showShort("请输入关键字！")
                    }
                }
                XyyIoUtil.trackSug(hashMapOf(
                    "spid" to "2",
                    "wq" to searchKey,
                    "keyword" to searchKey,
                    "pkw" to (preSearchKey?: "")
                ), "2")
                preSearchKey = searchKey
                suggestPopWindow?.dismiss()
                false
            }
            tvTitleEdit.doAfterTextChanged { shopGoodsFragment.setSearchKeyword(tvTitleEdit.text?.trim()?.toString() ?: "") }

            tvTitleEdit.setOnClickListener {
                searchKey = tvTitleEdit.text?.toString()?.trim()
                if (searchKey?.isEmpty() == false) {
                    shopGoodsFragment.startSearch(searchKey)
                    switchPage(false)
                }
            }
            ivScan.setOnClickListener(this)
            ivVoice.setOnClickListener(this)
            // 搜索下拉弹框初始化
            initSearchDropdown(this)

            ivClearEdit.setOnClickListener {
                tvTitleEdit.setText("")
                getHotSearch()
            }
            tvCancel.setOnClickListener {
                switchPage(false)
            }
        }
        setRecommend()
        mViewMode = ViewModelProvider(this).get(SearchDataViewModel::class.java)
        mViewMode?.searchStartLiveData?.observe(this, androidx.lifecycle.Observer {
            getHotSearch()
        })
    }

    private var searchRecommendAdapter: SearchRecommendAdapter? = null
    private val recommendList = mutableListOf<RecommendKeyWord>() //推荐搜索

    fun setRecommend() {
        // 2. 推荐搜索
        searchRecommendAdapter =
            SearchRecommendAdapter(R.layout.item_search_recommend_word, recommendList)
        searchRecommendAdapter!!.onItemClickListener =
            BaseQuickAdapter.OnItemClickListener { _: BaseQuickAdapter<*, *>?, _: View?, i: Int ->
                if (!TextUtils.isEmpty(recommendList[i].androidUrl)) {
                    RoutersUtils.open(recommendList[i].androidUrl)
                } else {
                    searchKey = recommendList[i].keyword
                    shopGoodsFragment.startSearch(searchKey)
                    switchPage(false)
                }
            }
        rv_recommed.layoutManager = WrapGridLayoutManager(this, 2)
        rv_recommed.addItemDecoration(SearchRecommendItemDecoration())
        rv_recommed.adapter = searchRecommendAdapter
    }

    fun getHotSearch(showHistory: Boolean = false) {
        showProgress()
        val params = RequestParams()
        params.put("merchantId", merchant_id)
        params.put("type", "2") //店铺搜索
        if (orgId == null) {
            params.put("shopCode", shopCode)
        } else {
            params.put("shopCode", orgIdShopCode)
        }
        HttpManager.getInstance()
            .post(AppNetConfig.START_WORD_LIST, params, object : BaseResponse<StartWord?>() {

                override fun onSuccess(
                    content: String?,
                    obj: BaseBean<StartWord?>?,
                    t: StartWord?
                ) {
                    super.onSuccess(content, obj, t)
                    dismissProgress()
                    if (obj != null) {
                        if (obj.isSuccess) {
                            t?.let(this@SearchResultActivity::setHotSearch)
                            //切换启动页和搜索结果，无历史和推荐则不显示启动页
                            switchPage(showHistory && (t?.historyList?.isNotEmpty() == true || t?.keywordList?.isNotEmpty() == true))
                            // 历史无数据则不显示
                            if (t?.historyList?.isNotEmpty() == true) {
                                ll_history.visibility = View.VISIBLE
                            } else {
                                ll_history.visibility = View.GONE
                            }
                            //推荐无数据则不显示
                            if (t?.keywordList?.isNotEmpty() == true) {
                                ll_recommend.visibility = View.VISIBLE
                            } else {
                                ll_recommend.visibility = View.GONE
                            }
                        }
                    }
                }
            })
    }

    /**
     * 设置热搜
     */
    private fun setHotSearch(startWord: StartWord?) {
        if (startWord == null) return
        if (ll_pre == null) return
        historyList.clear()
        historyList.addAll(startWord.historyList)
        switchPage(startWord.historyList.size != 0)
        addHistoryTag(historyList)

        recommendList.clear()
        if (startWord.keywordList != null) {
            recommendList.addAll(startWord.keywordList)
        }
        searchRecommendAdapter!!.setNewData(recommendList)
    }

    /**
     * 搜索启动页 - 初始化历史搜索数据
     *
     * @param history
     */
    private fun addHistoryTag(history: List<HistoryKeyWord>) {
        flex_box_history.maxLines = 2
        flex_box_history.clear()
        for (i in history.indices) {
            createHistoryTag(history[i], i).let(flex_box_history::addTagView)
        }
    }

    private fun createHistoryTag(history: HistoryKeyWord, i: Int): TextView {
        val historyTag = TextView(this)
        historyTag.textSize = 13f
        historyTag.setTextColor(UiUtils.getColor(R.color.color_292933))
        val shapeDrawable = GradientDrawable()
        shapeDrawable.setColor(UiUtils.getColor(R.color.color_f7f7f8))
        shapeDrawable.cornerRadius = UiUtils.dp2px(2).toFloat()
        historyTag.background = shapeDrawable
        historyTag.maxLines = 1
        if (history.historyType == 2) {
            historyTag.maxEms = 12
        } else {
            historyTag.maxEms = 10
        }
        historyTag.ellipsize = TextUtils.TruncateAt.END
        historyTag.gravity = Gravity.CENTER
        historyTag.setPadding(UiUtils.dp2px(8), 0, UiUtils.dp2px(8), 0)
        historyTag.setOnClickListener {
            if (history.historyType == 2 && !TextUtils.isEmpty(history.skipUrl)) {
                RoutersUtils.open(history.skipUrl)
            }
            if (history.historyType == 1) {
                isSuggest = false
                common_search_view.tvTitleEdit.setText(history.keyword)
                common_search_view.tvTitleEdit.setSelection(common_search_view.tvTitleEdit.text.length)
                searchKey = history.keyword
                val indexOfChild = i
                XyyIoUtil.trackSug(hashMapOf(
                    "spid" to "2",
                    "wq" to "",
                    "keyword" to history.keyword,
                    "pkw" to (preSearchKey?: ""),
                    "history" to "history_$indexOfChild"
                ), "2")
                shopGoodsFragment.startSearch(searchKey)
                switchPage(false)
            }
        }

        // 增加历史搜索词前缀logo
        historyTag.text = history.keyword
        if (history.historyType == 2) {
            val icon = ContextCompat.getDrawable(this, R.drawable.icon_search_history)
            icon!!.setBounds(0, 0, icon.minimumWidth, icon.minimumHeight)
            StringUtils.modifyTextViewDrawable(historyTag, icon, 0)
            historyTag.compoundDrawablePadding = UiUtils.dp2px(4)
        }
        val layoutParams =
            LinearLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, UiUtils.dp2px(30))
        layoutParams.leftMargin = UiUtils.dp2px(10)
        layoutParams.bottomMargin = UiUtils.dp2px(10)
        historyTag.layoutParams = layoutParams
        return historyTag
    }

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        val voiceReturnKey = intent?.getStringExtra("searchKey")
        if (voiceReturnKey?.isEmpty() == false) {
            searchKey = voiceReturnKey
            common_search_view?.apply {
                searchKey?.let { tvTitleEdit.setText(searchKey) }
            }
        }
    }
    var suggestPopWindow: SuggestNewPopWindowNew? = null
    private fun initSearchDropdown(searchBar: CommonSearchView) {
        searchBar.apply {
            suggestPopWindow = SuggestNewPopWindowNew(
                this@SearchResultActivity, AppNetConfig.SUGGEST_NEW,
                RequestParams().apply {
                    orgId?.let { put("orgId", orgId) }
                    shopCode?.let { put("shopCode", shopCode) }
                },
                this
            ).apply {
                dismissShopAndProduct = true
            }
            tvTitleEdit.inputType = InputType.TYPE_CLASS_TEXT or InputType.TYPE_TEXT_FLAG_CAP_CHARACTERS or InputType.TYPE_TEXT_FLAG_NO_SUGGESTIONS
            tvTitleEdit.doAfterTextChanged {
                if (it?.isEmpty() == true) {
                    switchPage(true)
                }
                if (searchKey?.equals(it?.toString()?.trim(), true) == true) {
                    return@doAfterTextChanged
                }
                if (!isSuggest) {
                    isSuggest = true
                    return@doAfterTextChanged
                }
                if (it?.toString().isNullOrEmpty()) return@doAfterTextChanged
                suggestPopWindow?.cancelHandler(false)
                suggestPopWindow?.suggest(it?.toString()!!)
            }
            suggestPopWindow?.setItemClickListener(listener = object : SuggestNewPopWindowNew.ItemClickListener {
                override fun onItemClick(str: String?, id: Long, position: Int) {
                    searchKey = str
                    tvTitleEdit.setText(str ?: "")
                    str?.let {
                        tvTitleEdit.setSelection(it.length)
                    }
                    shopGoodsFragment.startSearch(str)
                    switchPage(false)
                    suggestPopWindow?.dismiss()
                }
            })

        }


    }

    var mPw: PopupWindow? = null

    override fun getContentViewId(): Int = R.layout.activity_search_result

    override fun getRawAction(): String {
        val openNextPageUrl = StringBuilder()
        shopCode?.let { openNextPageUrl.append("ybmpage://shop_search_result?shopCode=${shopCode}") }
        orgId?.let { openNextPageUrl.append("ybmpage://shop_search_result?orgId=${orgId}") }

        searchKey?.let { openNextPageUrl.append("&&searchKey=${it}") }
        scanShopCode?.let { openNextPageUrl.append("&&scanShopCode=${it}") }

        return openNextPageUrl.toString()
    }

    /**
     * 搜索历史
     */
    fun initHistory() {
        common_search_view.tvTitleEdit.setOnFocusChangeListener { _, hasFocus ->
            switchPage(hasFocus)
            getHotSearch(true)
        }
        tv_history_clear.setOnClickListener {
            delHistoryData()
        }
    }

    /**
     * 清空历史记录
     */
    private fun delHistoryData() {
        val params = RequestParams()
        params.put("merchantId", merchant_id)
        if (orgId != null) {
            params.put("shopCode", orgIdShopCode)
        } else {
            params.put("shopCode", shopCode)
        }
        HttpManager.getInstance().post(
            AppNetConfig.DEL_HISTORY_HOT_SEARCH_LIST,
            params,
            object : BaseResponse<EmptyBean?>() {
                override fun onSuccess(
                    content: String?,
                    obj: BaseBean<EmptyBean?>?,
                    t: EmptyBean?
                ) {
                    super.onSuccess(content, obj, t)
                    if (obj != null) {
                        if (obj.isSuccess) {
                            historyList.clear()
                            flex_box_history.clear()
                            switchPage(false)
                        }
                    }
                }
            })
    }

    /**
     * 切换页面
     * @param showHistory true: 显示历史 隐藏列表
     */
    fun switchPage(showHistory: Boolean) {
        ll_pre.visibility = if (showHistory) View.VISIBLE else View.GONE
        placeHolder.visibility = if (!showHistory) View.VISIBLE else View.GONE
        val cart = common_search_view.findViewById<RelativeLayout>(R.id.rl_cart)
        val cancel = common_search_view.findViewById<TextView>(R.id.tv_search_history_cancel)
//        cart.visibility = if (showHistory) View.GONE else View.VISIBLE
//        cancel.visibility = if (!showHistory) View.GONE else View.VISIBLE
    }

    override fun registerPageProperties(): MutableMap<String, Any> {
        val properties = HashMap<String, Any>()
        properties[JGTrackManager.FIELD.FIELD_PAGE_ID] = JGTrackManager.TrackShopSearch.PAGE_ID
        properties[JGTrackManager.FIELD.FIELD_TITLE] = JGTrackManager.TrackShopSearch.TITLE
        return properties
    }

    override fun registerPageUrl(): String = this.getFullClassName()
}