package com.ybmmarket20.business.shop.ui

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Build
import android.text.Html
import android.text.TextUtils
import android.view.View
import androidx.fragment.app.activityViewModels
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import androidx.recyclerview.widget.LinearLayoutManager
import com.ybm.app.bean.NetError
import com.ybm.app.view.WrapLinearLayoutManager
import com.ybmmarket20.R
import com.ybmmarket20.adapter.ShopStreamerAdapter
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.CouponRowBean
import com.ybmmarket20.bean.PopCouponListBean
import com.ybmmarket20.bean.RowsBean
import com.ybmmarket20.bean.SearchResultBean
import com.ybmmarket20.bean.ShopHomeIndexBean
import com.ybmmarket20.bean.homesteady.HotZone
import com.ybmmarket20.bean.homesteady.StreamerItem
import com.ybmmarket20.business.shop.adapter.ShopCouponAdapter
import com.ybmmarket20.common.BaseFragment
import com.ybmmarket20.common.BaseResponse
import com.ybmmarket20.common.JGTrackManager
import com.ybmmarket20.common.JgTrackBean
import com.ybmmarket20.common.RequestParams
import com.ybmmarket20.common.getFullClassName
import com.ybmmarket20.common.jgShopMainResourceClickTrack
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.constant.IntentCanst
import com.ybmmarket20.network.HttpManager
import com.ybmmarket20.report.coupon.CouponEntryType
import com.ybmmarket20.report.coupon.ICouponEntryType
import com.ybmmarket20.utils.AdapterUtils.updateRowsData
import com.ybmmarket20.utils.SpUtil
import com.ybmmarket20.utils.analysis.XyyIoUtil
import com.ybmmarket20.utils.analysis.addAnalysisRequestParams
import com.ybmmarket20.utils.analysis.updateFlowData
import com.ybmmarket20.view.ShopNoticeDialog
import com.ybmmarket20.viewmodel.ShopAllViewModel
import com.ybmmarketkotlin.adapter.GoodListAdapterNew
import com.ybmmarketkotlin.adapter.GoodsListAdapterNewCategory
import kotlinx.android.synthetic.main.fragment_shop_home.ll_indicate
import kotlinx.android.synthetic.main.fragment_shop_home.ll_shop_notice
import kotlinx.android.synthetic.main.fragment_shop_home.rl_cvp
import kotlinx.android.synthetic.main.fragment_shop_home.rv_pop_coupon
import kotlinx.android.synthetic.main.fragment_shop_home.rv_shop_proprietary_home_floor
import kotlinx.android.synthetic.main.fragment_shop_home.rv_shop_proprietary_home_goods
import kotlinx.android.synthetic.main.fragment_shop_home.rv_streamer_line
import kotlinx.android.synthetic.main.fragment_shop_home.smartrefresh
import kotlinx.android.synthetic.main.fragment_shop_home.snwtv_express_tag
import kotlinx.android.synthetic.main.fragment_shop_home.tv_express_type
import kotlinx.android.synthetic.main.fragment_shop_home.tv_find_more
import kotlinx.android.synthetic.main.fragment_shop_home.tv_order_express_remark
import kotlinx.android.synthetic.main.fragment_shop_home.tv_order_send_local
import kotlinx.android.synthetic.main.fragment_shop_home.tv_shop_notices
import kotlinx.android.synthetic.main.fragment_shop_home.vp_banner
import org.json.JSONObject

class ShopHomeTabFragment : BaseFragment(), ICouponEntryType {

    var orgId: String? = null
    var shopCode: String? = null
    var floorId: String? = null
    var floorType: String? = null
    var floorName:String? = null
        set(value) {
            field = value
            jgTrackBean?.navigation_1 = "首页"
            jgTrackBean?.navigation_2 = value
        }
    var shopName: String? = null
        set(value) {
            shopCouponAdapter?.shopName = value
            floorAdapter?.shopName = value
            field = value
        }


    var floorData: MutableList<ShopHomeIndexBean.Floor> = mutableListOf()
    var goodsData: MutableList<RowsBean> = mutableListOf()
    var couponRowBeans: MutableList<CouponRowBean> = mutableListOf()

    var floorAdapter: HomeFloorAdapter? = null
    var goodlistAdapter: GoodListAdapterNew? = null
    var shopCouponAdapter: ShopCouponAdapter? = null

    var bannerController: BannerController? = null

    val popShopNoticesViewModel: ShopAllViewModel by activityViewModels()
    private var shopNoticeDialog: ShopNoticeDialog? = null
    private var jgTrackBean:JgTrackBean? = null
    private var source:String? = ""
    private var isFilterUnableAddCart:String? = ""
    override fun initData(content: String?) {
        shopNoticeDialog = activity?.let { ShopNoticeDialog(it) }
        orgId = arguments?.getString("orgId")
        shopCode = arguments?.getString("shopCode")
        shopName = arguments?.getString("shopName")
        source = arguments?.getString("source")
        isFilterUnableAddCart = arguments?.getString("isFilterUnableAddCart")
        jgTrackBean = arguments?.getSerializable(IntentCanst.JG_TRACK_BEAN)?.let { it as? JgTrackBean? }?.apply {
            url = <EMAIL>()
            jgReferrer = <EMAIL>()
            title = JGTrackManager.TrackShopMain.TITLE
            pageId = JGTrackManager.TrackShopMain.PAGE_ID
            module = JGTrackManager.Common.MODULE_PRODUCT_LIST
        }
        XyyIoUtil.track("shopHome_open", JSONObject().also {
            it.put("text", shopName)
            it.put("shopCode", shopCode ?: orgId)
        })
        floorAdapter = HomeFloorAdapter(R.layout.item_shop_floor, floorData)
        rv_shop_proprietary_home_floor.adapter = floorAdapter
        rv_shop_proprietary_home_floor.layoutManager = LinearLayoutManager(context)
        floorAdapter?.setOnItemClickListener { adapter, view, position ->
            val obj = JSONObject().apply {
                put("text", shopName)
                put("title", floorData[position].floorName)
            }
            XyyIoUtil.track(XyyIoUtil.SHOPHOME_FLOORTAB_CLICK, obj)
            if (!floorData.get(position).isSelect) {
                floorData.forEach { it.isSelect = false }

                floorData.get(position).isSelect = true
                floorId = floorData.get(position).floorId
                floorType = floorData.get(position).floorType
                floorName = floorData.get(position).floorName
                floorAdapter?.notifyDataSetChanged()
                // 获取对应楼层的商品信息
                rv_shop_proprietary_home_goods?.scrollToPosition(0)
                getNewGoodlist()
            }
        }

        goodlistAdapter = GoodsListAdapterNewCategory(this, goodsData,isAddCartShowPopupWindow = isFromShopCart()).apply {
            setEmptyView(
                notNullActivity,
                R.layout.layout_empty_view_all_goods,
                R.drawable.icon_empty,
                "哇哦，没有找到相关商品"
            )
            isShowShopInfo = false
            showUnderlinePrice = false
            jgTrackBean = <EMAIL>
            mIsFromShopCartGatherOrders = isFromShopCart()

            resourceViewTrackListener = { rowsBean, i,_ ->

                var productTag = ""
                rowsBean.tags?.productTags?.let { tagList ->
                    tagList.forEachIndexed { index, tagBean ->
                        if (index != tagList.size - 1) {
                            productTag += tagBean.text + "，"
                        } else {
                            productTag += tagBean.text
                        }
                    }
                }
                rowsBean.tags?.dataTags?.let { tagList ->
                    if (productTag.isNotEmpty()) {
                        productTag += ","
                    }
                    tagList.forEachIndexed { index, tagBean ->
                        if (index != tagList.size - 1) {
                            productTag += tagBean.text + "，"
                        } else {
                            productTag += tagBean.text
                        }
                    }
                }
            }

            productClickTrackListener = { rowsBean, i,isBtnClick,mContent,number ->
                var productTag = ""
                rowsBean.tags?.productTags?.let { tagList ->
                    tagList.forEachIndexed { index, tagBean ->
                        if (index != tagList.size - 1) {
                            productTag += tagBean.text + "，"
                        } else {
                            productTag += tagBean.text
                        }
                    }
                }
                rowsBean.tags?.dataTags?.let { tagList ->
                    if (productTag.isNotEmpty()) {
                        productTag += ","
                    }
                    tagList.forEachIndexed { index, tagBean ->
                        if (index != tagList.size - 1) {
                            productTag += tagBean.text + "，"
                        } else {
                            productTag += tagBean.text
                        }
                    }
                }

                val mJgTrackBean = jgTrackBean?.copy()?.apply {
                    module = JGTrackManager.Common.MODULE_PRODUCT_LIST
                }
            }
        }
//        goodlistAdapter?.isConfigSpellGroupSellOut = true
        goodlistAdapter?.flowData = mFlowData
        rv_shop_proprietary_home_goods.adapter = goodlistAdapter
        rv_shop_proprietary_home_goods.layoutManager = LinearLayoutManager(context)

        val mJgTrackBean = jgTrackBean?.copy()?.apply {
            module = JGTrackManager.Common.MODULE_PRODUCT_LIST
        }

        goodlistAdapter?.setOnLoadMoreListener(
            { getLoadMoreGoodlist() },
            rv_shop_proprietary_home_goods
        )

        shopCouponAdapter = ShopCouponAdapter(
                R.layout.item_pop_shop_coupon,
                couponRowBeans,
                shopName,shopCode).apply {
            jgTrackBean = <EMAIL>?.apply {
                url = <EMAIL>()
                jgReferrer = <EMAIL>()
                pageId = JGTrackManager.TrackShopMain.PAGE_ID
                title = JGTrackManager.TrackShopMain.TITLE
            }
        }
        rv_pop_coupon.layoutManager = LinearLayoutManager(
                context,
                LinearLayoutManager.HORIZONTAL,
                false)
        rv_pop_coupon.adapter = shopCouponAdapter

//        smartrefresh.setOnRefreshListener {
//            if (TextUtils.isEmpty(floorId) || TextUtils.isEmpty(floorType)) {
//                smartrefresh.finishRefresh()
//                return@setOnRefreshListener
//            }
//            getNewGoodlist()
//        }
        smartrefresh.setEnableRefresh(false)
        initReceiver()

        getFloorData()
        getShopCouponInfo()
    }

    private fun isFromShopCart() = source == "1"

    /**
     *
     */
    private fun getNewGoodlist() {
        showProgress()
        val newgoodparams = getNewGoodlistRequestParams(false)
        HttpManager.getInstance().post(
            AppNetConfig.SHOP_GETFLOORGOODS,
            newgoodparams,
            object : BaseResponse<SearchResultBean?>() {
                override fun onSuccess(
                    content: String?,
                    obj: BaseBean<SearchResultBean?>,
                    brandBean: SearchResultBean?
                ) {
                    dismissProgress()
                    smartrefresh?.finishRefresh()
                    goodsData.clear()
                    brandBean?.let {
                        updateGoodsData(true, it)
                    }
                }

                override fun onFailure(error: NetError) {
                    dismissProgress()
                    smartrefresh?.finishRefresh()
                }
            })
    }


    /**
     *  更新商品信息
     */
    private fun updateGoodsData(isRefresh: Boolean, rowsBeans: SearchResultBean) {
        searchMoreParams = rowsBeans.requestParams
        goodlistAdapter?.let {
            if (!rowsBeans.sptype.isNullOrEmpty() || !rowsBeans.spid.isNullOrEmpty() || !rowsBeans.sid.isNullOrEmpty()) {
                updateFlowData(
                    mFlowData,
                    rowsBeans.sptype,
                    rowsBeans.spid,
                    rowsBeans.sid,
                    rowsBeans.nsid
                )
                goodlistAdapter?.flowData = mFlowData
            }
            it.jgTrackBean = jgTrackBean
            updateRowsData(rowsBeans.licenseStatus, rowsBeans.rows, it, isRefresh, rowsBeans.isEnd)
        }
    }

    /**
     * 请求数据
     */
    private fun getLoadMoreGoodlist() {
        HttpManager.getInstance().post(
            AppNetConfig.SHOP_GETFLOORGOODS,
            getNewGoodlistRequestParams(true),
            object : BaseResponse<SearchResultBean?>() {
                override fun onSuccess(
                    content: String?,
                    obj: BaseBean<SearchResultBean?>?,
                    brandBean: SearchResultBean?
                ) {
                    brandBean?.let { updateGoodsData(false, it) }
                }

                override fun onFailure(error: NetError) {
                }
            })
    }

    private var searchMoreParams: RequestParams? = null

    /**
     * 请求参数
     */
    private fun getNewGoodlistRequestParams(loadMore: Boolean): RequestParams? =
        if (loadMore) searchMoreParams else RequestParams().apply {
            put("merchantId", SpUtil.getMerchantid())
            put("floorId", floorId)
            put("floorType", floorType)
            put("spFrom", "2")
            put("isFilterUnableAddCart", isFilterUnableAddCart)
            orgId?.let { put("orgId", orgId) }
            shopCode?.let { put("shopCode", shopCode) }
//            put("sptype", mFlowData.spType)
//            put("spid", mFlowData.spId)
//            put("spid", mFlowData.sId)
            if (mFlowData != null) {
                addAnalysisRequestParams(this, mFlowData)
            }
        }

    private fun setShopNotice(it: ShopHomeIndexBean.ShopNoticeVo) {
        ll_shop_notice.visibility = View.VISIBLE
        if (it.expressTypeTags != null && it.expressTypeTags.isNotEmpty()) {
            snwtv_express_tag.bindData(it.expressTypeTags, null, Int.MAX_VALUE)
        } else {
            snwtv_express_tag.visibility = View.GONE
            tv_express_type.visibility = View.GONE
        }
        if (it.content.isNullOrBlank()) {
            tv_shop_notices.visibility = View.GONE
        } else {
            val htmlString = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N)
                Html.fromHtml(it.content, Html.FROM_HTML_MODE_COMPACT)
            else Html.fromHtml(it.content)
            tv_shop_notices.text = htmlString
        }
        tv_find_more.setOnClickListener {_ ->
            shopNoticeDialog?.setData(it)
            shopNoticeDialog?.show()
            XyyIoUtil.track("shopHome_board_Click", hashMapOf("shop_code" to (shopCode?: orgId)))
        }
        //发货省市
        if (it.deliveryProvinceName == null) it.deliveryProvinceName = ""
        if (it.deliveryCityName == null) it.deliveryCityName = ""
        if (it.deliveryProvinceName.isEmpty() && it.deliveryCityName.isEmpty()) {
            tv_order_send_local.visibility = View.GONE
        } else {
            tv_order_send_local.text = "发货省市: ${it.deliveryProvinceName}${it.deliveryCityName}"
            tv_order_send_local.visibility = View.VISIBLE
        }
        //快递类型备注
        if (it.expressRemarks.isNullOrEmpty()) {
            tv_order_express_remark.visibility = View.GONE
        } else {
            tv_order_express_remark.visibility = View.VISIBLE
            tv_order_express_remark.text = it.expressRemarks
        }
    }

    /**
     * 获取楼层信息
     */
    private fun getFloorData() {
        val floorUrl =
            if (!TextUtils.isEmpty(orgId)) AppNetConfig.SHOP_HOME_INDEX_V2 else AppNetConfig.SHOP_PROPRIETARY_FLOOR
        val floorDataParams = RequestParams().apply {
            put("merchantId", SpUtil.getMerchantid())
            orgId?.let {
                put("orgId", orgId)
            }
            shopCode?.let {
                put("shopCode", shopCode)
            }
        }
        HttpManager.getInstance()
            .post(floorUrl, floorDataParams, object : BaseResponse<ShopHomeIndexBean?>() {
                override fun onSuccess(
                    content: String?,
                    obj: BaseBean<ShopHomeIndexBean?>?,
                    t: ShopHomeIndexBean?
                ) {
                    orgId?.let {
//                        if (t?.shopNoticeVo != null) {
//                            popShopNoticesViewModel.setShopNotices(t.shopNoticeVo)
//                        }
                        t?.let { it1 -> setShopNotice(it1.shopNoticeVo) }

                    }

                    t?.floorVOS?.let {
                        floorData.clear()
                        if (it.size > 0) {
                            it[0].isSelect = true
                            floorId = it[0].floorId
                            floorType = it[0].floorType
                            floorName = it[0].floorName
                            getNewGoodlist()
                        }
                        floorData.addAll(it)
                        floorAdapter?.notifyDataSetChanged()
                    }

                    if (t?.adrVOS?.size ?: 0 > 0) {
                        vp_banner?.let {
                            bannerController = BannerController(
                                    it,
                                    ll_indicate,
                                    t?.adrVOS).apply {
                                setOnPageChangeListener { position, bannerInfo ->
                                    val mJgTrackBean = jgTrackBean?.copy()?.apply {
                                        module = "banner"
                                    }
                                }
                            }
                        }
                        bannerController?.setOnclickItemListener { action ->
                            val objBanner = JSONObject().also {
                                it.put(
                                        "text",
                                        shopName)
                                it.put(
                                        "shopCode",
                                        shopCode ?: orgId)
                                it.put(
                                        "action",
                                        action)
                            }
                            XyyIoUtil.track(
                                    "shopHome_Banner_Click",
                                    objBanner)
                            val mJgTrackBean = jgTrackBean?.copy()?.apply {
                                module = "banner"
                            }
                            activity?.jgShopMainResourceClickTrack(mJgTrackBean, shopId = shopCode, shopName = shopName, locationUrl = action)
                        }
                        bannerController?.start()
                        rl_cvp?.visibility = View.VISIBLE
                    } else {
                        rl_cvp?.visibility = View.GONE
                    }
                    try {
                        t?.picAdrVOS?.let(::setShopStreamer)
                    } catch (e: Exception) {
                        e.printStackTrace()
                    }
                }
            })
    }

    /**
     * 设置横幅
     */
    fun setShopStreamer(picAdrVos: List<ShopHomeIndexBean.PicAdrVOS>) {
        val streamerList = picAdrVos.map {
            val zoneList = it.urls.map { routerUrl ->
                HotZone(routerUrl, 100 / picAdrVos.size, 0, "", "", "")
            }
            StreamerItem(it.imgUrl, "", true, zoneList)
        }
        val mJgTrackBean = jgTrackBean?.copy()?.apply {
            module = "热区"
        }
        val shopStreamerAdapter = ShopStreamerAdapter(streamerList, shopName, shopCode ?: orgId)
        rv_streamer_line.layoutManager =
            WrapLinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)
        rv_streamer_line.adapter = shopStreamerAdapter.apply {
            jgTrackBean = mJgTrackBean
        }
    }

    //动态注册广播
    private fun initReceiver() {
        val intentFilter = IntentFilter()
        intentFilter.addAction(IntentCanst.ACTION_RECEIVE_POP_COUPON)
        LocalBroadcastManager.getInstance(notNullActivity)
            .registerReceiver(mRefreshBroadcastReceiver, intentFilter)
    }

    private val mRefreshBroadcastReceiver: BroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            val action = intent.action
            if (IntentCanst.ACTION_RECEIVE_POP_COUPON.equals(action, ignoreCase = true)) {
                getShopCouponInfo()
            }
        }
    }

    /**
     * 获取 店铺 优惠券 信息
     */
    private fun getShopCouponInfo() {
        if (orgId?.isEmpty() == true && shopCode?.isEmpty() == true) {
            return
        }
        var couponUrl =
            if (!TextUtils.isEmpty(orgId)) AppNetConfig.SHOP_COUPON_INFO else AppNetConfig.SHOP_COUPON_INFO_SELF
        val shopCouponRequest = RequestParams().apply {
            orgId?.let {
                put("orgId", orgId)
            }
            shopCode?.let {
                put("shopCode", shopCode)
            }
        }
        HttpManager.getInstance()
            .post(couponUrl, shopCouponRequest, object : BaseResponse<PopCouponListBean?>() {
                override fun onSuccess(
                    content: String?,
                    obj: BaseBean<PopCouponListBean?>?,
                    bean: PopCouponListBean?
                ) {
                    obj?.let {
                        bean?.let {
                            if (couponRowBeans.size > 0) {
                                couponRowBeans.clear()
                            }
                            it?.list?.let {
                                couponRowBeans.addAll(it)
                            }
                        }
                        shopCouponAdapter?.notifyDataSetChanged()
                    }
                }
            })
    }


    override fun onStop() {
        super.onStop()
        bannerController?.stop()
    }

    override fun initTitle() {
    }

    override fun getParams(): RequestParams? = null

    override fun getUrl(): String? = null

    override fun getLayoutId(): Int = R.layout.fragment_shop_home
    override fun getCouponEntryType(): String = CouponEntryType.COUPON_ENTRY_TYPE_SHOP_HOME

}
