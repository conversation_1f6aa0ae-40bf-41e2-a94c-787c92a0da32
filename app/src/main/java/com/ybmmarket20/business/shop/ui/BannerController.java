package com.ybmmarket20.business.shop.ui;

import android.content.Context;

import androidx.viewpager.widget.PagerAdapter;
import androidx.viewpager.widget.ViewPager;

import android.text.TextUtils;
import android.util.TypedValue;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;

import com.ybm.app.common.ImageLoader.ImageHelper;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.ShopHomeIndexBean;
import com.ybmmarket20.common.JGTrackTopLevelKt;
import com.ybmmarket20.common.util.ConvertUtils;
import com.ybmmarket20.utils.RoutersUtils;
import com.ybmmarket20.utils.UiUtils;
import com.ybmmarket20.view.ClipViewPager;

import java.util.HashMap;
import java.util.List;
import java.util.Timer;
import java.util.TimerTask;

/**
 * <br>
 * 作者：zhuruqiao
 * 时间：2018/10/18 10:15
 * 邮箱：<EMAIL>
 */
public class BannerController {

    private LinearLayout indexGroup;

    private ClipViewPager viewPager;

    private List<ShopHomeIndexBean.BannerInfo> dataList;

    private Context context;

    private View currentSelect;

    private MyPagerAdapter pagerAdapter = new MyPagerAdapter();

    private OnClickItemListener onClickItemListener;

    private OnPageChangeListener onPageChangeListener;

    //是否正在循环
    private boolean isLooping;

    protected ItemClick itemClick = new ItemClick();

    private HashMap<String,Long> productViewTrackMap = new HashMap<>();
    private static Integer TRACK_DURATION = 2 * 60 * 1000; //2分钟内不上报

    public BannerController(ClipViewPager viewPager, LinearLayout indexGroup, List<ShopHomeIndexBean.BannerInfo> dataList) {
        this.indexGroup = indexGroup;
        this.viewPager = viewPager;
        this.dataList = dataList;
        this.context = viewPager.getContext();
        if (dataList == null || dataList.size() == 0) {
            return;
        }
        init();
    }

    public void setOnPageChangeListener(OnPageChangeListener onPageChangeListener) {
        this.onPageChangeListener = onPageChangeListener;
    }
    private void init() {
        indexGroup.setOrientation(LinearLayout.HORIZONTAL);
        for (Object o : dataList) {
            View view = new View(context);

            int dotWidth = (int) TypedValue.applyDimension(
                    TypedValue.COMPLEX_UNIT_DIP, 18, context.getResources()
                            .getDisplayMetrics());

            int dotHight = (int) TypedValue.applyDimension(
                    TypedValue.COMPLEX_UNIT_DIP, 3, context.getResources()
                            .getDisplayMetrics());

            // 设置宽高、marging
            LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(
                    dotWidth, dotHight);
            params.setMargins(0, 0, dotHight, 0);
            view.setLayoutParams(params);

            setOff(view);
            indexGroup.addView(view);
        }
        if (dataList != null && dataList.size() == 1) {
            indexGroup.setVisibility(View.GONE);
        } else {
            indexGroup.setVisibility(View.VISIBLE);
        }
        currentSelect = indexGroup.getChildAt(0);
        setOn(currentSelect);
        pagerAdapter.setDataList(dataList);
        viewPager.setAdapter(pagerAdapter);

        RelativeLayout.LayoutParams layoutParams = (RelativeLayout.LayoutParams) viewPager.getLayoutParams();
        layoutParams.setMargins(ConvertUtils.dp2px(10), ConvertUtils.dp2px(5), ConvertUtils.dp2px(10), ConvertUtils.dp2px(5));
        viewPager.setLayoutParams(layoutParams);
        viewPager.setPageMargin(ConvertUtils.dp2px(5));
        viewPager.addOnPageChangeListener(pageChangeListener);
        viewPager.setOnTouchListener((v, event) -> {
            if (event.getAction() == MotionEvent.ACTION_UP) {
                start();
            } else {
                stop();
            }
            return false;
        });


    }

    private Timer timer;


    private ViewPager.OnPageChangeListener pageChangeListener = new ViewPager.OnPageChangeListener() {
        @Override
        public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

        }

        @Override
        public void onPageSelected(int position) {
            if (currentSelect != null) {
                setOff(currentSelect);
            }
            currentSelect = indexGroup.getChildAt(position % dataList.size());
            setOn(currentSelect);
            if (onPageChangeListener!=null){
                if (position>0 && position<dataList.size()){
                    ShopHomeIndexBean.BannerInfo bannerInfo = dataList.get(position);
                    Long preTime = productViewTrackMap.get(bannerInfo.imgUrl);
                    if (preTime != null){
                        if ( preTime - System.currentTimeMillis() > TRACK_DURATION){
                            onPageChangeListener.onPageSelected(position,bannerInfo);
                            productViewTrackMap.put(bannerInfo.imgUrl,System.currentTimeMillis());
                        }
                    }else {
                        onPageChangeListener.onPageSelected(position,bannerInfo);
                        productViewTrackMap.put(bannerInfo.imgUrl,System.currentTimeMillis());
                    }

                }
            }
        }

        @Override
        public void onPageScrollStateChanged(int state) {

        }
    };

    public interface OnPageChangeListener{
        void onPageSelected(int position, ShopHomeIndexBean.BannerInfo bannerInfo);
    }


    public void start() {
        if (this.dataList == null || dataList.size() <= 1) {
            return;
        }

        timer = new Timer();
        timer.schedule(new TimerTask() {
            @Override
            public void run() {
                UiUtils.runOnMainThread(new Runnable() {
                    @Override
                    public void run() {
                        int currentItem = viewPager.getCurrentItem();
                        viewPager.setCurrentItem(++currentItem);
                    }
                });

            }
        }, 3000, 3000);


    }

    public void stop() {
        if (timer != null) {
            timer.cancel();

        }

    }

    public void setOn(View view) {
        view.setBackgroundResource(R.drawable.shop_indicate_on);
    }

    public void setOff(View view) {
        view.setBackgroundResource(R.drawable.shop_indicate_off);
    }


    public class MyPagerAdapter extends PagerAdapter {


        private List<ShopHomeIndexBean.BannerInfo> dataList;


        public void setDataList(List<ShopHomeIndexBean.BannerInfo> dataList) {
            this.dataList = dataList;
        }

        @Override
        public int getCount() {
            if (dataList == null) return 0;
            if (dataList.size() == 1) return 1;
            return Integer.MAX_VALUE;
        }

        @Override
        public boolean isViewFromObject(View view, Object object) {
            return view == object;
        }

        @Override
        public Object instantiateItem(ViewGroup container, int position) {
            ImageView imageView = new ImageView(context);
            imageView.setScaleType(ImageView.ScaleType.FIT_XY);
            container.addView(imageView);
            int i = position % dataList.size();
            ShopHomeIndexBean.BannerInfo bannerInfo = dataList.get(i);
            ImageHelper.with(context).load(bannerInfo.imgUrl).into(imageView);

            imageView.setTag(R.id.tag_action, bannerInfo.url);
            imageView.setTag(R.id.tag_1, position);
            imageView.setOnClickListener(itemClick);
            return imageView;

        }

        @Override
        public void destroyItem(ViewGroup container, int position, Object object) {
            container.removeView((View) object);

        }
    }

    //通用点击跳转
    public class ItemClick implements View.OnClickListener {
        @Override
        public void onClick(final View v) {
            v.setEnabled(false);
            String action = (String) v.getTag(R.id.tag_action);
            click(action);
            v.postDelayed(new Runnable() {
                @Override
                public void run() {
                    if (v != null) {
                        v.setEnabled(true);
                    }
                }
            }, 500);
        }
    }

    //执行点击
    public void click(String action) {
        if (TextUtils.isEmpty(action)) {
            return;
        }
        HashMap<String,String> mParams = new HashMap<>();
        mParams.put("entrance","店铺-banner");
        action = JGTrackTopLevelKt.splicingUrlWithParams(action,mParams);
        if (onClickItemListener != null) onClickItemListener.onClickItem(action);
        RoutersUtils.open(action);
    }

    public void setOnclickItemListener(OnClickItemListener onClickItemListener) {
        this.onClickItemListener = onClickItemListener;
    }

    interface OnClickItemListener {
        void onClickItem(String action);
    }

}
