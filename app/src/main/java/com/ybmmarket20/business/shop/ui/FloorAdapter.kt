package com.ybmmarket20.business.shop.ui

import android.widget.TextView
import com.ybm.app.adapter.YBMBaseAdapter
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.bean.ShopHomeIndexBean
import com.ybmmarket20.utils.UiUtils

open class FloorAdapter(
    layoutResId: Int,
    data: MutableList<ShopHomeIndexBean.Floor>?
) : YBMBaseAdapter<ShopHomeIndexBean.Floor>(layoutResId, data) {

    override fun bindItemView(baseViewHolder: YBMBaseHolder, t: ShopHomeIndexBean.Floor) {
        val tvFloorName = baseViewHolder.getView<TextView>(R.id.tv_floor_name)
        tvFloorName?.text = t.floorName
        if (t.isSelect) {
            tvFloorName.setTextColor(UiUtils.getColor(R.color.color_00b377))
            tvFloorName.isActivated = true
        } else {
            tvFloorName.setTextColor(UiUtils.getColor(R.color.color_676773))
            tvFloorName.isActivated = false
        }
    }
}