package com.ybmmarket20.business.shop.ui

import android.util.SparseArray
import androidx.core.util.contains
import androidx.core.util.containsKey
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.bean.ShopHomeIndexBean
import com.ybmmarket20.utils.analysis.XyyIoUtil
import org.json.JSONObject

class HomeFloorAdapter(
    layoutResId: Int,
    data: MutableList<ShopHomeIndexBean.Floor>?,
    var shopName: String? = ""
) : FloorAdapter(layoutResId, data) {
    private val exposureViews = SparseArray<String>()

    override fun bindItemView(baseViewHolder: YBMBaseHolder, t: ShopHomeIndexBean.Floor) {
        super.bindItemView(baseViewHolder, t)
        if (!exposureViews.containsKey(baseViewHolder.layoutPosition)) {
            exposureViews.put(baseViewHolder.layoutPosition, t.floorId)
            val obj = JSONObject().apply {
                put("text", shopName)
                put("title", t.floorName)
            }
            XyyIoUtil.track(XyyIoUtil.SHOPHOME_FLOORTAB_EXPOSURE, obj)
        }
    }
}