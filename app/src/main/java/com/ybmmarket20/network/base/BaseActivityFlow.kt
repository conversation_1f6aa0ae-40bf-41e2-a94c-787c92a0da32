package com.ybmmarket20.network.base

import androidx.lifecycle.lifecycleScope
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.common.BaseActivity
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch

private fun <T> BaseActivity.launchWithLoadingFlow(block: suspend () -> BaseBean<T>): Flow<BaseBean<T>> {
    return flow {
        emit(block())
    }.onStart {
        showProgress()
    }.onCompletion {
        dismissProgress()
    }
}

private fun <T> BaseActivity.parseResultAndCallback(
    response: BaseBean<T>,
    listenerBuilder: ResultBuilder<T>.() -> Unit
) {
    val listener = ResultBuilder<T>().also(listenerBuilder)
    if (response.isSuccess) {
        listener.onSuccess(response.data)
    } else {
        listener.onFailed(response.code, response.msg)
    }
    listener.onComplete()
}

fun <T> BaseActivity.launchWithLoadingAndCollect(
    block: suspend () -> BaseBean<T>,
    listenerBuilder: ResultBuilder<T>.() -> Unit
) {
    lifecycleScope.launch {
        launchWithLoadingFlow(block).collect {
            parseResultAndCallback(it, listenerBuilder)
        }
    }
}