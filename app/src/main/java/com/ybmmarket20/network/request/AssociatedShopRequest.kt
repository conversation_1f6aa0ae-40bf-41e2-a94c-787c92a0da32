package com.ybmmarket20.network.request

import com.ybmmarket20.bean.AssociatedShopBean
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.more_account.data.initWithException
import com.ybmmarket20.network.NetworkService
import retrofit2.http.Field
import retrofit2.http.FormUrlEncoded
import retrofit2.http.POST

/**
 * 关联店铺
 */
interface IAssociatedShopService {

    @FormUrlEncoded
    @POST("relShop")
    suspend fun associatedShop(@Field("poiId")poiId: String, @Field("accountId")accountId: String): BaseBean<AssociatedShopBean>

}

class AssociateShopRequest {

    /**
     * 关联店铺
     */
    suspend fun associatedShop(poiId: String, accountId: String): BaseBean<AssociatedShopBean> = try {
        NetworkService.instance.mRetrofit.create(IAssociatedShopService::class.java).associatedShop(poiId, accountId)
    } catch (e: Exception) {
        BaseBean<AssociatedShopBean>().initWithException(e)
    }
}