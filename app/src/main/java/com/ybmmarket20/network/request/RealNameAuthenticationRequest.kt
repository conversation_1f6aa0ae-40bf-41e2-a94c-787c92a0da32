package com.ybmmarket20.network.request

import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.EmptyBean
import com.ybmmarket20.bean.PhoneMobileResponse
import com.ybmmarket20.more_account.data.initWithException
import com.ybmmarket20.network.NetworkService
import retrofit2.http.Field
import retrofit2.http.FormUrlEncoded
import retrofit2.http.POST

/**
 * 实名认证
 */
interface IRealNameAuthenticationService {

    @POST("account/sendClearIdentityVerifyCode")
    suspend fun sendClearIdentityVerifyCode(): BaseBean<EmptyBean>

    @FormUrlEncoded
    @POST("account/clearAccountIdentity")
    suspend fun clearAccountIdentity(@Field("code") code:String): BaseBean<EmptyBean>

    @POST("account/info")
    suspend fun getPhoneInfo(): BaseBean<PhoneMobileResponse>

}

class RealNameAuthenticationRequest {

    /**
     * 发送验证码
     */
    suspend fun sendClearIdentityVerifyCode(): BaseBean<EmptyBean> = try {
        NetworkService.instance.mRetrofit.create(IRealNameAuthenticationService::class.java).sendClearIdentityVerifyCode()
    } catch (e: Exception) {
        BaseBean<EmptyBean>().initWithException(e)
    }

    /**
     * 获取手机号
     * @return BaseBean<EmptyBean>
     */
    suspend fun getPhoneInfo(): BaseBean<PhoneMobileResponse> = try {
        NetworkService.instance.mRetrofit.create(IRealNameAuthenticationService::class.java).getPhoneInfo()
    } catch (e: Exception) {
        BaseBean<PhoneMobileResponse>().initWithException(e)
    }

    /**
     * 发送验证码
     */
    suspend fun clearAccountIdentity(code:String): BaseBean<EmptyBean> = try {
        NetworkService.instance.mRetrofit.create(IRealNameAuthenticationService::class.java).clearAccountIdentity(code)
    } catch (e: Exception) {
        BaseBean<EmptyBean>().initWithException(e)
    }

}