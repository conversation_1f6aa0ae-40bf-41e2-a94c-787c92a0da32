package com.ybmmarket20.network.request

import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.FreightTipBean
import retrofit2.http.Field
import retrofit2.http.FormUrlEncoded
import retrofit2.http.POST

interface ShopAfterSaleDistributionRequest {

    @FormUrlEncoded
    @POST("freight/query")
    suspend fun getSelfAfterSaleDistributionContent(@Field("merchantId")merchantId: String, @Field("shopCode")shopCode: String): BaseBean<FreightTipBean>


    @FormUrlEncoded
    @POST("company/center/companyInfo/getReturnNotice")
    suspend fun getPopAfterSaleDistributionContent(@Field("orgId")orgId: String): BaseBean<String>


}