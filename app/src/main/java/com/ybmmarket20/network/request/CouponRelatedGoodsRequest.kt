package com.ybmmarket20.network.request

import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.CouponInfoBean
import com.ybmmarket20.bean.CouponRelatedGoodsPriceBean
import com.ybmmarket20.bean.RowsBean
import com.ybmmarket20.bean.SearchFilterBean
import com.ybmmarket20.bean.SearchResultBean
import com.ybmmarket20.bean.SearchResultOPBean
import com.ybmmarket20.more_account.data.initWithException
import com.ybmmarket20.network.NetworkService
import retrofit2.http.FieldMap
import retrofit2.http.FormUrlEncoded
import retrofit2.http.POST


interface ICouponRelatedGoodsService {

    @FormUrlEncoded
    @POST("voucher/supplementOrderPriceRange")
    suspend fun getPriceFilterList(@FieldMap params: Map<String, String>): BaseBean<List<CouponRelatedGoodsPriceBean>>

    @FormUrlEncoded
    @POST("voucher/getMyVoucherListByTemplateInfo")
    suspend fun getCouponInfo(@FieldMap params: Map<String, String>): BaseBean<List<CouponInfoBean>>

    @FormUrlEncoded
    @POST("voucher/supplementOrderShopInfo")
    suspend fun getShopList(@FieldMap params: Map<String, String>): BaseBean<List<SearchFilterBean>>

    @FormUrlEncoded
    @POST("voucher/supplementOrderProducts")
    suspend fun getCouponRelatedGoodsList(@FieldMap params: Map<String, String>): BaseBean<SearchResultOPBean>
}

class CouponRelatedGoodsRequest {

    /**
     * 获取凑单页价格筛选条件
     */
    suspend fun getPriceFilterList(params: Map<String, String>): BaseBean<List<CouponRelatedGoodsPriceBean>> = try {
        NetworkService.instance.mRetrofit.create(ICouponRelatedGoodsService::class.java).getPriceFilterList(params)
    } catch (e: Exception) {
        BaseBean<List<CouponRelatedGoodsPriceBean>>().initWithException(e)
    }

    /**
     * 获取优惠券信息
     */
    suspend fun getCouponInfo(params: Map<String, String>): BaseBean<List<CouponInfoBean>> = try {
        NetworkService.instance.mRetrofit.create(ICouponRelatedGoodsService::class.java).getCouponInfo(params)
    } catch (e: Exception) {
        BaseBean<List<CouponInfoBean>>().initWithException(e)
    }

    /**
     * 获取店铺列表
     */
    suspend fun getShopList(@FieldMap params: Map<String, String>): BaseBean<List<SearchFilterBean>> = try {
        NetworkService.instance.mRetrofit.create(ICouponRelatedGoodsService::class.java).getShopList(params)
    } catch (e: Exception) {
        BaseBean<List<SearchFilterBean>>().initWithException(e)
    }

    /**
     * 获取优惠券凑单商品
     */
    suspend fun getCouponRelatedGoodsList(@FieldMap params: Map<String, String>): BaseBean<SearchResultOPBean> = try {
        NetworkService.instance.mRetrofit.create(ICouponRelatedGoodsService::class.java).getCouponRelatedGoodsList(params)
    } catch (e: Exception) {
        BaseBean<SearchResultOPBean>().initWithException(e)
    }

}