package com.ybmmarket20.network.request

import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.PurchaseSwitchBean
import com.ybmmarket20.more_account.data.initWithException
import com.ybmmarket20.network.NetworkService
import retrofit2.http.Field
import retrofit2.http.FormUrlEncoded
import retrofit2.http.POST

/**
 * 解绑
 */
interface UnBindBankService {

    @FormUrlEncoded
    @POST("card/cancelContract")
    suspend fun unBindBankCard(@Field("reqNo")reqNo: String): BaseBean<Any>
}


class UnBindBankRequest {
    suspend fun unBindBankCard(@Field("reqNo")reqNo: String): BaseBean<Any> = try {
        NetworkService.instance.mRetrofit.create(UnBindBankService::class.java).unBindBankCard(reqNo)
    } catch (e: Exception) {
        BaseBean<Any>().initWithException(e)
    }
}