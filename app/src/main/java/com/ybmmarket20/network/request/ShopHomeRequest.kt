package com.ybmmarket20.network.request

import com.ybmmarket20.bean.AptitudeBasicInfoBean
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.ImPackUrlBean
import com.ybmmarket20.bean.ShopBasicInfoBean
import com.ybmmarket20.bean.ShopTab
import com.ybmmarketkotlin.bean.ShopBaseInfo
import retrofit2.http.Field
import retrofit2.http.FormUrlEncoded
import retrofit2.http.POST

interface ShopHomeRequest {

    @FormUrlEncoded
    @POST("company/center/companyInfo/getPopCompanyDetail")
    suspend fun getPopCompanyDetail(@Field("merchantId") merchantId: String, @Field("orgId") orgId: String,@Field("selectDeliveryAddress") selectDeliveryAddress: Boolean): BaseBean<ShopBasicInfoBean>

    @FormUrlEncoded
    @POST("shop/shopBaseInfo")
    suspend fun getShopInfo(@Field("merchantId") merchantId: String, @Field("shopCode") shopCode: String): BaseBean<ShopBaseInfo>

    @FormUrlEncoded
    @POST("messageCenter/getIMPackUrl")
    suspend fun sendOnLineService(@Field("isThirdCompany") isThirdCompany: String): BaseBean<ImPackUrlBean>

    @FormUrlEncoded
    @POST("company/center/companyInfo/getFbpShopInfo")
    suspend fun getShopTabs(@Field("merchantId")merchantId: String): BaseBean<List<ShopTab>>
}