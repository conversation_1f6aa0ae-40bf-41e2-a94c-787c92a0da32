package com.ybmmarket20.network.request

import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.VirtualMoney
import com.ybmmarket20.bean.VirtualMoneyData
import com.ybmmarket20.more_account.data.initWithException
import com.ybmmarket20.network.NetworkService
import retrofit2.http.Field
import retrofit2.http.FormUrlEncoded
import retrofit2.http.POST

/**
 * 购物金
 */
interface VirtualMoneyService {

    @FormUrlEncoded
    @POST("virtualGold/list")
    suspend fun getMyVirtualMoneyList(@Field("offset")offset: String, @Field("limit")limit: String, @Field("virtualGoldType")virtualGoldType: String): BaseBean<VirtualMoneyData>

    @FormUrlEncoded
    @POST("virtualGold/query")
    suspend fun getMyVirtualMoney(@Field("merchantId")merchantId: String):BaseBean<VirtualMoney>
}

class VirtualMoneyRequest {

    suspend fun getMyVirtualMoneyList(offset: String, limit: String, virtualGoldType: String): BaseBean<VirtualMoneyData> = try {
        NetworkService.instance.mRetrofit.create(VirtualMoneyService::class.java).getMyVirtualMoneyList(offset, limit, virtualGoldType)
    } catch (e: Exception) {
        BaseBean<VirtualMoneyData>().initWithException(e)
    }

    suspend fun getMyVirtualMoney(merchantId: String) = try {
        NetworkService.instance.mRetrofit.create(VirtualMoneyService::class.java).getMyVirtualMoney(merchantId)
    } catch (e: Exception) {
        BaseBean<VirtualMoney>().initWithException(e)
    }
}