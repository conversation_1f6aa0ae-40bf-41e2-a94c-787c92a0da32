package com.ybmmarket20.network.request

import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.FlowDataAnalysisSId
import com.ybmmarket20.bean.RefundOrAfterSalesBean
import com.ybmmarket20.more_account.data.initWithException
import com.ybmmarket20.network.NetworkService
import com.ybmmarket20.utils.SpUtil
import retrofit2.http.Field
import retrofit2.http.FieldMap
import retrofit2.http.FormUrlEncoded
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Query

interface IRefundOrAfterSalesService {

    @FormUrlEncoded
    @POST("afterSales/list")
    suspend fun getRefundOrAfterSalesList(@FieldMap paramsMap: Map<String, String>): BaseBean<RefundOrAfterSalesBean>

    @FormUrlEncoded
    @POST("sku/getSid")
    suspend fun getSId(@Field("merchantId")merchantId: String): BaseBean<FlowDataAnalysisSId>

    @FormUrlEncoded
    @POST("orders/rebuyForOrder")
    suspend fun buyAgain(@FieldMap paramsMap: Map<String, String>): BaseBean<Any>

    @GET("intervention/check")
    suspend fun checkIntervention(@Query("refundOrderNo") refundOrderNo:String,@Query("orderNo") orderNo:String ): BaseBean<String>
}

class RefundOrAfterSalesRequest {

    suspend fun getRefundOrAfterSalesList(paramsMap: Map<String, String>): BaseBean<RefundOrAfterSalesBean> = try {
        NetworkService.instance.mRetrofit.create(IRefundOrAfterSalesService::class.java).getRefundOrAfterSalesList(paramsMap)
    } catch (e: Exception) {
        BaseBean<RefundOrAfterSalesBean>().initWithException(e)
    }

    suspend fun getSId(): BaseBean<FlowDataAnalysisSId> = try {
        NetworkService.instance.mRetrofit.create(IRefundOrAfterSalesService::class.java).getSId(SpUtil.getMerchantid())
    } catch (e: Exception) {
        BaseBean<FlowDataAnalysisSId>().initWithException(e)
    }

    suspend fun buyAgain(@FieldMap paramsMap: Map<String, String>): BaseBean<Any> = try {
        NetworkService.instance.mRetrofit.create(IRefundOrAfterSalesService::class.java).buyAgain(paramsMap)
    } catch (e: Exception) {
        BaseBean<Any>().initWithException(e)
    }
    suspend fun checkIntervention(refundOrderNo:String,orderNo:String): BaseBean<String> = try {
        NetworkService.instance.mRetrofit.create(IRefundOrAfterSalesService::class.java).checkIntervention(refundOrderNo,orderNo)
    } catch (e: Exception) {
        BaseBean<String>().initWithException(e)
    }
}