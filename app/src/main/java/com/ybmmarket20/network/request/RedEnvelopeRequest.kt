package com.ybmmarket20.network.request

import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.RedEnvelopeData
import com.ybmmarket20.bean.RedEnvelopeRecordData
import com.ybmmarket20.more_account.data.initWithException
import com.ybmmarket20.network.NetworkService
import retrofit2.http.Field
import retrofit2.http.FormUrlEncoded
import retrofit2.http.POST

/**
 * 红包相关请求
 */
interface RedEnvelopeService {

    /**
     * 获取我的红包列表
     */
    @FormUrlEncoded
    @POST("redPacket/myRedPacketList")
    suspend fun getRedEnvelopeList(@Field("queryStatus") queryStatus: String, @Field("pageNum") pageNum: String, @Field("pageSize") pageSize: String = ""): BaseBean<RedEnvelopeData>

    /**
     * 获取我的红包收支记录
     */
    @FormUrlEncoded
    @POST("redPacket/myRedPacketRecords")
    suspend fun getRedEnvelopeRecordList(@Field("tradeType") tradeType: String, @Field("pageNum") pageNum: String, @Field("pageSize") pageSize: String = ""): BaseBean<RedEnvelopeRecordData>
}

class RedEnvelopeRequest {
    /**
     * 获取我的红包列表
     */
    suspend fun getRedEnvelopeList(@Field("queryStatus") queryStatus: String, @Field("pageNum") pageNum: String, @Field("pageSize") pageSize: String = ""): BaseBean<RedEnvelopeData> = try {
        NetworkService.instance.mRetrofit.create(RedEnvelopeService::class.java).getRedEnvelopeList(queryStatus, pageNum, pageSize)
    } catch (e: Exception) {
        BaseBean<RedEnvelopeData>().initWithException(e)
    }

    /**
     * 获取我的红包收支记录
     */
    @FormUrlEncoded
    @POST("redPacket/myRedPacketRecords")
    suspend fun getRedEnvelopeRecordList(@Field("tradeType") tradeType: String, @Field("pageNum") pageNum: String, @Field("pageSize") pageSize: String = ""): BaseBean<RedEnvelopeRecordData> = try {
        NetworkService.instance.mRetrofit.create(RedEnvelopeService::class.java).getRedEnvelopeRecordList(tradeType, pageNum, pageSize)
    } catch (e: Exception) {
        BaseBean<RedEnvelopeRecordData>().initWithException(e)
    }
}