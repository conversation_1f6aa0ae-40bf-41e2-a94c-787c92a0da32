package com.ybmmarket20.network.request

import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.FindSameGoodsResultBean
import com.ybmmarket20.bean.HomeFeedBean
import com.ybmmarket20.bean.SearchResultBean
import com.ybmmarket20.more_account.data.initWithException
import com.ybmmarket20.network.NetworkService
import retrofit2.http.FieldMap
import retrofit2.http.FormUrlEncoded
import retrofit2.http.POST
import java.lang.Exception

class GoodsListRequest {
    /**
     * 找相似列表
     */
    suspend fun getGoodsListFindSameGoods(@FieldMap paramsMap: MutableMap<String, String>): BaseBean<FindSameGoodsResultBean>? = try {
        NetworkService.instance.mRetrofit.create(GoodsListService::class.java).getGoodsListFindSameGoods(paramsMap)
    } catch (e: Exception) {
        BaseBean<FindSameGoodsResultBean>().initWithException(e)
    }
}

interface GoodsListService {
    @FormUrlEncoded
    @POST("recommend/v1/failureSimilarProducts")
    suspend fun getGoodsListFindSameGoods(@FieldMap paramsMap: MutableMap<String, String>): BaseBean<FindSameGoodsResultBean>?
}