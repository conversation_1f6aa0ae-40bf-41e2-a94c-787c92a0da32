package com.ybmmarket20.network.request

import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.UserRecallBean
import com.ybmmarket20.more_account.data.initWithException
import com.ybmmarket20.network.NetworkService
import retrofit2.http.POST

/**
 * 获取用户召回路由信息
 */

interface IUserRecallService {

    @POST("layout/getUserRecallGuideInfo")
    suspend fun getUserRecallGuideInfo(): BaseBean<UserRecallBean>
}

class UserRecallRequest {

    suspend fun getUserRecallGuideInfo(): BaseBean<UserRecallBean> = try {
        NetworkService.instance.mRetrofit.create(IUserRecallService::class.java).getUserRecallGuideInfo()
    } catch (e: Exception) {
        BaseBean<UserRecallBean>().initWithException(e)
    }
}