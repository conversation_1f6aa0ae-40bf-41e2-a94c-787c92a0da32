package com.ybmmarket20.network.request

import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.payment.PaymentConsumeRebateDetailBean
import com.ybmmarket20.more_account.data.initWithException
import com.ybmmarket20.network.NetworkService
import retrofit2.http.Field
import retrofit2.http.FormUrlEncoded
import retrofit2.http.POST

/**
 * hcq
 * 25.7.17
 */

interface IRebateRatioService {
    @FormUrlEncoded
    @POST("order/v1/queryConsumeRebateDetail")
    suspend fun getRebateRatioInfo(@Field("money") money: Number): BaseBean<PaymentConsumeRebateDetailBean>
}

class RebateRatioRequest {
    suspend fun getRebateRatioData(money: Number): BaseBean<PaymentConsumeRebateDetailBean> = try {
        NetworkService.instance.mRetrofit.create(IRebateRatioService::class.java)
            .getRebateRatioInfo(money)
    } catch (e: Exception) {
        BaseBean<PaymentConsumeRebateDetailBean>().initWithException(e)
    }
}