package com.ybmmarket20.network.request

import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.HomeAlertBean
import com.ybmmarket20.more_account.data.initWithException
import com.ybmmarket20.network.NetworkService
import retrofit2.http.FieldMap
import retrofit2.http.FormUrlEncoded
import retrofit2.http.POST

interface IHomeAlertService {

    @FormUrlEncoded
    @POST("layout/indexDialog")
    suspend fun requestHomeAlert(@FieldMap params: Map<String, String>): BaseBean<HomeAlertBean>
}

class HomeAlertRequest {

    /**
     * 请求首页弹窗
     */
    suspend fun requestHomeAlert(params: Map<String, String>): BaseBean<HomeAlertBean> = try {
        NetworkService.instance.mRetrofit.create(IHomeAlertService::class.java).requestHomeAlert(params)
    } catch (e: Exception) {
        BaseBean<HomeAlertBean>().initWithException(e)
    }

}