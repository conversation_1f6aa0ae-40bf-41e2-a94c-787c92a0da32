package com.ybmmarket20.network.request

import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.SearchSpellGroup
import com.ybmmarket20.more_account.data.initWithException
import com.ybmmarket20.network.NetworkService
import retrofit2.http.FormUrlEncoded
import retrofit2.http.POST
import java.lang.Exception

/**
 * 大搜获取拼团数据
 */
interface SearchSpellGroupService {

    @POST("sku/listSearchRecommendGroupBuyingProducts")
    suspend fun getSearchSpellGroupData(): BaseBean<SearchSpellGroup>?
}

class SearchSpellGroupRequest {
    suspend fun getSearchSpellGroupData(): BaseBean<SearchSpellGroup>? = try {
        NetworkService.instance.mRetrofit.create(SearchSpellGroupService::class.java).getSearchSpellGroupData()
    } catch(e: Exception) {
        BaseBean<SearchSpellGroup>().initWithException(e)
    }
}