package com.ybmmarket20.network.request

import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.ClerkInfoWithPage
import com.ybmmarket20.more_account.data.initWithException
import com.ybmmarket20.network.NetworkService
import retrofit2.http.Field
import retrofit2.http.FormUrlEncoded
import retrofit2.http.POST

/**
 * 店员信息
 */
interface IClerkInfoService {
    @FormUrlEncoded
    @POST("merchant/clerk")
    suspend fun getClerkInfoList(@Field("merchantId")merchantId: String, @Field("pageNo")pageNo: String, @Field("pageSize")pageSize: String): BaseBean<ClerkInfoWithPage>
}
class ClerkInfoRequest {
    suspend fun getClerkInfoList(merchantId: String, pageNo: String, pageSize: String): BaseBean<ClerkInfoWithPage> = try {
        NetworkService.instance.mRetrofit.create(IClerkInfoService::class.java).getClerkInfoList(merchantId, pageNo, pageSize)
    } catch (e: Exception) {
        BaseBean<ClerkInfoWithPage>().initWithException(e)
    }
}