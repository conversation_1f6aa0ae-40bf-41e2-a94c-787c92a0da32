package com.ybmmarket20.network.request

import com.ybmmarket20.bean.aftersales.AfterSalesErrorInvoiceItem
import com.ybmmarket20.bean.aftersales.AfterSalesSpecialInvoice
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.aftersales.AfterSalesNoBean
import com.ybmmarket20.more_account.data.initWithException
import com.ybmmarket20.network.NetworkService
import retrofit2.http.Field
import retrofit2.http.FieldMap
import retrofit2.http.FormUrlEncoded
import retrofit2.http.POST
import kotlin.Exception

interface IAfterSalesService {

    @FormUrlEncoded
    @POST("afterSales/queryIncorrectInvoiceType")
    suspend fun queryErrorInvoiceInfoList(@Field("orderNo")orderNo: String): BaseBean<MutableList<AfterSalesErrorInvoiceItem>>

    @FormUrlEncoded
    @POST("afterSales/querySpecialInvoice")
    suspend fun querySpecialInvoiceInfo(@Field("orderNo")orderNo: String): BaseBean<AfterSalesSpecialInvoice>

    @FormUrlEncoded
    @POST("afterSales/invoiceApply")
    suspend fun submitInvoiceAfterSales(@FieldMap paramsMap: Map<String, String>): BaseBean<AfterSalesNoBean>
}

class AfterSalesRequest {

    suspend fun queryErrorInvoiceInfoList(orderNo: String): BaseBean<MutableList<AfterSalesErrorInvoiceItem>> = try {
        NetworkService.instance.mRetrofit.create(IAfterSalesService::class.java).queryErrorInvoiceInfoList(orderNo)
    } catch (e: Exception) {
        BaseBean<MutableList<AfterSalesErrorInvoiceItem>>().initWithException(e)
    }

    suspend fun querySpecialInvoiceInfo(orderNo: String): BaseBean<AfterSalesSpecialInvoice> = try {
        NetworkService.instance.mRetrofit.create(IAfterSalesService::class.java).querySpecialInvoiceInfo(orderNo)
    } catch (e: Exception) {
        BaseBean<AfterSalesSpecialInvoice>().initWithException(e)
    }

    suspend fun submitInvoiceAfterSales(@FieldMap paramsMap: Map<String, String>): BaseBean<AfterSalesNoBean> = try {
        NetworkService.instance.mRetrofit.create(IAfterSalesService::class.java).submitInvoiceAfterSales(paramsMap)
    } catch (e: Exception) {
        BaseBean<AfterSalesNoBean>().initWithException(e)
    }
}