package com.ybmmarket20.network.request

import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.RebateVoucherStartActInfoBean
import com.ybmmarket20.more_account.data.initWithException
import com.ybmmarket20.network.NetworkService
import retrofit2.http.GET

/**
 *    author : 朱勇闯
 *    e-mail : <EMAIL>
 *    date   : 2025/7/11 16:16
 *    desc   :
 */

interface IRebateVoucherService {
    @GET("marketing/rebateVoucher/consumption/startActInfo")
    suspend fun getRebateVoucherInfo(): BaseBean<RebateVoucherStartActInfoBean>

}

class RebateVoucherRequest {
    suspend fun getRebateVoucherInfo(): BaseBean<RebateVoucherStartActInfoBean> = try {
        NetworkService.instance.mRetrofit.create(IRebateVoucherService::class.java)
            .getRebateVoucherInfo()
    } catch (e: Exception) {
        BaseBean<RebateVoucherStartActInfoBean>().initWithException(e)
    }
}