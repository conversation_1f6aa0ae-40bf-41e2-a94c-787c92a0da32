package com.ybmmarket20.network.request

import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.OpenAccount
import retrofit2.http.Field
import retrofit2.http.FormUrlEncoded
import retrofit2.http.POST

interface OpenAccountRequest {

    @FormUrlEncoded
    @POST("company/center/companyInfo/getAccountInfo")
    suspend fun getShopOpenAccountData(@Field("merchantId")merchantId: String, @Field("orgId")orgId: String): BaseBean<OpenAccount>
}