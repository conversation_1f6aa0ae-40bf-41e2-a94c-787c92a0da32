package com.ybmmarket20.network.request

import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.RejectRefundReason
import com.ybmmarket20.more_account.data.initWithException
import com.ybmmarket20.network.NetworkService
import retrofit2.http.Field
import retrofit2.http.FormUrlEncoded
import retrofit2.http.POST

interface RejectRefundService {

    @POST("listAuditRejectReasons")
    suspend fun getRejectRefundReason(): BaseBean<RejectRefundReason>

    @FormUrlEncoded
    @POST("auditOrderRefund")
    suspend fun auditOrderRefund(@Field("auditResult")auditType: String, @Field("refundId")refundId: String, @Field("rejectReason")rejectReason: String, @Field("images")images: String, @Field("remarks")remarks: String): BaseBean<Nothing>
}


class RejectRefundRequest {

    suspend fun getRejectRefundReason(): BaseBean<RejectRefundReason> = try {
        NetworkService.instance.mRetrofit.create(RejectRefundService::class.java).getRejectRefundReason()
    } catch (e: Exception) {
        BaseBean<RejectRefundReason>().initWithException(e)
    }

    suspend fun auditOrderRefund(auditType: String, refundId: String, rejectReason: String, images: String, remarks: String): BaseBean<Nothing> = try {
        NetworkService.instance.mRetrofit.create(RejectRefundService::class.java).auditOrderRefund(auditType, refundId, rejectReason, images, remarks)
    } catch (e: Exception) {
        BaseBean<Nothing>().initWithException(e)
    }
}