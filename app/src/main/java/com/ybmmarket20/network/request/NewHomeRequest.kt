package com.ybmmarket20.network.request

import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.SearchGiftSelectResponseBean
import com.ybmmarket20.home.newpage.bean.HomeFeedStreamResponse
import com.ybmmarket20.home.newpage.bean.HomeModulesResponse
import com.ybmmarket20.home.newpage.bean.HomeSearchContentResponse
import com.ybmmarket20.home.newpage.bean.TabBeanResponse
import com.ybmmarket20.more_account.data.initWithException
import com.ybmmarket20.network.NetworkService
import retrofit2.http.Field
import retrofit2.http.FieldMap
import retrofit2.http.FormUrlEncoded
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Query
import kotlin.coroutines.cancellation.CancellationException

/**
 * @class   NewHomeRequest
 * <AUTHOR>
 * @date  2024/5/6
 * @description  新首页请求
 */

interface INewHomeService {

	@GET("layout/getSearchHotKeyword")
	suspend fun requestSearchHotList(): BaseBean<MutableList<HomeSearchContentResponse>>

	@GET("layout/getTabList")
	suspend fun requestTabList(): BaseBean<TabBeanResponse>

	@FormUrlEncoded
	@POST("layout/getIndexData/V3")
	suspend fun requestTabModel(
			@Field("tabId") tabId: String,
			@Field("tabType") tabType: String
	): BaseBean<HomeModulesResponse>

	@FormUrlEncoded
	@POST("layout/getFeedStreamInfo")
	suspend fun requestFeedStream(
			@Field("pageNum") pageNum: String,
			@Field("pageSize") pageSize: String,
			@Field("tabId") tabId: String,
			@Field("tabType") tabType: String,
			@Field("isGuaranteed") isGuaranteed: Boolean,
			@Field("sptype") sptype: String,
			@Field("jgspid") jgspid: String,
			@Field("sid") sid: String
	): BaseBean<HomeFeedStreamResponse>

	@FormUrlEncoded
	@POST("layout/getFeedStreamInfo")
	suspend fun requestFeedStream(@FieldMap map: Map<String, String?>): BaseBean<HomeFeedStreamResponse>

}

class NewHomeRequest {

	//请求搜索热词
	suspend fun requestSearchHotList(): BaseBean<MutableList<HomeSearchContentResponse>> = try {
		NetworkService.instance.mRetrofit.create(INewHomeService::class.java).requestSearchHotList()
	} catch (e: Exception) {
		BaseBean<MutableList<HomeSearchContentResponse>>().initWithException(e)
	}

	//请求tabList
	suspend fun requestTabList(): BaseBean<TabBeanResponse> = try {
		NetworkService.instance.mRetrofit.create(INewHomeService::class.java).requestTabList()
	} catch (e: Exception) {
		BaseBean<TabBeanResponse>().initWithException(e)
	}

	//请求每个tab中的各个模块数据
	suspend fun requestTabModel(tabId: String,tabType: String): BaseBean<HomeModulesResponse> = try {
		NetworkService.instance.mRetrofit.create(INewHomeService::class.java).requestTabModel(tabId, tabType)
	} catch (e:CancellationException){
		e.printStackTrace()
		BaseBean<HomeModulesResponse>()
	} catch (e: Exception) {
		BaseBean<HomeModulesResponse>().initWithException(e)
	}

	//请求每个tab下的feed流
	suspend fun requestHomeFeedStream(tabId: String,tabType: String,pageNum: String,pageSize: String,isGuaranteed:Boolean, sptype: String?, jgspid: String?, sid: String?): BaseBean<HomeFeedStreamResponse> = try {
		NetworkService.instance.mRetrofit.create(INewHomeService::class.java).requestFeedStream(pageNum,pageSize,tabId, tabType,isGuaranteed, sptype ?: "", jgspid ?: "", sid ?: "")
	} catch (e:CancellationException){
		e.printStackTrace()
		BaseBean<HomeFeedStreamResponse>()
	} catch (e: Exception) {
		BaseBean<HomeFeedStreamResponse>().initWithException(e)
	}

	//请求每个tab下的feed流
	suspend fun requestHomeFeedStream(map: Map<String, String>): BaseBean<HomeFeedStreamResponse> = try {
		NetworkService.instance.mRetrofit.create(INewHomeService::class.java).requestFeedStream(map)
	} catch (e:CancellationException){
		e.printStackTrace()
		BaseBean<HomeFeedStreamResponse>()
	} catch (e: Exception) {
		BaseBean<HomeFeedStreamResponse>().initWithException(e)
	}
}