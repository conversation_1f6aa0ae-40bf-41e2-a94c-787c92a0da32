package com.ybmmarket20.network.request

import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.SearchStartRecommend
import com.ybmmarket20.more_account.data.initWithException
import com.ybmmarket20.network.NetworkService
import com.ybmmarket20.utils.SpUtil
import retrofit2.http.Field
import retrofit2.http.FormUrlEncoded
import retrofit2.http.POST
import java.lang.Exception

interface SearchStartRecommendService {

    @FormUrlEncoded
    @POST("recommend/v1/hot")
    suspend fun getSearchStartRecommendData(@Field("merchantId")merchantId: String): BaseBean<SearchStartRecommend>?
}

class SearchStartRecommendRequest {
    suspend fun getSearchStartRecommendData(): BaseBean<SearchStartRecommend>? = try {
        NetworkService.instance.mRetrofit.create(SearchStartRecommendService::class.java).getSearchStartRecommendData(SpUtil.getMerchantid())
    } catch (e: Exception) {
        BaseBean<SearchStartRecommend>().initWithException(e)
    }
}