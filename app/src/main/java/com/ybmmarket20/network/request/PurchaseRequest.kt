package com.ybmmarket20.network.request

import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.PurchaseAmountBean
import com.ybmmarket20.bean.PurchaseBean
import com.ybmmarket20.more_account.data.initWithException
import com.ybmmarket20.network.NetworkService
import retrofit2.http.Field
import retrofit2.http.FormUrlEncoded
import retrofit2.http.POST

/**
 * 采购对账单
 */
interface PurchaseService {
    /**
     * 根据日期获取采购对账单金额
     */
    @FormUrlEncoded
    @POST("purchaseOrder/query")
    suspend fun getPurchaseAllAmount(@Field("year")year: String, @Field("month")month: String): BaseBean<PurchaseAmountBean>

    /**
     * 获取采购对账单列表
     */
    @FormUrlEncoded
    @POST("purchaseOrder/shopList")
    suspend fun getPurchaseList(@Field("year")year: String, @Field("month")month: String, @Field("pageNum")pageNum: String, @Field("pageSize")pageSize: String): BaseBean<PurchaseBean>
}

class PurchaseRequest {

    suspend fun getPurchaseAllAmount(year: String, month: String): BaseBean<PurchaseAmountBean> = try {
        NetworkService.instance.mRetrofit.create(PurchaseService::class.java).getPurchaseAllAmount(year, month)
    } catch (e: Exception) {
        BaseBean<PurchaseAmountBean>().initWithException(e)
    }

    suspend fun getPurchaseList(year: String, month: String, pageNum: String, pageSize: String): BaseBean<PurchaseBean> = try {
        NetworkService.instance.mRetrofit.create(PurchaseService::class.java).getPurchaseList(year, month, pageNum, pageSize)
    } catch (e: Exception) {
        BaseBean<PurchaseBean>().initWithException(e)
    }

}