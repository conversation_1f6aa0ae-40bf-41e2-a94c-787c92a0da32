package com.ybmmarket20.network.request

import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.RefundReason
import retrofit2.http.Field
import retrofit2.http.FormUrlEncoded
import retrofit2.http.POST

/**
 * 退款原因
 */
interface RefundReasonRequest {

    @FormUrlEncoded
    @POST("refund/queryRefundReasonV2")
    suspend fun getRefundReasonList(@Field("orderNo")orderNo: String, @Field("merchantId")merchantId: String, @Field("refundOrderDetailList")refundOrderDetailList: String): BaseBean<MutableList<RefundReason>>
}