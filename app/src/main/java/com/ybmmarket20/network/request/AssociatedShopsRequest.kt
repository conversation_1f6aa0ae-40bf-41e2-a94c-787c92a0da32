package com.ybmmarket20.network.request

import com.ybmmarket20.bean.AddShopInfoBean
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.CancelAssociatedPositionBean
import com.ybmmarket20.bean.CheckOcrStatusBean
import com.ybmmarket20.bean.CheckSubmitStatusBean
import com.ybmmarket20.bean.SelectLoginShopInfoWithPage
import com.ybmmarket20.bean.SubmitOcrBean
import com.ybmmarket20.more_account.data.initWithException
import com.ybmmarket20.network.NetworkService
import retrofit2.http.Field
import retrofit2.http.FieldMap
import retrofit2.http.FormUrlEncoded
import retrofit2.http.Header
import retrofit2.http.POST

/**
 * 通过用户id查询已关联的店铺
 */
interface IAssociatedShopsRequest {
    @FormUrlEncoded
    @POST("account/merchantList")
    suspend fun queryAssociatedShops(@Field("pageNo")pageNo: String, @Field("pageSize")pageSize: String, @Field("merchantId") merchantIdBody: String ,@Header("merchantId")merchantId: String): BaseBean<SelectLoginShopInfoWithPage>

    @FormUrlEncoded
    @POST("delRelShop")
    suspend fun cancelAssociateMerchant(@Field("merchantId")merchantId: String): BaseBean<CancelAssociatedPositionBean>

    @FormUrlEncoded
    @POST("merchant/getBusinessInfo")
    suspend fun getAddShopInfo(@FieldMap params: Map<String, String>): BaseBean<AddShopInfoBean>

    @FormUrlEncoded
    @POST("data/getBusinessAndGeneral")
    suspend fun submitOct(@FieldMap params: Map<String, String>): BaseBean<SubmitOcrBean>

    @FormUrlEncoded
    @POST("data/getBusinessAndGeneralData")
    suspend fun checkOctStatus(@Field("dataKey") dataKey: String, @Field("type")type: String): BaseBean<CheckOcrStatusBean>

    @FormUrlEncoded
    @POST("merchant/getMerchantStatus")
    suspend fun checkSubmitStatus(@Field("merchantId") merchantId: String): BaseBean<CheckSubmitStatusBean>
}
class AssociatedShopsRequest {

    /**
     * 获取关联的店铺
     */
    suspend fun queryAssociatedShops(pageNo: String, pageSize: String) = try {
        NetworkService.instance.mRetrofit.create(IAssociatedShopsRequest::class.java).queryAssociatedShops(pageNo, pageSize, "", "")
    } catch (e: Exception) {
        BaseBean<SelectLoginShopInfoWithPage>().initWithException(e)
    }

    /**
     * 取消关联
     */
    suspend fun cancelAssociateMerchant(merchantId: String): BaseBean<CancelAssociatedPositionBean> = try {
        NetworkService.instance.mRetrofit.create(IAssociatedShopsRequest::class.java).cancelAssociateMerchant(merchantId)
    } catch (e: Exception) {
        BaseBean<CancelAssociatedPositionBean>().initWithException(e)
    }

    /**
     * 获取添加店铺的店铺信息
     */
    suspend fun getAddShopInfo(params: Map<String, String>): BaseBean<AddShopInfoBean> = try {
        NetworkService.instance.mRetrofit.create(IAssociatedShopsRequest::class.java).getAddShopInfo(params)
    } catch (e: Exception) {
        BaseBean<AddShopInfoBean>().initWithException(e)
    }

    /**
     * 提交oct
     */
    suspend fun submitOct(params: Map<String, String>): BaseBean<SubmitOcrBean> = try {
        NetworkService.instance.mRetrofit.create(IAssociatedShopsRequest::class.java).submitOct(params)
    } catch (e: Exception) {
        BaseBean<SubmitOcrBean>().initWithException(e)
    }

    /**
     * 检查ocr状态
     */
    suspend fun checkOctStatus(dataKey: String, type: String): BaseBean<CheckOcrStatusBean> = try {
        NetworkService.instance.mRetrofit.create(IAssociatedShopsRequest::class.java).checkOctStatus(dataKey, type)
    } catch (e: Exception) {
        BaseBean<CheckOcrStatusBean>().initWithException(e)
    }

    /**
     * 检查提交状态
     */
    suspend fun checkSubmitStatus(merchantId: String): BaseBean<CheckSubmitStatusBean> = try {
        NetworkService.instance.mRetrofit.create(IAssociatedShopsRequest::class.java).checkSubmitStatus(merchantId)
    } catch (e: Exception) {
        BaseBean<CheckSubmitStatusBean>().initWithException(e)
    }
}