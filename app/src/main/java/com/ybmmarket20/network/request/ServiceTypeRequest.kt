package com.ybmmarket20.network.request

import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.ServiceType
import retrofit2.http.Field
import retrofit2.http.FormUrlEncoded
import retrofit2.http.POST

/**
 * 退款服务类型
 */
interface ServiceTypeRequest {

    @FormUrlEncoded
    @POST("refund/queryRefundAfterSales")
    suspend fun getServiceType(@Field("orderNo")orderNo: String): BaseBean<MutableList<ServiceType>>
}