package com.ybmmarket20.network.request

import com.ybmmarket20.bean.*
import com.ybmmarket20.bean.ReceiveMoneyAccountInfo
import com.ybmmarket20.more_account.data.initWithException
import com.ybmmarket20.network.NetworkService
import retrofit2.http.*
import kotlin.Exception

/**
 * 账户信息
 */
interface IAccountInfoService {

    @FormUrlEncoded
    @POST("order/queryAccountInfo")
    suspend fun getReceiveMoneyAccountInfo(@Field("orderNo") orderNo: String): BaseBean<ReceiveMoneyAccountInfo>?

    @FormUrlEncoded
    @POST("v2/login")
    @Headers("Remove-TOKEN:1")
    suspend fun login(@Field("mobileNumber")mobileNumber: String, @Field("password")password: String,@Field("loginType")loginType:String? = null): BaseBean<LoginInfo>

    @FormUrlEncoded
    @POST("wechat/accessToken")
    suspend fun wechatOauth(@Field("code")tempToken: String): BaseBean<WechatOauthInfo>

    @FormUrlEncoded
    @POST("wechat/login")
    @Headers("Remove-TOKEN:1")
    suspend fun loginByWechat(@FieldMap data: Map<String,String?>): BaseBean<LoginInfo>

    /**
     * 账号是否绑定了微信状态查询
     */
    @GET("account/wechat/bindStatus")
    suspend fun accountBindWechatStatus(): BaseBean<RegisterStatusBean>

    /**
     * 微信是否绑定了账号状态查询
     */
    @FormUrlEncoded
    @POST("account/wechat/bind/status")
    suspend fun wechatBindAccountStatus(@Field("accessToken")accessToken:String,@Field("openid")openid:String): BaseBean<RegisterStatusBean>

    @POST("initMerchantInfo")
    suspend fun getMerchantInfo(@Header("merchantId")merchantId: String): BaseBean<LoginMerchantInfo>

    @POST("initMerchantInfo")
    suspend fun getMerchantInfo(@Header("merchantId")merchantId: String, @Header("accountId")accountId: String, @Header("token")token: String): BaseBean<LoginMerchantInfo>

    @POST("getAccountId")
    suspend fun getAccountId(): BaseBean<AccountIdBean>

    @GET("loginAgreement/getLoginAgreement")
    suspend fun getLoginAgreement(): BaseBean<LoginAgreementBean>
    @FormUrlEncoded
    @POST("loginAgreement/addLog")
    suspend fun saveLoginAgreement(@FieldMap paramsMap: Map<String, String>): BaseBean<EmptyBean>
    @FormUrlEncoded
    @POST("account/wechat/customer/bind/v2")
    suspend fun bindWechatCustomer(@Field("code") code:String): BaseBean<EmptyBean>
}


class AccountInfoRequest {

    suspend fun getReceiveMoneyAccountInfo(orderNo: String): BaseBean<ReceiveMoneyAccountInfo>? = try {
        NetworkService.instance.mRetrofit.create(IAccountInfoService::class.java).getReceiveMoneyAccountInfo(orderNo)
    } catch (e: Exception) {
        BaseBean<ReceiveMoneyAccountInfo>().initWithException(e)
    }

    /**
     * 登陆
     */
    suspend fun login(mobileNumber: String, password: String,loginType:String? = null): BaseBean<LoginInfo> = try {
        if(loginType.isNullOrEmpty() || LoginInfo.LOGIN_TYPE_DEFAULT == loginType){
            NetworkService.instance.mRetrofit.create(IAccountInfoService::class.java).login(mobileNumber, password)
        }else{
            NetworkService.instance.mRetrofit.create(IAccountInfoService::class.java).login(mobileNumber, password,loginType)
        }
    } catch (e: Exception) {
        BaseBean<LoginInfo>().initWithException(e)
    }

    /**
     * 查询账号是否绑定了微信状态
     */
    suspend fun accountBindWechatStatus(): BaseBean<RegisterStatusBean> = try {
        NetworkService.instance.mRetrofit.create(IAccountInfoService::class.java).accountBindWechatStatus()
    } catch (e: Exception) {
        BaseBean<RegisterStatusBean>().initWithException(e)
    }

    /**
     * 查询微信是否绑定了账号状态
     */
    suspend fun wechatBindAccountStatus(accessToken:String,openid:String): BaseBean<RegisterStatusBean> = try {
        NetworkService.instance.mRetrofit.create(IAccountInfoService::class.java).wechatBindAccountStatus(accessToken,openid)
    } catch (e: Exception) {
        BaseBean<RegisterStatusBean>().initWithException(e)
    }

    /**
     * 微信授权accessToken
     */
    suspend fun wechatOauth(tempToken: String): BaseBean<WechatOauthInfo> = try {
        NetworkService.instance.mRetrofit.create(IAccountInfoService::class.java).wechatOauth(tempToken)
    } catch (e: Exception) {
        BaseBean<WechatOauthInfo>().initWithException(e)
    }

    /**
     * 微信登录
     */
    suspend fun loginByWechat(params: Map<String,String?>): BaseBean<LoginInfo> = try {
        NetworkService.instance.mRetrofit.create(IAccountInfoService::class.java).loginByWechat(params)
    } catch (e: Exception) {
        BaseBean<LoginInfo>().initWithException(e)
    }

    /**
     * 获取商户信息
     */
    suspend fun getMerchantInfo(merchantId: String): BaseBean<LoginMerchantInfo> = try {
        NetworkService.instance.mRetrofit.create(IAccountInfoService::class.java).getMerchantInfo(merchantId)
    } catch (e: Exception) {
        BaseBean<LoginMerchantInfo>().initWithException(e)
    }

    /**
     * 获取商户信息
     */
    suspend fun getMerchantInfo(merchantId: String, accountId: String, token: String): BaseBean<LoginMerchantInfo> = try {
        NetworkService.instance.mRetrofit.create(IAccountInfoService::class.java).getMerchantInfo(merchantId, accountId, token)
    } catch (e: Exception) {
        BaseBean<LoginMerchantInfo>().initWithException(e)
    }

    suspend fun getAccountId(): BaseBean<AccountIdBean> = try {
        NetworkService.instance.mRetrofit.create(IAccountInfoService::class.java).getAccountId()
    } catch (e: Exception) {
        BaseBean<AccountIdBean>().initWithException(e)
    }
    suspend fun getLoginAgreement(): BaseBean<LoginAgreementBean> = try {
        NetworkService.instance.mRetrofit.create(IAccountInfoService::class.java).getLoginAgreement()
    } catch (e: Exception) {
        BaseBean<LoginAgreementBean>().initWithException(e)
    }
    suspend fun saveLoginAgreement(paramsMap: Map<String, String>): BaseBean<EmptyBean> = try {
        NetworkService.instance.mRetrofit.create(IAccountInfoService::class.java).saveLoginAgreement(paramsMap)
    } catch (e: Exception) {
        BaseBean<EmptyBean>().initWithException(e)
    }
    suspend fun bindWechatCustomer(code: String): BaseBean<EmptyBean> = try {
        NetworkService.instance.mRetrofit.create(IAccountInfoService::class.java).bindWechatCustomer(code)
    } catch (e: Exception) {
        BaseBean<EmptyBean>().initWithException(e)
    }
}