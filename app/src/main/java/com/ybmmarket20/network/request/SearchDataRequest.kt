package com.ybmmarket20.network.request

import com.ybmmarket20.bean.*
import com.ybmmarket20.common.BaseResponse
import com.ybmmarket20.more_account.data.initWithException
import com.ybmmarket20.network.NetworkService
import com.ybmmarket20.utils.SpUtil
import retrofit2.http.Field
import retrofit2.http.FieldMap
import retrofit2.http.FormUrlEncoded
import retrofit2.http.POST

interface SearchDataService {

    @FormUrlEncoded
    @POST("recommend/v2/page")
    suspend fun getRecommendSearchDataService(@FieldMap map: Map<String, String>): BaseBean<RefreshWrapperPagerBean<RowsBean>>


    @FormUrlEncoded
    @POST("recommend/v2/page")
    suspend fun getRecommendOftenBuyService(@FieldMap map: Map<String, String>): BaseBean<RefreshWrapperPagerBean<OftenBuyRecommendRowsBean>>

    @FormUrlEncoded
    @POST("search/v1/listWantSearchWords")
    suspend fun getSearchFind(@Field("merchantId") merchantId: String): BaseBean<SearchFindBean>

    @FormUrlEncoded
    @POST("voucher/receiveVoucher")
    suspend fun getVoucher(@FieldMap pare: Map<String, String>): BaseBean<EmptyBean>?
}


// 获取推荐
class SearchDataRequest {
    suspend fun getRecommendSearchDataRequest(mapParams: Map<String, String>): BaseBean<RefreshWrapperPagerBean<RowsBean>> = try {
        NetworkService.instance.mRetrofit.create(SearchDataService::class.java).getRecommendSearchDataService(mapParams)
    } catch (e: Exception) {
        BaseBean<RefreshWrapperPagerBean<RowsBean>>().initWithException(e)
    }
    suspend fun getRecommendOftenBuyService(mapParams: Map<String, String>): BaseBean<RefreshWrapperPagerBean<OftenBuyRecommendRowsBean>> = try {
        NetworkService.instance.mRetrofit.create(SearchDataService::class.java).getRecommendOftenBuyService(mapParams)
    } catch (e: Exception) {
        BaseBean<RefreshWrapperPagerBean<OftenBuyRecommendRowsBean>>().initWithException(e)
    }

    /**
     * 搜索发现
     */
    suspend fun getSearchFind(): BaseBean<SearchFindBean> = try {
        NetworkService.instance.mRetrofit.create(SearchDataService::class.java).getSearchFind(SpUtil.getMerchantid())
    } catch (e: Exception) {
        BaseBean<SearchFindBean>().initWithException(e)
    }
    /**
     *  领取优惠券
     */
    suspend fun getVoucher(paramMap: Map<String, String>): BaseBean<EmptyBean>? = try {
        NetworkService.instance.mRetrofit.create(CartService::class.java).getVoucher(paramMap)
    } catch (e: Exception) {
        BaseBean<EmptyBean>().initWithException(e)
    }
    /**
     *  查询消费返活动标签
     */
    suspend fun getStartActTags(): BaseBean<RebateVoucherStartActInfoBean> = try {
        NetworkService.instance.mRetrofit.create(CartService::class.java).getStartActTags()
    } catch (e: Exception) {
        BaseBean<RebateVoucherStartActInfoBean>().initWithException(e)
    }
}