package com.ybmmarket20.network.request

import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.PdfUrlBean
import com.ybmmarket20.more_account.data.initWithException
import com.ybmmarket20.network.NetworkService
import retrofit2.http.Field
import retrofit2.http.FormUrlEncoded
import retrofit2.http.POST

interface OrderDetailService {

//    /**
//     * 获取仓库交接单PDF url
//     */
//    @FormUrlEncoded
//    @POST("queryHandoverOrderUrl")
//    suspend fun getWarehouseHandoverFormPdfUrl(@Field("orderNo") orderNo: String): BaseBean<PdfUrlBean>
//
//    /**
//     * 获取电子出库单PDF url
//     */
//    @FormUrlEncoded
//    @POST("queryOutOrderUrl")
//    suspend fun getElectronDeliveryFormPdfUrl(@Field("orderNo") orderNo: String): BaseBean<PdfUrlBean>

    /**
     * 获取仓库交接单和获取电子出库单PDF url
     */
    @FormUrlEncoded
    @POST("queryOutOrderPdfUrl")
    suspend fun getWarehouseHandoverAndElectronDeliveryPdfUrl(@Field("orderNo") orderNo: String, @Field("docType") formType: String, @Field("branchCode") branchCode: String): BaseBean<MutableList<String>>

    @FormUrlEncoded
    @POST("afterSales/guide")
    suspend fun getAfterSalesTips(@Field("orderNo")orderNo: String, @Field("orgId")orgId: String): BaseBean<String>
}

class OrderDetailRequest {

    /**
     * 获取仓库交接单和获取电子出库单pdfUrl
     */
    suspend fun getFormPdfUrl(orderNo: String, formType: String, branchCode: String): BaseBean<MutableList<String>> = try {
        NetworkService.instance.mRetrofit.create(OrderDetailService::class.java).getWarehouseHandoverAndElectronDeliveryPdfUrl(orderNo, formType, branchCode)
    } catch (e: Exception) {
        BaseBean<MutableList<String>>().initWithException(e)
    }

    /**
     * 获取售后资质文本
     */
    suspend fun getAfterSalesTips(orderNo: String, orgId: String): BaseBean<String> = try {
        NetworkService.instance.mRetrofit.create(OrderDetailService::class.java).getAfterSalesTips(orderNo, orgId)
    } catch (e: Exception) {
        BaseBean<String>().initWithException(e)
    }


}