package com.ybmmarket20.network.request

import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.HomeAptitudeStatus
import com.ybmmarket20.more_account.data.initWithException
import com.ybmmarket20.network.NetworkService
import com.ybmmarket20.utils.SpUtil
import retrofit2.http.Field
import retrofit2.http.FormUrlEncoded
import retrofit2.http.POST

interface IHomeAptitudeService {

    @FormUrlEncoded
    @POST("licenseAudit/validity/remind")
    suspend fun getHomeAptitudeStatus(@Field("merchantId")merchantId: String): BaseBean<HomeAptitudeStatus>

}

class HomeAptitudeRequest {

    /**
     * 获取首页弹窗状态
     */
    suspend fun getHomeAptitudeStatus(): BaseBean<HomeAptitudeStatus> = try {
        NetworkService.instance.mRetrofit.create(IHomeAptitudeService::class.java).getHomeAptitudeStatus(SpUtil.getMerchantid())
    } catch (e: Exception) {
        BaseBean<HomeAptitudeStatus>().initWithException(e)
    }

}