package com.ybmmarket20.network.request
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.CouponsTipsResponse
import com.ybmmarket20.bean.PurchaseSwitchBean
import com.ybmmarket20.more_account.data.initWithException
import com.ybmmarket20.network.NetworkService
import retrofit2.http.Field
import retrofit2.http.FormUrlEncoded
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Query

interface SwitchService {

    @FormUrlEncoded
    @POST("purchaseOrder/querySwitch")
    suspend fun purchaseOrderSwitch(@Field("merchantId") merchantId: String): BaseBean<PurchaseSwitchBean>

    @GET("layout/getSceneData")
    suspend fun getSceneData(@Query("merchantId") merchantId: String, @Query("sceneType") sceneType: String = "8"): BaseBean<CouponsTipsResponse>
}

class SwitchRequest {

    suspend fun purchaseOrderSwitch(merchantId: String): BaseBean<PurchaseSwitchBean> = try {
        NetworkService.instance.mRetrofit.create(SwitchService::class.java).purchaseOrderSwitch(merchantId)
    } catch (e: Exception) {
        BaseBean<PurchaseSwitchBean>().initWithException(e)
    }

    suspend fun getSceneData(merchantId: String): BaseBean<CouponsTipsResponse> = try {
        NetworkService.instance.mRetrofit.create(SwitchService::class.java).getSceneData(merchantId)
    } catch (e: Exception) {
        BaseBean<CouponsTipsResponse>().initWithException(e)
    }


}