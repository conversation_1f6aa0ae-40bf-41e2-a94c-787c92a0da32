package com.ybmmarket20.network.request

import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.OftenBuyItemData
import com.ybmmarket20.bean.RowsBean
import com.ybmmarket20.bean.SearchResultBean
import com.ybmmarket20.more_account.data.initWithException
import com.ybmmarket20.network.NetworkService
import retrofit2.http.Field
import retrofit2.http.FieldMap
import retrofit2.http.FormUrlEncoded
import retrofit2.http.POST

/**
 * 搜索接口
 */

interface SearchService {
    @FormUrlEncoded
    @POST("search/v1/productList")
    suspend fun searchGoods(@FieldMap params: Map<String, String>): BaseBean<SearchResultBean>

    @FormUrlEncoded
    @POST("recommend/v2/often/purchase")
    suspend fun searchOftenBuyList(@FieldMap params: Map<String, String>): BaseBean<OftenBuyItemData>
}

class SearchRequest {

    /**
     * 搜索随心拼商品
     */
    suspend fun searchSpellGroupRecommendGoods(params: Map<String, String>): BaseBean<SearchResultBean> = try {
        NetworkService.instance.mRetrofit.create(SearchService::class.java).searchGoods(params)
    } catch (e: Exception) {
        BaseBean<SearchResultBean>().initWithException(e)
    }

    /**
     * 常购清单列表
     */
    suspend fun searchOftenBuyList(params: Map<String, String>) = try {
        NetworkService.instance.mRetrofit.create(SearchService::class.java).searchOftenBuyList(params)
    } catch (e: Exception) {
        BaseBean<OftenBuyItemData>().initWithException(e)
    }
}