package com.ybmmarket20.network.request

import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.CouponInfoBean
import com.ybmmarket20.bean.CouponRelatedGoodsPriceBean
import com.ybmmarket20.bean.GiftSubTotalResponseBean
import com.ybmmarket20.bean.RowsBean
import com.ybmmarket20.bean.SearchFilterBean
import com.ybmmarket20.bean.SearchGiftSelectResponseBean
import com.ybmmarket20.bean.SearchResultBean
import com.ybmmarket20.bean.SearchResultOPBean
import com.ybmmarket20.more_account.data.initWithException
import com.ybmmarket20.network.NetworkService
import retrofit2.http.FieldMap
import retrofit2.http.FormUrlEncoded
import retrofit2.http.POST
import kotlin.coroutines.cancellation.CancellationException


interface IGiftSelectGoodsService {

    @FormUrlEncoded
    @POST("promotion/promo/getMainProductList")
    suspend fun getGiftGoodsList(@FieldMap params: Map<String, String>): BaseBean<SearchGiftSelectResponseBean>

    @FormUrlEncoded
    @POST("promotion/promo/goGatherOrdersStatistics")
    suspend fun getGiftSubTotal(@FieldMap params: Map<String, String>): BaseBean<GiftSubTotalResponseBean>
}

class GiftSelectGoodsRequest {
    /**
     * 获取赠品池商品
     */
    suspend fun getGiftGoodsList(@FieldMap params: Map<String, String>): BaseBean<SearchGiftSelectResponseBean> = try {
        NetworkService.instance.mRetrofit.create(IGiftSelectGoodsService::class.java).getGiftGoodsList(params)
    } catch (e:CancellationException){
        e.printStackTrace()
        BaseBean<SearchGiftSelectResponseBean>()
    } catch (e: Exception) {
        BaseBean<SearchGiftSelectResponseBean>().initWithException(e)
    }


    suspend fun getGiftSubTotal(@FieldMap params: Map<String, String>): BaseBean<GiftSubTotalResponseBean> = try {
        NetworkService.instance.mRetrofit.create(IGiftSelectGoodsService::class.java).getGiftSubTotal(params)
    } catch (e:CancellationException){
        e.printStackTrace()
        BaseBean<GiftSubTotalResponseBean>()
    } catch (e: Exception) {
        BaseBean<GiftSubTotalResponseBean>().initWithException(e)
    }
}