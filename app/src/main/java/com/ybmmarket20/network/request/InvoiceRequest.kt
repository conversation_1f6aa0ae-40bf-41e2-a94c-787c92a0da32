package com.ybmmarket20.network.request

import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.InvoiceListBaseBean
import retrofit2.http.Field
import retrofit2.http.FormUrlEncoded
import retrofit2.http.POST

/**
 * 发票相关
 */
interface InvoiceRequest {
    @FormUrlEncoded
    @POST("invoice/queryInvoice")
    suspend fun getInvoiceInfo(@Field("merchantId")merchantId: String, @Field("orderId")orderId: String): BaseBean<InvoiceListBaseBean>

    @FormUrlEncoded
    @POST("orders/sendInvoiceEmail")
    suspend fun sendMail(@Field("merchantId")merchantId: String, @Field("orderId")orderId: String, @Field("email", encoded = true)email: String): BaseBean<Any>
}
