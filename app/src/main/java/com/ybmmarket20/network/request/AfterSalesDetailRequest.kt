package com.ybmmarket20.network.request

import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.ImPackUrlBean
import com.ybmmarket20.bean.aftersales.AfterSalesDetailBean
import com.ybmmarket20.bean.aftersales.AfterSalesDetailStatusBean
import com.ybmmarket20.more_account.data.initWithException
import com.ybmmarket20.network.NetworkService
import retrofit2.http.Field
import retrofit2.http.FieldMap
import retrofit2.http.FormUrlEncoded
import retrofit2.http.POST
import kotlin.Exception

interface IAfterSalesDetailService {

    @FormUrlEncoded
    @POST("afterSales/detail")
    suspend fun getAfterSalesDetailInfo(@Field("afterSalesNo")afterSalesNo: String): BaseBean<AfterSalesDetailBean>

    @FormUrlEncoded
    @POST("afterSales/queryProcessState")
    suspend fun getAfterSalesDetailStatus(@Field("afterSalesNo")afterSalesNo: String): BaseBean<AfterSalesDetailStatusBean>

    @FormUrlEncoded
    @POST("messageCenter/getIMPackUrl")
    suspend fun getIMPackUrl(@Field("isThirdCompany")isThirdCompany: String): BaseBean<ImPackUrlBean>

    @FormUrlEncoded
    @POST("afterSales/saveOperate")
    suspend fun afterSalesOperation(@FieldMap paramsMap: Map<String, String>): BaseBean<Any>
}

class AfterSalesDetailRequest {

    suspend fun getAfterSalesDetailInfo(afterSalesNo: String): BaseBean<AfterSalesDetailBean> = try {
        NetworkService.instance.mRetrofit.create(IAfterSalesDetailService::class.java).getAfterSalesDetailInfo(afterSalesNo)
    } catch (e: Exception) {
        BaseBean<AfterSalesDetailBean>().initWithException(e)
    }

    suspend fun getAfterSalesDetailStatus(afterSalesNo: String): BaseBean<AfterSalesDetailStatusBean> = try {
        NetworkService.instance.mRetrofit.create(IAfterSalesDetailService::class.java).getAfterSalesDetailStatus(afterSalesNo)
    } catch (e: Exception) {
        BaseBean<AfterSalesDetailStatusBean>().initWithException(e)
    }

    suspend fun getIMPackUrl(@Field("isThirdCompany")isThirdCompany: String): BaseBean<ImPackUrlBean> = try {
        NetworkService.instance.mRetrofit.create(IAfterSalesDetailService::class.java).getIMPackUrl(isThirdCompany)
    } catch (e: Exception) {
        BaseBean<ImPackUrlBean>().initWithException(e)
    }

    suspend fun afterSalesOperation(paramsMap: Map<String, String>): BaseBean<Any> = try {
        NetworkService.instance.mRetrofit.create(IAfterSalesDetailService::class.java).afterSalesOperation(paramsMap)
    } catch (e: Exception) {
        BaseBean<Any>().initWithException(e)
    }
}