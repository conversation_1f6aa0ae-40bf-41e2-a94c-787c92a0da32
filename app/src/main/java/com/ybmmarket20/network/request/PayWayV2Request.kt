package com.ybmmarket20.network.request

import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.PayConfigBean
import com.ybmmarket20.bean.PingAnPayChannelTipsBean
import com.ybmmarket20.bean.SignBankBean
import com.ybmmarket20.more_account.data.initWithException
import com.ybmmarket20.network.NetworkService
import com.ybmmarket20.utils.SpUtil
import retrofit2.http.Field
import retrofit2.http.FieldMap
import retrofit2.http.FormUrlEncoded
import retrofit2.http.POST

interface IPayWayV2Service {

    @FormUrlEncoded
    @POST("paymentlist")
    suspend fun paymentList(
            @FieldMap params: Map<String, String>
    ): BaseBean<PayConfigBean>

    @FormUrlEncoded
    @POST("pinganaccount/queryPingAnCreditBalance")
    suspend fun getPingAnPayChannelTips(@FieldMap params: Map<String, String>): BaseBean<PingAnPayChannelTipsBean>

    @FormUrlEncoded
    @POST("card/queryOneKeySignBanks")
    suspend fun queryOneKeySignBankList(@Field("merchantId") merchantId: String = SpUtil.getMerchantid()): BaseBean<SignBankBean>
}

class PayWayV2Request {

    /**
     * 获取支付方式列表
     */
    suspend fun paymentList(
        merchantId: String,
        orderId: String,
        payRoute: String,
        orderNo: String,
        payCode: String,
        cardId: String,
        virtualCardId: String,
        amount:String,
        rechargeType:String,

    ): BaseBean<PayConfigBean> = try {
        val params = hashMapOf(
                "merchantId" to merchantId,
                "orderId" to orderId,
                "payRoute" to payRoute,
                "orderNo" to orderNo,
                "paycode" to payCode,
                "cardId" to cardId,
                "virtualCardId" to virtualCardId
        )
        if (rechargeType == "2"){
            params["amount"] = amount
            params["rechargeType"] = rechargeType

        }
        NetworkService.instance.mRetrofit.create(IPayWayV2Service::class.java)
            .paymentList(params)
    } catch (e: Exception) {
        BaseBean<PayConfigBean>().initWithException(e)
    }

    /**
     * 获取平安支付渠道可用额度
     */
    suspend fun getPingAnPayChannelTips(): BaseBean<PingAnPayChannelTipsBean> = try {
        NetworkService.instance.mRetrofit.create(IPayWayV2Service::class.java)
            .getPingAnPayChannelTips(mapOf("merchantId" to SpUtil.getMerchantid()))
    } catch (e: Exception) {
        BaseBean<PingAnPayChannelTipsBean>().initWithException(e)
    }

    /**
     * 获取绑定的银行卡列表
     */
    suspend fun queryOneKeySignBankList(): BaseBean<SignBankBean> = try {
        NetworkService.instance.mRetrofit.create(IPayWayV2Service::class.java)
            .queryOneKeySignBankList()
    } catch (e: Exception) {
        BaseBean<SignBankBean>().initWithException(e)
    }
}