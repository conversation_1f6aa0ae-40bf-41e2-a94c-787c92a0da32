package com.ybmmarket20.network.request

import com.ybmmarket20.bean.*
import com.ybmmarket20.more_account.data.initWithException
import com.ybmmarket20.network.NetworkService
import retrofit2.http.Field
import retrofit2.http.FormUrlEncoded
import retrofit2.http.POST

/**
 * 我的页面常用工具
 */

interface IMine2CommonToolsService {
    @FormUrlEncoded
    @POST("getCommonTools/V2")
    suspend fun getCommonToolsV2(@Field("merchantId") merchantId: String): BaseBean<List<UseToolsBean>>

    @FormUrlEncoded
    @POST("orders/findNumGroupByStatus")
    suspend fun getOrdersBubbleCount(@Field("merchantId") merchantId: String): BaseBean<OrderStatusNumber>

    @FormUrlEncoded
    @POST("layout/exhibitionPosition")
    suspend fun getBoothData(@Field("sceneType") sceneType: String): BaseBean<BoothData>

    @FormUrlEncoded
    @POST("pinganaccount/preApply")
    suspend fun getPingAnPrePay(@Field("merchantId") merchantId: String): BaseBean<PingAnPrePay>

    @FormUrlEncoded
    @POST("finance/preApplay")
    suspend fun getJDPrePayUrl(@Field("merchantId") merchantId: String): BaseBean<JDPrePay>

    @FormUrlEncoded
    @POST("finance/jd/login")
    suspend fun jdLogin(
        @Field("merchantId") merchantId: String,
        @Field("url") url: String
    ): BaseBean<String>

    @FormUrlEncoded
    @POST("abchinaLoan/preApply")
    suspend fun getNongH5Url(@Field("merchantId") merchantId: String): BaseBean<PayNongData>

    @FormUrlEncoded
    @POST("virtual/gold/getRechargeDiscount")
    suspend fun getShoppingGoldRechargeBean(@Field("merchantId") merchantId: String): BaseBean<ShoppingGoldRechargeBean>
}

class Mine2CommonToolsRequest {

    /**
     * 常用工具
     */
    suspend fun getCommonTools(merchantId: String): BaseBean<List<UseToolsBean>> = try {
        NetworkService.instance.mRetrofit.create(IMine2CommonToolsService::class.java).getCommonToolsV2(merchantId)
    } catch (e: Exception) {
        BaseBean<List<UseToolsBean>>().initWithException(e)
    }

    /**
     * 获取订单气泡数量
     */
    suspend fun getOrdersBubbleCount(merchantId: String): BaseBean<OrderStatusNumber> = try {
        NetworkService.instance.mRetrofit.create(IMine2CommonToolsService::class.java).getOrdersBubbleCount(merchantId)
    } catch (e: Exception) {
        BaseBean<OrderStatusNumber>().initWithException(e)
    }

    suspend fun getShoppingGoldRechargeBean(merchantId: String): BaseBean<ShoppingGoldRechargeBean> = try {
        NetworkService.instance.mRetrofit.create(IMine2CommonToolsService::class.java).getShoppingGoldRechargeBean(merchantId)
    } catch (e: Exception) {
        BaseBean<ShoppingGoldRechargeBean>().initWithException(e)
    }

    /**
     * 获取个人中心展位
     */
    suspend fun getBoothData(sceneType: String): BaseBean<BoothData> = try {
        NetworkService.instance.mRetrofit.create(IMine2CommonToolsService::class.java).getBoothData(sceneType)
    } catch (e: Exception) {
        BaseBean<BoothData>().initWithException(e)
    }

    /**
     * 获取平安进件数据
     */
    suspend fun getPingAnPrePay(merchantId: String) = try {
        NetworkService.instance.mRetrofit.create(IMine2CommonToolsService::class.java).getPingAnPrePay(merchantId)
    } catch (e: Exception) {
        BaseBean<PingAnPrePay>().initWithException(e)
    }

    /**
     * 京东采购融资预申请
     */
    suspend fun getJDPrePayUrl(merchantId: String) = try {
        NetworkService.instance.mRetrofit.create(IMine2CommonToolsService::class.java).getJDPrePayUrl(merchantId)
    } catch (e: Exception) {
        BaseBean<JDPrePay>().initWithException(e)
    }

    /**
     * 京东联合支付
     */
    suspend fun jdLogin(merchantId: String, url: String) = try {
        NetworkService.instance.mRetrofit.create(IMine2CommonToolsService::class.java)
            .jdLogin(merchantId, url)
    } catch (e: Exception) {
        BaseBean<String>().initWithException(e)
    }

    /**
     * 京东联合支付
     */
    suspend fun getNongH5Url(merchantId: String) = try {
        NetworkService.instance.mRetrofit.create(IMine2CommonToolsService::class.java)
            .getNongH5Url(merchantId)
    } catch (e: Exception) {
        BaseBean<PayNongData>().initWithException(e)
    }

}