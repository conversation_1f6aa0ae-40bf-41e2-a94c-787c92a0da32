package com.ybmmarket20.network.request

import com.ybmmarket20.bean.*
import com.ybmmarket20.common.toCommaSeparatedString
import com.ybmmarket20.more_account.data.initWithException
import com.ybmmarket20.network.NetworkService
import com.ybmmarket20.network.ResponseIntercepter
import com.ybmmarketkotlin.bean.RebateVoucherBean
import retrofit2.http.Field
import retrofit2.http.FieldMap
import retrofit2.http.FormUrlEncoded
import retrofit2.http.POST
import java.lang.Exception

interface PayResultService {

    @FormUrlEncoded
    @POST("queryPay")
    suspend fun getPayResultInfo(@FieldMap pare: Map<String, String>): BaseBean<PayResultBean>?

    @FormUrlEncoded
    @POST("search/recommendedSku")
    suspend fun getRecommendGoodlist(@FieldMap pare: Map<String, String>): BaseBean<RefreshWrapperPagerBean<RowsBean>>?

    @FormUrlEncoded
    @POST("marketing/rebateVoucher/getMarketingRebateVoucherTemplateListV2")
    suspend fun getRebateVoucher(@FieldMap pare: Map<String, String>): BaseBean<RebateVoucherBean>?

    @FormUrlEncoded
    @POST("layout/exhibitionPosition")
    suspend fun getBoothData(@FieldMap pare: Map<String, String>): BaseBean<BoothData>?

    @FormUrlEncoded
    @POST("marketing/rebateVoucher/autoReceiveMarketingRebateVoucherTemplateListV2")
    suspend fun getActivityDialog(@Field("orderNo") orderNo: String, @Field("merchantId") merchantId: String): BaseBean<Unit>

    @FormUrlEncoded
    @POST("marketing/rebateVoucher/afterPaid/openRedPacket")
    suspend fun openRedEnvelope(@FieldMap map: Map<String, String>): BaseBean<OpenRedEnvelopData>

    @FormUrlEncoded
    @POST("getPayDialog")
    suspend fun getPayDialog(@Field("orderNo") orderNo: String, @Field("paycode") payCode: String): BaseBean<PayDialogBean>

    //上传电汇凭证
    @FormUrlEncoded
    @POST("order/pay/evidence/submit")
    suspend fun submitVoucher(@Field("orderId") orderId: String, @Field("merchantId") merchantId: String,@Field("evidenceImages") evidenceImages: String,): BaseBean<Any?>
}

class PayResultRequest {

    /**
     *  获取支付成功页面头部信息
     */
    suspend fun getPayResultInfo(params: Map<String, String>): BaseBean<PayResultBean>? = try {
        NetworkService.instance.mRetrofit.create(PayResultService::class.java).getPayResultInfo(params)
    } catch (e: Exception) {
        BaseBean<PayResultBean>().initWithException(e)
    }

    /**
     *  获取支付成功页面 商品推荐
     */
    suspend fun getRecommendGoodlist(params: Map<String, String>): BaseBean<RefreshWrapperPagerBean<RowsBean>>? = try {
        NetworkService.instance.mRetrofit.create(PayResultService::class.java).getRecommendGoodlist(params)
    } catch (e: Exception) {
        BaseBean<RefreshWrapperPagerBean<RowsBean>>().initWithException(e)
    }

    /**
     *  获取支付成功页 优惠券推荐
     */
    suspend fun getRebateVoucher(params: Map<String, String>): BaseBean<RebateVoucherBean>? = try {
        NetworkService.instance.mRetrofit.create(PayResultService::class.java).getRebateVoucher(params)
    } catch (e: Exception) {
        BaseBean<RebateVoucherBean>().initWithException(e)
    }

    /**
     *  获取支付成功页面 热词推荐
     */
    suspend fun getBoothData(params: Map<String, String>): BaseBean<BoothData>? = try {
        NetworkService.instance.mRetrofit.create(PayResultService::class.java).getBoothData(params)
    } catch (e: Exception) {
        BaseBean<BoothData>().initWithException(e)
    }

    /**
     * 支付成功请求活动对话框
     */
    suspend fun getActivityDialog(orderNo: String, merchantId: String): BaseBean<Unit> = try {
        NetworkService.instance.mRetrofit.create(PayResultService::class.java).getActivityDialog(orderNo, merchantId)
    } catch (e: Exception) {
        BaseBean<Unit>().initWithException(e)
    }

    /**
     * 领取红包
     */
    suspend fun openRedEnvelope(@FieldMap map: Map<String, String>): BaseBean<OpenRedEnvelopData> = try {
        NetworkService.instance.mRetrofit.create(PayResultService::class.java).openRedEnvelope(map)
    } catch (e: Exception) {
        BaseBean<OpenRedEnvelopData>().initWithException(e)
    }

    /**
     * 获取支付后的弹框
     */
    suspend fun getPayDialog(orderNo: String, payCode: String): BaseBean<PayDialogBean> = try {
        NetworkService.instance.mRetrofit.create(PayResultService::class.java).getPayDialog(orderNo, payCode)
    } catch (e: Exception) {
        BaseBean<PayDialogBean>().initWithException(e)
    }

    suspend fun submitVoucher(orderId: String, merchantId: String,evidenceImages: ArrayList<String>): BaseBean<Any?>? = try {
        val strEvidenceImages = evidenceImages.toCommaSeparatedString()
        NetworkService.instance.mRetrofit.create(PayResultService::class.java).submitVoucher(orderId, merchantId, strEvidenceImages)
    } catch (e: Exception) {
        BaseBean<Any?>().initWithException(e)
    }
}



