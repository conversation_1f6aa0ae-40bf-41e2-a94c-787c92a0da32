package com.ybmmarket20.network.request

import com.google.gson.Gson
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.ListItemAddCardBean
import com.ybmmarket20.bean.RowsPriceDiscount
import com.ybmmarket20.common.JGTrackManager
import com.ybmmarket20.bean.JgRequestParams
import com.ybmmarket20.common.YBMAppLike
import com.ybmmarket20.more_account.data.initWithException
import com.ybmmarket20.network.NetworkService
import com.ydmmarket.report.manager.TrackManager
import retrofit2.http.Field
import retrofit2.http.FormUrlEncoded
import retrofit2.http.POST

interface IListItemAddCartService {

    /**
     * 商品列表获取运费
     */
    @POST("addCartTrial")
    @FormUrlEncoded
    suspend fun addCartTrial(
            @Field("skuId")
            skuId: String,
            @Field("quantity")
            quantity: String,
            @Field("orgId")
            orgId: String,
            @Field("productPrice")
            productPrice: String,
            @Field("productType")
            productType : Int,
            @Field("mddata")
            mddata: String): BaseBean<ListItemAddCardBean>

    /**
     * 商品列表获取运费
     */
    @POST("addCartTrial")
    @FormUrlEncoded
    suspend fun addCartTrial(
            @Field("skuId")
            skuId: String,
            @Field("quantity")
            quantity: String,
            @Field("orgId")
            orgId: String,
            @Field("productPrice")
            productPrice: String,
            @Field("productType")
            productType : Int): BaseBean<ListItemAddCardBean>

    /**
     * 商品列表获取运费
     */
    @POST("addCartTrial")
    @FormUrlEncoded
    suspend fun addCartTrial(
        @Field("skuId")
        skuId: String,
        @Field("quantity")
        quantity: String,
        @Field("orgId")
        orgId: String,
        @Field("productPrice")
        productPrice: String,
        @Field("entrance")
        entrance: String,
        @Field("activityEntrance")
        activityEntrance: String,
        @Field("operationId")
        operationId: String,
        @Field("operationRank")
        operationRank: String,
        @Field("rank")
        rank: String,
        @Field("productType")
        productType : Int,
        @Field("freeShippingFlag")
        freeShippingFlag : Boolean): BaseBean<ListItemAddCardBean>

    /**
     * 获取折后价
     */
    @POST("marketing/discount/satisfactoryInHandPrice")
    @FormUrlEncoded
    suspend fun getAfterDiscountPrice(@Field("skuIds")skuIds: String): BaseBean<List<RowsPriceDiscount>?>
}

class ListItemAddCartRequest {

    suspend fun addCartTrial(skuId: String, quantity: String, orgId: String, productPrice: String,entrance:String,activityEntrance:String,productType : Int): BaseBean<ListItemAddCardBean> = try {
        val jgRequestParams = JgRequestParams()

        jgRequestParams.entrance = entrance
        jgRequestParams.activity_entrance = activityEntrance
        jgRequestParams.search_sort_strategy_id = JGTrackManager.getSuperProperty(
                YBMAppLike.getAppContext(),
                JGTrackManager.FIELD.FIELD_SEARCH_SORT_STRATEGY_ID) as String?
        if (JGTrackManager.GlobalVariable.mJgOperationInfo != null) {
            val mJgOperationInfo = JGTrackManager.GlobalVariable.mJgOperationInfo
            if (!mJgOperationInfo?.productId.isNullOrEmpty() && mJgOperationInfo?.productId == skuId.toString()) {
                if (mJgOperationInfo.operationId != null) {
                    jgRequestParams.operation_id = mJgOperationInfo.operationId
                }
                if (mJgOperationInfo.operationRank != null) {
                    jgRequestParams.operation_rank = mJgOperationInfo.operationRank
                }
                if (mJgOperationInfo.rank != null) {
                    jgRequestParams.rank = mJgOperationInfo.rank
                }
            }
        }
        JGTrackManager.GlobalVariable.mJgSearchRowsBean?.let {
            if (!it.productId.isNullOrEmpty() && it.productId == skuId){
                jgRequestParams.list_position_type = it.positionType.toString()
                jgRequestParams.list_position_typename = it.positionTypeName
                jgRequestParams.key_word = it.searchKeyword
                jgRequestParams.product_id = it.productId
                jgRequestParams.product_name = it.productName
                jgRequestParams.product_first = it.categoryFirstId
                jgRequestParams.product_number = quantity.toIntOrNull()
                jgRequestParams.product_price = it.jgProductPrice
                jgRequestParams.product_type = it.productType.toString()
                jgRequestParams.product_activity_type = it.productActivityType
                jgRequestParams.product_shop_code = it.shopCode
                jgRequestParams.product_shop_name = it.shopName
                JGTrackManager.GlobalVariable.mJgSearchSomeField?.let {field->
                    field.mJgPageListCommonBean?.let {
                        jgRequestParams.sptype = it.sptype?:""
                        jgRequestParams.jgspid = it.jgspid?:""
                        jgRequestParams.sid = it.sid?:""
                        jgRequestParams.direct = "1"
                        jgRequestParams.page_no = it.page_no?:1
                        jgRequestParams.result_cnt = it.result_cnt?:0
                        jgRequestParams.page_size = it.page_size?:1
                        jgRequestParams.total_page = it.total_page

                    }
                    jgRequestParams.rank = field.rank?:1
                }
            }
        }
        jgRequestParams.session_id = TrackManager.getSessionId(YBMAppLike.getAppContext())

        val mddata = if (entrance.contains(JGTrackManager.TrackShoppingCart.TITLE)){ //购物车只传个direct = "3"
            null
        }else{
            Gson().toJson(jgRequestParams)
        }
        mddata?.let {
            NetworkService.instance.mRetrofit.create(IListItemAddCartService::class.java).addCartTrial(
                    skuId,
                    quantity,
                    orgId,
                    productPrice,
                    productType,
                    it)
        }?: kotlin.run {
            NetworkService.instance.mRetrofit.create(IListItemAddCartService::class.java).addCartTrial(
                    skuId,
                    quantity,
                    orgId,
                    productPrice,
                    productType)
        }


    } catch (e: Exception) {
        BaseBean<ListItemAddCardBean>().initWithException(e)
    }


    suspend fun addCartTrial(skuId: String, quantity: String, orgId: String, productPrice: String,entrance:String,activityEntrance:String,
                             productType : Int, freeShippingFlag : Boolean): BaseBean<ListItemAddCardBean> = try {
        var operationId: String = ""
        var operationRank: String = ""
        var rank: String = ""

        NetworkService.instance.mRetrofit.create(IListItemAddCartService::class.java).addCartTrial(
            skuId,
            quantity,
            orgId,
            productPrice,
            entrance,
            activityEntrance,
            operationId,
            operationRank,
            rank,
            productType,
            freeShippingFlag)
    } catch (e: Exception) {
        BaseBean<ListItemAddCardBean>().initWithException(e)
    }

    suspend fun getAfterDiscountPrice(skuIds: String): BaseBean<List<RowsPriceDiscount>?> = try {
        NetworkService.instance.mRetrofit.create(IListItemAddCartService::class.java).getAfterDiscountPrice(skuIds)
    } catch (e: Exception) {
        BaseBean<List<RowsPriceDiscount>?>().initWithException(e)
    }
}