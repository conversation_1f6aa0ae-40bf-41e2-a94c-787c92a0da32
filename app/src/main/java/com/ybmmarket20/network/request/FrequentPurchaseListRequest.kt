package com.ybmmarket20.network.request

import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.RowsBean
import com.ybmmarket20.home.newpage.bean.FrequentPurchaseResponseBean
import com.ybmmarket20.more_account.data.initWithException
import com.ybmmarket20.network.NetworkService
import retrofit2.http.GET
import retrofit2.http.Query

/**
 * @class   FrequentPurchaseRequest
 * <AUTHOR>
 * @date  2024/5/6
 * @description  常购清单List的请求
 */

interface IFrequentPurchaseListService {

	@GET("layout/getFrequentPurchaseList")
	suspend fun requestFrequentPurchaseList(@Query("pageNum") pageNum:String,@Query("pageSize") pageSize:String,@Query("anchorCsuIds") anchorCsuIds:String): BaseBean<FrequentPurchaseResponseBean>

}

class FrequentPurchaseRequest {

	suspend fun requestFrequentPurchaseList(pageNum:Int,pageSize:Int,anchorCsuIds:String): BaseBean<FrequentPurchaseResponseBean> = try {
		NetworkService.instance.mRetrofit.create(IFrequentPurchaseListService::class.java).requestFrequentPurchaseList(pageNum.toString(),pageSize.toString(),anchorCsuIds)
	} catch (e: Exception) {
		BaseBean<FrequentPurchaseResponseBean>().initWithException(e)
	}
}