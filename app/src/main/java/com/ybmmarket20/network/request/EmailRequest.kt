package com.ybmmarket20.network.request

import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.more_account.data.initWithException
import com.ybmmarket20.network.NetworkService
import retrofit2.http.Field
import retrofit2.http.FormUrlEncoded
import retrofit2.http.POST
import kotlin.Exception

/**
 * 电子邮件
 */

interface EmailService {
    @FormUrlEncoded
    @POST("sendEmail")
    suspend fun sendEmail(@Field("orderNo") orderNo: String, @Field("email") email: String): BaseBean<Any>

    @FormUrlEncoded
    @POST("purchaseOrder/sendEmail")
    suspend fun sendPurchaseEmail(@Field("email") email: String, @Field("year") year: String, @Field("month") month: String): BaseBean<Any>

    @FormUrlEncoded
    @POST("file/sendMail")
    suspend fun sendEmailForContract(@Field("email") email: String, @Field("orderNo") orderNo: String, @Field("infoType") fileType: String): BaseBean<Any>
}

class EmailRequest {
    suspend fun sendEmail(orderNo: String, email: String) = try {
        NetworkService.instance.mRetrofit.create(EmailService::class.java).sendEmail(orderNo, email)
    } catch (e: Exception) {
        BaseBean<Any>().initWithException(e)
    }

    suspend fun sendPurchaseEmail(orderNo: String, year: String, month: String) = try {
        NetworkService.instance.mRetrofit.create(EmailService::class.java).sendPurchaseEmail(orderNo, year, month)
    } catch (e: Exception) {
        BaseBean<Any>().initWithException(e)
    }

    /**
     * 发送购销合同到指定邮箱
     */
    suspend fun sendEmailForContract(email: String, orderNo: String, fileType: String): BaseBean<Any> = try {
        NetworkService.instance.mRetrofit.create(EmailService::class.java).sendEmailForContract(email, orderNo, fileType)
    } catch (e: Exception) {
        BaseBean<Any>().initWithException(e)
    }
}