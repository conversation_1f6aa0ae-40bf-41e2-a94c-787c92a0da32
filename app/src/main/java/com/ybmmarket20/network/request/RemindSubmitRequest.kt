package com.ybmmarket20.network.request

import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.RemindProgressDetailData
import com.ybmmarket20.bean.RemindSubmitBean
import com.ybmmarket20.more_account.data.initWithException
import com.ybmmarket20.network.NetworkService
import retrofit2.http.*

/**
 *    author : 朱勇闯
 *    e-mail : <EMAIL>
 *    date   : 2024/10/28 14:48
 *    desc   :
 */
interface IRemindService {

    @FormUrlEncoded
    @POST("reminder/submit")
    suspend fun getRemindSubmit(@Field("orderNo") orderNo: String): BaseBean<RemindSubmitBean>

    @FormUrlEncoded
    @POST("reminder/progress")
    suspend fun getRemindProgress(@Field("orderNo") orderNo: String): BaseBean<RemindProgressDetailData>

    @FormUrlEncoded
    @POST("reminder/cancel")
    suspend fun getRemindCancel(@Field("orderNo") orderNo: String): BaseBean<String>

}

class RemindSubmitRequest {
    //提醒发货
    suspend fun getRemindSubmit(orderNo: String): BaseBean<RemindSubmitBean> = try {
        NetworkService.instance.mRetrofit.create(IRemindService::class.java)
            .getRemindSubmit(orderNo)
    } catch (e: Exception) {
        BaseBean<RemindSubmitBean>().initWithException(e)
    }

    //催发货详情
    suspend fun getRemindProgress(orderNo: String): BaseBean<RemindProgressDetailData> = try {
        NetworkService.instance.mRetrofit.create(IRemindService::class.java)
            .getRemindProgress(orderNo)
    } catch (e: Exception) {
        BaseBean<RemindProgressDetailData>().initWithException(e)
    }

    //提醒发货
    suspend fun getRemindCancel(orderNo: String): BaseBean<String> = try {
        NetworkService.instance.mRetrofit.create(IRemindService::class.java)
            .getRemindCancel(orderNo)
    } catch (e: Exception) {
        BaseBean<String>().initWithException(e)
    }

}