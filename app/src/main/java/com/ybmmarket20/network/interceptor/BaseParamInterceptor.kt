package com.ybmmarket20.network.interceptor
import com.ybmmarket20.network.HttpManager
import okhttp3.*

class BaseParamInterceptor : Interceptor {
    override fun intercept(chain: Interceptor.Chain): Response {
        val request = chain.request()

        //add base headers
        val requestHeader = Headers.Builder()
                .addAll(request.headers)
        val removeToken = request.headers["Remove-TOKEN"]
        HttpManager.getInstance().createBaseHeader().forEach {
            requestHeader.add(it.key, it.value)
        }
        if(!removeToken.isNullOrEmpty()){
            requestHeader.removeAll("accountId")
            requestHeader.removeAll("merchantId")
            requestHeader.removeAll("token")
            requestHeader.removeAll("Remove-TOKEN")
        }

        val newRequest = request.newBuilder().headers(requestHeader.build())

        //add base params to url
        if (request.method == "POST") {
            val newRequestBody = when (request.body) {
                is FormBody -> withBaseParams(request.body as FormBody)
                is MultipartBody -> withBaseParams(request.body as MultipartBody)
                else -> request.body
            }

            newRequest.post(newRequestBody!!)
        }

        return chain.proceed(newRequest.build())
    }

    private fun withBaseParams(origBody: FormBody): FormBody {
        val body = FormBody.Builder()
        //add base body
        HttpManager.getInstance().createBaseParams().forEach {
            body.add(it.key, it.value)
        }
        //add original body
        for (i in 0 until origBody.size) {
            body.add(origBody.name(i), origBody.value(i))
        }

        return body.build()
    }

    private fun withBaseParams(origBody: MultipartBody): MultipartBody {
        val body = MultipartBody.Builder()
                .setType(origBody.type)
        //add base params
        HttpManager.getInstance().createBaseParams().forEach {
            body.addFormDataPart(it.key, it.value)
        }
        //add original body
        origBody.parts.forEach{
            body.addPart(it)
        }

        return body.build()
    }
}
