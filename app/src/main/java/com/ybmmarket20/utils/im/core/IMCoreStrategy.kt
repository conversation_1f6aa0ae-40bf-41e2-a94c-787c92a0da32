package com.ybmmarket20.utils.im.core

import android.content.Context
import com.ybmmarket20.utils.im.core.callback.IMCoreCallback
import com.ybmmarket20.utils.im.core.callback.IMCoreSimpleMessageCallback


/**
 * IM 策略抽象
 */
interface IMCoreStrategy {

    /**
     * 初始化策略
     */
    fun initStrategy(context: Context?, sdkAppId: Int, logLevel: Int, callback: IMCoreCallback?)

    /**
     * 反初始化
     */
    fun unInitStrategy(callback: IMCoreCallback?)

    /**
     * 登录
     */
    fun login(userId: String?, userSig: String?, callback: IMCoreCallback?)

    /**
     * 登出
     */
    fun logout(userId: String?, callback: IMCoreCallback?)

    /**
     * 获取登录状态
     */
    fun getLoginStatus(userId: String?): Int

    /**
     * 加入群组
     */
    fun joinGroup(userId: String?, groupId: String?, message: String?, callback: IMCoreCallback?)

    /**
     * 退出群组
     */
    fun quiteGroup(userId: String?, groupId: String?, callback: IMCoreCallback?)

    /**
     * 发送群组文本消息
     */
    fun sendGroupTextMessage(text: String?, userId: String?, groupId: String?, priority: Int, callback: IMCoreCallback?)

    /**
     * 发送群组自定义消息
     */
    fun sendGroupCustomMessage(customData: ByteArray?, userId: String?, groupId: String?, priority: Int, callback: IMCoreCallback?)

    /**
     * 设置IM消息回调
     */
    fun setReceiveMessageListener(callBack: IMCoreSimpleMessageCallback?)

    /**
     * 监听群组
     */
    fun setReceiveGroupListener(callback: IMCoreCallback?)

    /**
     * 移除IM消息监听
     */
    fun removeSimpleGroupMessageListener(listener: IMCoreSimpleMessageCallback?)

    /**
     * 移除所有IM消息监听
     */
    fun removeAllSimpleGroupMessageListener()


}