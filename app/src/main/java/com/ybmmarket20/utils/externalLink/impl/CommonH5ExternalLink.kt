package com.ybmmarket20.utils.externalLink.impl

import android.net.Uri
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.constant.*
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.utils.externalLink.YBM100AbstractExternalLink

/**
 * 分发到h5页面
 */
class CommonH5ExternalLink(override val baseActivity: BaseActivity): YBM100AbstractExternalLink(baseActivity){

    override fun handleRouter(uri: Uri): <PERSON><PERSON>an {
        //白名单过滤
        if (!decodedQuery.contains(ROUTER_HOST_H5_RELEASE)
                && !decodedQuery.contains(ROUTER_HOST_H5_STAGE)
                && !decodedQuery.contains(ROUTER_HOST_H5_TEST)
                && !decodedQuery.contains(ROUTER_HOST_CMS_RELEASE)
                && !decodedQuery.contains(ROUTER_HOST_CMS_STAGE)
                && !decodedQuery.contains(ROUTER_HOST_CMS_TEST)) {
            baseActivity.finish()
            return false
        }
        return startRouter(baseActivity, "$ROUTER_SCHEME_WITH_SYMBOL_YBMPAGE$path?$decodedQuery")
    }
}