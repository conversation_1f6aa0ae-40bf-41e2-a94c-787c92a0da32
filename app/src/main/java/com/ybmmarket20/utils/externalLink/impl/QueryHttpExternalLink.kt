package com.ybmmarket20.utils.externalLink.impl

import android.net.Uri
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.constant.ROUTER_SCHEME_WITH_SYMBOL_YBMPAGE
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.utils.externalLink.YBM100AbstractExternalLink

/**
 * url 中包含http
 */
class QueryHttpExternalLink(override val baseActivity: BaseActivity): YBM100AbstractExternalLink(baseActivity) {

    override fun handleRouter(uri: Uri): <PERSON><PERSON><PERSON> = startRouter(baseActivity, "$ROUTER_SCHEME_WITH_SYMBOL_YBMPAGE$path?$query")

}