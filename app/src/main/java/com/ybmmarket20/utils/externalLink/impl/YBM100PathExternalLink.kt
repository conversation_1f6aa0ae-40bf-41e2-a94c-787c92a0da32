package com.ybmmarket20.utils.externalLink.impl

import android.content.Intent
import android.net.Uri
import android.util.Log
import com.ybmmarket20.activity.SplashActivity
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.common.YBMAppLike
import com.ybmmarket20.constant.*
import com.ybmmarket20.utils.SpUtil
import com.ybmmarket20.utils.externalLink.YBM100AbstractExternalLink

/**
 * 传入的url path作为路由地址
 */
class YBM100PathExternalLink(override val baseActivity: BaseActivity): YBM100AbstractExternalLink(baseActivity) {
    override fun handleRouter(uri: Uri): Boolean {
        //校验跳转的绑定用户与当前用户是否为同一用户，sassOrderSourcePath不可为空，否则不计来源
        val decodedUri = Uri.parse(uri.toString().replace(uri.query?:"", decodedQuery))
        val saasOrderSourcePath = decodedUri.getQueryParameter("saasOrderSourcePath")?:""
        //原逻辑需判断路由path，现已废弃
        //if(this == ROUTER_PATH_PRODUCTDETAIL || this == ROUTER_PATH_COUPONAVAILABLEACTIVITY || this == ROUTER_PATH_SEARCHPRODUCT) {
        if(saasOrderSourcePath.isNotEmpty()) {
            val merchantId = decodedUri.getQueryParameter("merchantId")
            val app = baseActivity.application as YBMAppLike
            //未登录状态跳转到登录页面
            if(SpUtil.getMerchantid() == null || SpUtil.getMerchantid().isEmpty()) return startRouter(baseActivity, "ybmpage://login")
            if(merchantId?.trim() == SpUtil.getMerchantid()){
                //登录状态，已绑定
                app.saasOrderSourcePath = if(saasOrderSourcePath.isNotEmpty()) saasOrderSourcePath else null
                if(ROUTER_PATH_PRODUCTDETAIL==path||ROUTER_PATH_COUPONAVAILABLEACTIVITY==path||ROUTER_PATH_SEARCHPRODUCT==path){
                    //处理路径'/'
                    val pathName: String = if (path.startsWith("/")) path.substring(1) else path
                    return startRouter(baseActivity, "${ROUTER_SCHEME_WITH_SYMBOL_YBMPAGE}main?routerPath=$ROUTER_SCHEME_WITH_SYMBOL_YBMPAGE$pathName?$decodedQuery")
                }
            } else {
                //登录状态，未绑定
                return startRouter(baseActivity, "${ROUTER_SCHEME_WITH_SYMBOL_YBMPAGE}main")
            }
        }
        Log.i("splash_router", "$ROUTER_SCHEME_WITH_SYMBOL_YBMPAGE$path?$decodedQuery")
        startRouter(baseActivity, "$ROUTER_SCHEME_WITH_SYMBOL_YBMPAGE$path?$decodedQuery")
        return true
    }
}