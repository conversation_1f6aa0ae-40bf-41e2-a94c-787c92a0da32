package com.ybmmarket20.utils.externalLink.impl

import android.net.Uri
import android.text.TextUtils
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.constant.ROUTER_LOGIN
import com.ybmmarket20.constant.ROUTER_SCHEME_WITH_SYMBOL_YAOBANGMANG
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.utils.SpUtil
import com.ybmmarket20.utils.externalLink.AbstractExternalLink

/**
 * 兼容老规则
 */
class CompatExternalLink(override val baseActivity: BaseActivity) : AbstractExternalLink(baseActivity) {

    override fun handleRouter(uri: Uri): Boolean {
        val merchantId = SpUtil.getMerchantid()
        return if (TextUtils.isEmpty(merchantId)) RoutersUtils.open(ROUTER_LOGIN)
        else {
            val schemeName = ROUTER_SCHEME_WITH_SYMBOL_YAOBANGMANG
            var url = uri.toString()
            url = url.substring(schemeName.length, url.length)
            RoutersUtils.open(url)
        }
    }
}