package com.ybmmarket20.utils.externalLink.impl

import android.net.Uri
import com.tencent.tddiag.logger.TDLog
import com.xyy.app.rsa.Base64
import com.ybm.app.bean.NetError
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bugly.Utils
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.common.BaseResponse
import com.ybmmarket20.common.RequestParams
import com.ybmmarket20.common.util.ToastUtils
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.constant.ROUTER_HOST_YBMMARKET20
import com.ybmmarket20.constant.ROUTER_LOGIN
import com.ybmmarket20.constant.ROUTER_SCHEME_WITH_SYMBOL_YBMPAGE
import com.ybmmarket20.network.HttpManager
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.utils.SpUtil
import com.ybmmarket20.utils.WxCodeSaveUtil
import com.ybmmarket20.utils.externalLink.AbstractExternalLink

/**
 * 微信绑定药帮忙账号
 */
class WXChatBindExternalLink(override val baseActivity: BaseActivity) :
    AbstractExternalLink(baseActivity) {
    override fun preHandleRouter(uri: Uri): Boolean {
        val url = uri.toString()
        if (host != ROUTER_HOST_YBMMARKET20 || path.isEmpty() || !url.contains(path)) {
            baseActivity.finish()
            return false
        }
        return true
    }

    override fun handleRouter(uri: Uri): Boolean {
        val decodedQuery = if (query.isNotEmpty()) String(
            Base64.decode(query) ?: emptyArray<Byte>().toByteArray()
        ) else ""
        val decodedUri = Uri.parse(uri.toString().replace(uri.query ?: "", decodedQuery))
        val merchantId = SpUtil.getMerchantid()
        val url = uri.toString()
        if (host != ROUTER_HOST_YBMMARKET20 || path.isEmpty() || !url.contains(path)) {
            baseActivity.finish()
            return false
        }
        var code = (decodedUri.getQueryParameter("code") ?: "").trim()
        if (code.isEmpty()) {
            return startRouter(baseActivity, "${ROUTER_SCHEME_WITH_SYMBOL_YBMPAGE}main")
        }

        if (merchantId.isEmpty()) { //去登陆
            WxCodeSaveUtil.getInstance(baseActivity).saveValue(code)
            return startRouter(baseActivity,"ybmpage://login")
        } else {
            return startRouter(
                baseActivity,
                "${ROUTER_SCHEME_WITH_SYMBOL_YBMPAGE}bindWX/$code"
            )
        }
    }
}