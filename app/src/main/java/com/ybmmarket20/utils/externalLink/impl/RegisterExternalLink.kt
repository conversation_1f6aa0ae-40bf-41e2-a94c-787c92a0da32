package com.ybmmarket20.utils.externalLink.impl

import android.net.Uri
import com.xyy.app.rsa.Base64
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.common.YBMAppLike
import com.ybmmarket20.constant.ROUTER_HOST_YBMMARKET20
import com.ybmmarket20.constant.ROUTER_SCHEME_WITH_SYMBOL_YBMPAGE
import com.ybmmarket20.utils.SpUtil
import com.ybmmarket20.utils.externalLink.AbstractExternalLink

/**
 * 注册
 */
class RegisterExternalLink(override val baseActivity: BaseActivity): AbstractExternalLink(baseActivity) {

    override fun handleRouter(uri: Uri): Boolean {
        val decodedQuery = if(query.isNotEmpty()) String(Base64.decode(query)?: emptyArray<Byte>().toByteArray()) else ""
        val decodedUri = Uri.parse(uri.toString().replace(uri.query?:"", decodedQuery))
        val merchantId = SpUtil.getMerchantid()
        val url = uri.toString()
        if (host != ROUTER_HOST_YBMMARKET20 || path.isEmpty() || !url.contains(path)) {
            baseActivity.finish()
            return false
        }
        if (merchantId.isEmpty()) { //去注册
            val registerSource = (decodedUri.getQueryParameter("registerSource") ?: "").trim()
            val organSign = (decodedUri.getQueryParameter("organSign") ?: "").trim()
            if(registerSource.isNotEmpty()){
                (baseActivity.application as YBMAppLike).registerSource = registerSource
            } else {
                (baseActivity.application as YBMAppLike).registerSource = null
            }
            if(organSign.isNotEmpty()) {
                (baseActivity.application as YBMAppLike).organSign = organSign
            } else {
                (baseActivity.application as YBMAppLike).organSign = null
            }
        } else {
            return startRouter(baseActivity, "${ROUTER_SCHEME_WITH_SYMBOL_YBMPAGE}main")
        }
        return startRouter(baseActivity, "$ROUTER_SCHEME_WITH_SYMBOL_YBMPAGE$path?$decodedQuery")
    }
}