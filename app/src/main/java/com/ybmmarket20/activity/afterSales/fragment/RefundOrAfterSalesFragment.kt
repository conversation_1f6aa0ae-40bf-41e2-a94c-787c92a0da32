package com.ybmmarket20.activity.afterSales.fragment

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Intent
import androidx.fragment.app.viewModels
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import androidx.recyclerview.widget.LinearLayoutManager
import com.ybm.app.common.BaseYBMApp
import com.ybmmarket20.R
import com.ybmmarket20.activity.afterSales.adapter.RefundOrAfterSalesAdapter
import com.ybmmarket20.bean.RefundOrAfterSalesItem
import com.ybmmarket20.common.BaseFragment2
import com.ybmmarket20.constant.IntentCanst
import com.ybmmarket20.utils.IntervalListener
import com.ybmmarket20.utils.IntervalUtil
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.viewmodel.RefundOrAfterSalesViewModel
import com.ybmmarketkotlin.utils.RouterJump
import kotlinx.android.synthetic.main.fragment_refund_or_after_sales.refreshLayout
import kotlinx.android.synthetic.main.fragment_refund_or_after_sales.rv

/**
 * 退款售后
 */
class RefundOrAfterSalesFragment(private val mStatus: Int,private var mKeyWord: String) : BaseFragment2(), IntervalListener {

    private var mPageNo = 1
    private var mOrderStartDateString = ""
    private var mOrderEndDateString = ""
    private var mAfterSalesType = 0
    private val mViewModel: RefundOrAfterSalesViewModel by viewModels()
    private val mData: MutableList<RefundOrAfterSalesItem> = mutableListOf()
    private lateinit var mAdapter: RefundOrAfterSalesAdapter
    private var mItemCountCallback: ((Int)->Unit)? = null

    fun setItemCountCallback(itemCountCallback: ((Int)->Unit)?) {
        mItemCountCallback = itemCountCallback
    }

    override fun getLayoutId(): Int = R.layout.fragment_refund_or_after_sales

    @SuppressLint("NotifyDataSetChanged")
    override fun initData(content: String?) {
        rv.layoutManager = LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)
        mAdapter = RefundOrAfterSalesAdapter(mData, {orderId->
                showProgress()
                mViewModel.buyAgain(orderId)
        }, {orderNo,afterSalesNo->
            //平台介入
            showProgress()
            mViewModel.checkIntervention(afterSalesNo,orderNo)
        })
        rv.adapter = mAdapter
        mAdapter.setEmptyView(context, R.layout.layout_empty_view, R.drawable.icon_empty, "暂无数据")
        showProgress()
        refreshData()
        refreshLayout.setOnRefreshListener {
           refreshData()
        }
        setObserver()
        IntervalUtil.registerInterval(this)

    }

    private fun setLoadMoreListener() {
        mAdapter.setOnLoadMoreListener({
            getData(mPageNo + 1)
        }, rv)
    }

    private fun setObserver() {
        mViewModel.refundOrAfterSalesLiveData.observe(this) {
            dismissProgress()
            refreshLayout.finishRefresh()
            if (it.isSuccess) {
                mItemCountCallback?.invoke(it.data?.total?: 0)
                val rows = it.data.rows
                if (it.data.currentPage == 1) mData.clear()
                rows?.let { rows1-> mData.addAll(rows1) }
                val canLoadMore = rows?.isNotEmpty() ?: false && rows?.size == 10
                if (canLoadMore) {
                    setLoadMoreListener()
                }
                mAdapter.notifyDataChangedAfterLoadMore(canLoadMore)
                if (it.data != null) mPageNo = it.data.currentPage
            } else {
                mAdapter.loadMoreFail()
            }
        }

        mViewModel.buyAgainLiveData.observe(this) {
            //跳转购物车
            RouterJump.jump2ShopCar()
            (context as Activity).finish()
            LocalBroadcastManager.getInstance(BaseYBMApp.getAppContext())
                .sendBroadcast(Intent(IntentCanst.ACTION_BUY_PRODUCT))
            LocalBroadcastManager.getInstance(BaseYBMApp.getAppContext())
                .sendBroadcast(Intent(IntentCanst.ACTION_SHOPNUMBER))
        }
        mViewModel.checkInterventionLiveData.observe(this) {
            dismissProgress()
            if (it.isSuccess){
                RoutersUtils.open("ybmpage://commonh5activity?url=" + it.data+"&isShowCart=0")
            }
        }
    }

    fun search(keyWord: String, orderStartDateString: String, orderEndDateString: String, afterSalesType: Int=0) {
        mKeyWord = keyWord
        mOrderStartDateString = orderStartDateString
        mOrderEndDateString = orderEndDateString
        mAfterSalesType = afterSalesType
        showProgress()
        getData(1)
    }

    fun refreshData() {
        getData(1)
    }

    fun getData(pageNo: Int) {
        val map = HashMap<String, String>()
        map["pageNo"] = "$pageNo"
        map["pageSize"] = "10"
        map["keyword"] = mKeyWord
        if (mStatus!=3){
            map["status"] = "$mStatus"
        }
        when (mAfterSalesType) {
            1,2 -> {
                map["afterSalesType"] = "$mAfterSalesType"
            }
            3 -> {
                map["docType"] = "1"
            }
        }
        map["orderStartDateString"] = mOrderStartDateString
        map["orderEndDateString"] = mOrderEndDateString
        mViewModel.getRefundOrAfterSalesList(map, pageNo)
    }

    fun getKeyWord() = mKeyWord

    override fun onDestroy() {
        super.onDestroy()
        IntervalUtil.unRegisterInterval(this)
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun callback() {
        mViewModel.updateCountDown()
        mAdapter.notifyDataSetChanged()
    }
}
