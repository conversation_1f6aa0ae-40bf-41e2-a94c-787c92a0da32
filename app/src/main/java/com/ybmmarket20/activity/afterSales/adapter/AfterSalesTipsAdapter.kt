package com.ybmmarket20.activity.afterSales.adapter

import android.graphics.Color
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.style.ForegroundColorSpan
import android.widget.EditText
import android.widget.TextView
import androidx.core.widget.addTextChangedListener
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.bean.aftersales.AfterSalesTips
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.common.widget.RoundLinearLayout

/**
 * 补充说明
 */
class AfterSalesTipsAdapter(afterSalesTips: AfterSalesTips) :
    YBMSingleViewAdapter<AfterSalesTips>(R.layout.item_after_sales_supplementary_explanation, afterSalesTips) {

    override fun bindSingleView(holder: YBMBaseHolder, bean: AfterSalesTips) {
        val etContent = holder.getView<EditText>(R.id.etContent)
        val tvCounter = holder.getView<TextView>(R.id.tvCounter)
        val rllEditFrame = holder.getView<RoundLinearLayout>(R.id.rllEditFrame)
        etContent.setText(bean.tips)
        notifyEdit(!bean.tips.isNullOrEmpty())
        etContent.addTextChangedListener {
            val text = it?.toString()?: ""
            bean.tips = text
            if (it.isNullOrEmpty()) {
                tvCounter.text = "0/50"
            } else {
                val builder = SpannableStringBuilder("${it.length}")
                builder.setSpan(ForegroundColorSpan(Color.parseColor("#00B377")), 0, builder.length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
                builder.append("/50")
                tvCounter.text = builder
            }
            notifyEdit(!it.isNullOrEmpty())
        }

        rllEditFrame.setOnClickListener {
            etContent.requestFocus()
            (mContext as BaseActivity).showSoftInput()
        }

    }

    override fun enableNotify(): Boolean = true
}