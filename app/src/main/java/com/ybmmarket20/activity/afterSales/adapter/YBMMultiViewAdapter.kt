package com.ybmmarket20.activity.afterSales.adapter

import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.view.homesteady.whenAllNotNull

/**
 * 多个View的Adapter
 */
abstract class YBMMultiViewAdapter<T>(layoutId: Int, multiData: List<T>) :
    YBMNotifyAdapter<T>(layoutId, multiData) {

    override fun bindItemView(baseViewHolder: YBMBaseHolder?, t: T) {
        whenAllNotNull(baseViewHolder, t) { holder, bean ->
            bindMultiView(holder, bean)
        }
    }

    abstract fun bindMultiView(holder: YBMBaseHolder, bean: T)
}