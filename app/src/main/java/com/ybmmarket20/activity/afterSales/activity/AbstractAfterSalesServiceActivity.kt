package com.ybmmarket20.activity.afterSales.activity

import androidx.recyclerview.widget.LinearLayoutManager
import com.ybmmarket20.R
import com.ybmmarket20.activity.afterSales.AdapterComposeManager
import com.ybmmarket20.activity.afterSales.adapter.AfterSalesErrorInvoiceAdapter
import com.ybmmarket20.activity.afterSales.adapter.AfterSalesInvoiceTypeAdapter
import com.ybmmarket20.activity.afterSales.adapter.AfterSalesSpecialInvoiceListAdapter
import com.ybmmarket20.activity.afterSales.adapter.AfterSalesSpecialInvoiceTitleAdapter
import com.ybmmarket20.activity.afterSales.adapter.AfterSalesTipsAdapter
import com.ybmmarket20.activity.afterSales.adapter.AfterSalesUploadImageAdapter
import com.ybmmarket20.bean.aftersales.AfterSalesAcceptElectronicInvoice
import com.ybmmarket20.bean.aftersales.AfterSalesBean
import com.ybmmarket20.bean.aftersales.AfterSalesInvoiceType
import com.ybmmarket20.bean.aftersales.AfterSalesSpecialInvoice
import com.ybmmarket20.bean.aftersales.AfterSalesTips
import com.ybmmarket20.bean.aftersales.AfterSalesUploadImage
import com.ybmmarket20.common.BaseActivity
import kotlinx.android.synthetic.main.activity_after_sales.rv

/**
 * 售后基类
 */
abstract class AbstractAfterSalesServiceActivity : BaseActivity() {

    override fun getContentViewId(): Int = R.layout.activity_after_sales

    override fun initData() {
        setTitle(getPageTitle())
        rv.layoutManager = LinearLayoutManager(this, LinearLayoutManager.VERTICAL, false)
        setObserver()
    }

    abstract fun setObserver()

    abstract fun getPageTitle(): String
}