package com.ybmmarket20.activity.afterSales.activity

import android.view.View
import com.github.mzule.activityrouter.annotation.Router
import com.ybm.app.adapter.YBMBaseAdapter
import com.ybmmarket20.activity.AbstractLicenseRequirementWithOrderActivity
import com.ybmmarket20.adapter.GoodsLicenseStaticAdapter
import com.ybmmarket20.bean.RefundProductListBean
import kotlinx.android.synthetic.main.activity_license_requirement_with_order.clSearch
import kotlinx.android.synthetic.main.activity_license_requirement_with_order.rvLicense
import kotlinx.android.synthetic.main.activity_license_requirement_with_order.tvShopName

/**
 * 随货资质需求
 */
@Router("licenserequirementwithorderstatic")
class LicenseRequirementWithOrderStaticActivity: AbstractLicenseRequirementWithOrderActivity() {

    override fun initData() {
        super.initData()
        tvShopName.text = paymentGoodsViewModel.staticShopName
        paymentGoodsViewModel.goodsLicenseIsShowLiveData.observe(this) {it->
            if(it){
                rvLicense.visibility = View.GONE
                clSearch.visibility = View.GONE
            }else{
                rvLicense.visibility = View.VISIBLE
                clSearch.visibility = View.VISIBLE
            }
        }
    }

    override fun getCompanyLicense() {
        licenseRequirementViewModel.setCompanyLicense(paymentGoodsViewModel.getSelectedCompanyMap(), shopCode?: "", paymentGoodsViewModel.staticShopName)
//        showProgress()
//        licenseRequirementViewModel.getCompanyLicense()
    }

    override fun getGoodsLicenseAdapter(list: MutableList<RefundProductListBean>): YBMBaseAdapter<RefundProductListBean> {
        return GoodsLicenseStaticAdapter(list, paymentGoodsViewModel)
    }


    override fun canClickCompanyLicense(): Boolean = false
    override fun isShowConfirmButton(): Boolean = false


}