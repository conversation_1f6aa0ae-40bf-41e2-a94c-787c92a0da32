package com.ybmmarket20.activity.afterSales

import androidx.recyclerview.widget.ConcatAdapter
import com.ybm.app.adapter.YBMBaseAdapter
import com.ybmmarket20.activity.afterSales.adapter.YBMNotifyAdapter

class AdapterComposeManager {

    private val mAdapterCache = mutableMapOf<Class<*>, YBMBaseAdapter<*>>()
    private val mCurrentAdapterList = mutableListOf<YBMNotifyAdapter<*>>()
    private var mEditWatch: ((Boolean)->Unit)? = null

//    companion object {
//        private var instance: AfterSalesManager? = null
//            get() {
//                if (field == null) {
//                    field = AfterSalesManager()
//                }
//                return field
//            }
//        fun get(): AfterSalesManager {
//            return instance!!
//        }
//    }

    fun addAdapter(ybmAdapter: YBMNotifyAdapter<*>): AdapterComposeManager {
//        if (!mAdapterCache.containsKey(ybmAdapter::class.java)) {
//            mAdapterCache[ybmAdapter::class.java] = ybmAdapter
//        }
//        mAdapterCache[ybmAdapter::class.java]?.let { mCurrentAdapterList.add(it) }
        mCurrentAdapterList.add(ybmAdapter)
        return this
    }

    fun addWatchEdit(editWatch: ((Boolean)->Unit)?) {
        mEditWatch = editWatch
    }

    fun concatAdapter(): ConcatAdapter {
        val adapterList = mCurrentAdapterList.toMutableList()
        mCurrentAdapterList.clear()
        if (mEditWatch != null) {
            val watchCount = adapterList.count { it.enableNotify() }
            var counter = 0
            adapterList.filter { it.enableNotify() }
                .forEach {
                    it.addNotify {isEdit->
                        if(isEdit) {
                            counter++
                        } else {
                            counter--
                        }
                        if (counter >= watchCount) {
                            counter = watchCount
                            mEditWatch?.invoke(true)
                        } else {
                            mEditWatch?.invoke(false)
                        }
                        if (counter < 0) counter = 0
                    }
                }
        }
        return ConcatAdapter(adapterList)
    }

    fun changeState(callback: (()->IComposeAdapterState)): AdapterComposeManager {
        callback().onAddAdapter()
        return this
    }
}