package com.ybmmarket20.activity.afterSales.adapter

import android.graphics.Color
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.bean.aftersales.AfterSalesLicenseGoods
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.utils.ImageUtil

/**
 * 商品资质
 */
class AfterSalesGoodsLicenseAdapter(multiData: List<AfterSalesLicenseGoods>) :
    YBMMultiViewAdapter<AfterSalesLicenseGoods>(R.layout.item_goods_license, multiData) {

    override fun bindMultiView(holder: YBMBaseHolder, bean: AfterSalesLicenseGoods) {
        val ivGoods = holder.getView<ImageView>(R.id.ivGoods)
        val tvProductName = holder.getView<TextView>(R.id.tvProductName)
        val llReport = holder.getView<LinearLayout>(R.id.llReport)
        val llLicense = holder.getView<LinearLayout>(R.id.llLicense)
        ImageUtil.load(mContext, "${bean.goodsImageUrl}", ivGoods)
        tvProductName.text = "${bean.goodsName}/${bean.spec}"

        val ivReport = holder.getView<ImageView>(R.id.ivReport)
        val tvReport = holder.getView<TextView>(R.id.tvReport)
        setSelectStatus(ivReport, tvReport, bean.checkReportState)

        val ivLicense = holder.getView<ImageView>(R.id.ivLicense)
        val tvLicense = holder.getView<TextView>(R.id.tvLicense)
        setSelectStatus(ivLicense, tvLicense, bean.licenseState)

        llReport.setOnClickListener {
            bean.checkReportState = !bean.checkReportState
            notifyItemChanged(holder.bindingAdapterPosition)
        }
        llLicense.setOnClickListener {
            bean.licenseState = !bean.licenseState
            notifyItemChanged(holder.bindingAdapterPosition)
        }
    }

    private fun setSelectStatus(ivStatus: ImageView, textStatus: TextView, status: Boolean) {
        if (status) {
            //选中
            ivStatus.setImageResource(R.drawable.icon_license_selected)
            textStatus.setTextColor(Color.parseColor("#01B377"))
        } else {
            ivStatus.setImageResource(R.drawable.icon_license_unselected)
            textStatus.setTextColor(Color.parseColor("#676773"))
        }
    }
}