package com.ybmmarket20.activity.afterSales.adapter

import android.annotation.SuppressLint
import android.view.View
import android.widget.TextView
import com.google.android.flexbox.FlexboxLayout
import com.ybm.app.adapter.YBMBaseAdapter
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.bean.aftersales.AfterSalesErrorInvoiceItem
import com.ybmmarket20.bean.aftersales.AfterSalesInvoiceType
import com.ybmmarket20.view.homesteady.whenAllNotNull

/**
 * 发票错误信息
 */
class AfterSalesErrorInvoiceAdapter(errorInvoiceInfoList: MutableList<AfterSalesErrorInvoiceItem>) :
    YBMSingleViewAdapter<MutableList<AfterSalesErrorInvoiceItem>>(R.layout.item_after_sales_select_invoice_error, errorInvoiceInfoList) {

    @SuppressLint("NotifyDataSetChanged")
    override fun bindSingleView(
        holder: Y<PERSON>BaseHolder,
        bean: MutableList<AfterSalesErrorInvoiceItem>
    ) {
        val flexBox = holder.getView<FlexboxLayout>(R.id.fbl)
        flexBox.removeAllViews()
        val selectedIndex = bean.indexOfFirst { bean-> bean.isSelected }
        notifyEdit(selectedIndex != -1)
        bean.forEach {
            val item = View.inflate(mContext, R.layout.item_after_sales_error_invoice_info, null)
            val tv = item.findViewById<TextView>(R.id.tv)
            tv.text = it.itemName
            tv.isActivated = it.isSelected
            item.setOnClickListener {_->
                it.isSelected = !it.isSelected
                notifyDataSetChanged()
            }
            flexBox.addView(item)
        }
    }

    override fun enableNotify(): Boolean = true
}