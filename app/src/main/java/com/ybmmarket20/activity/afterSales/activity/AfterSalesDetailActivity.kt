package com.ybmmarket20.activity.afterSales.activity

import android.content.Intent
import android.view.View
import androidx.activity.viewModels
import androidx.recyclerview.widget.ConcatAdapter
import androidx.recyclerview.widget.LinearLayoutManager
import com.github.mzule.activityrouter.annotation.Router
import com.ybmmarket20.R
import com.ybmmarket20.activity.afterSales.AdapterComposeManager
import com.ybmmarket20.activity.afterSales.IComposeAdapterState
import com.ybmmarket20.activity.afterSales.adapter.detail.AfterSalesDetailApplyDetailAdapter
import com.ybmmarket20.activity.afterSales.adapter.detail.AfterSalesDetailCompanyLicenseAdapter
import com.ybmmarket20.activity.afterSales.adapter.detail.AfterSalesDetailConsultHistoryAdapter
import com.ybmmarket20.activity.afterSales.adapter.detail.AfterSalesDetailEnsureReceiveProductAdapter
import com.ybmmarket20.activity.afterSales.adapter.detail.AfterSalesDetailGoodsLicenseAdapter
import com.ybmmarket20.activity.afterSales.adapter.detail.AfterSalesDetailLicenseSubTitleAdapter
import com.ybmmarket20.activity.afterSales.adapter.detail.AfterSalesDetailLicenseTitleAdapter
import com.ybmmarket20.activity.afterSales.adapter.detail.AfterSalesDetailStatusInfoAdapter
import com.ybmmarket20.activity.afterSales.adapter.detail.AfterSalesDetailWithdrawInvoiceAdapter
import com.ybmmarket20.activity.afterSales.adapter.detail.TYPE_PLATFORM
import com.ybmmarket20.activity.afterSales.adapter.detail.TYPE_ENSURE_INVOICE_RESULT
import com.ybmmarket20.activity.afterSales.adapter.detail.TYPE_WITHDRAW_AFTER_SALES_APPLICATION
import com.ybmmarket20.bean.aftersales.AFTER_SALES_TYPE_INVOICE
import com.ybmmarket20.bean.aftersales.AfterSalesDetailBean
import com.ybmmarket20.common.AlertDialogEx
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.common.util.ToastUtils
import com.ybmmarket20.utils.IntervalListener
import com.ybmmarket20.utils.IntervalUtil
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.viewmodel.AfterSalesDetailViewModel
import kotlinx.android.synthetic.main.activity_after_sales_detail.clConfirmLayout
import kotlinx.android.synthetic.main.activity_after_sales_detail.rv
import kotlinx.android.synthetic.main.activity_after_sales_detail.rvConfirm
import kotlinx.android.synthetic.main.activity_after_sales_detail.tvPlatformin

/**
 * 售后详情
 */
@Router("aftersalesdetail")
class AfterSalesDetailActivity : BaseActivity(), IntervalListener {

    private val mViewModel: AfterSalesDetailViewModel by viewModels()
    private var mAfterSalesNo: String = ""
    private var mAdapter: ConcatAdapter? = null
    private var mAfterType: Int = 0
    private var mAfterSalesDetailBean: AfterSalesDetailBean? = null
    override fun getContentViewId(): Int = R.layout.activity_after_sales_detail

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        mAfterSalesNo = intent?.getStringExtra("afterSalesNo") ?: ""
        showProgress()
        mViewModel.getAfterSalesDetailInfo(mAfterSalesNo)
    }

    override fun initData() {
        setTitle("售后详情")
        mAfterSalesNo = intent.getStringExtra("afterSalesNo") ?: ""
        setObserver()
        showProgress()
        mViewModel.getAfterSalesDetailInfo(mAfterSalesNo)
        rvConfirm.isActivated = true
        rvConfirm.setOnClickListener {
//            if (mAfterSalesDetailBean?.afterSalesType == AFTER_SALES_TYPE_INVOICE) {
//                //发票
//                RoutersUtils.open("ybmpage://invoiceaftersalesservice?orderNo=${mAfterSalesDetailBean?.orderNo?: ""}")
//            } else if (mAfterSalesDetailBean?.afterSalesType == AFTER_SALES_TYPE_LICENSE) {
//                //资质
//                RoutersUtils.open("ybmpage://licenseaftersalesservice?orderNo=${mAfterSalesDetailBean?.orderNo?: ""}&orgName=${mAfterSalesDetailBean?.origName?: ""}")
//            }
            mViewModel.getAfterSalesInfo(mAfterSalesDetailBean?.orderNo?: "", mAfterSalesDetailBean?.orgId?: "", mAfterSalesDetailBean?.origName?: "", mAfterSalesDetailBean?.afterSalesType?: AFTER_SALES_TYPE_INVOICE)
        }
        tvPlatformin.isActivated=true
        tvPlatformin.setOnClickListener {
            //平台介入
            showProgress()
            mViewModel.checkIntervention(mAfterSalesDetailBean?.afterSalesNo?: "",mAfterSalesDetailBean?.orderNo?: "" )
        }

    }

    private fun setObserver() {
        mViewModel.afterSalesDetailLiveData.observe(this) {
            dismissProgress()
            if (it.isSuccess) {
                mAfterSalesDetailBean = it.data
                //根据时间字段注册或取消倒计时
                if ((it.data.afterSalesDetailStatusBean?.countDownTime?: 0) > 0) {
                    IntervalUtil.registerInterval(this)
                } else {
                    IntervalUtil.unRegisterInterval(this)
                }
                //设置再次发起按钮是否显示
                clConfirmLayout.visibility = if (it.data.isShowApplyBtn == 1) View.VISIBLE else View.GONE
                rvConfirm.visibility = if (it.data.isShowApplyBtn == 1) View.VISIBLE else View.GONE

                rv.layoutManager = LinearLayoutManager(
                    this@AfterSalesDetailActivity,
                    LinearLayoutManager.VERTICAL,
                    false
                )
                mAdapter = AdapterComposeManager().run {
                    changeState {
                        AfterSalesDetailState(it.data, this)
                    }
                }.concatAdapter()
                rv.adapter = mAdapter
            }
        }
        mViewModel.checkInterventionLiveData.observe(this) {
            dismissProgress()
            if (it.isSuccess()) {
                RoutersUtils.open("ybmpage://commonh5activity?url=" + it.data+"&isShowCart=0")
            }
        }
        mViewModel.afterSaleTipsLiveData.observe(this, RoutersUtils::open)
        mViewModel.customerServiceRouterLiveData.observe(this, RoutersUtils::open)
        mViewModel.errorInfoLiveData.observe(this, ToastUtils::showShort)
    }

    inner class AfterSalesDetailState(
        private val afterSalesDetailBean: AfterSalesDetailBean,
        private val manager: AdapterComposeManager
    ) :
        IComposeAdapterState {
        override fun onAddAdapter() {
            manager.addAdapter(AfterSalesDetailStatusInfoAdapter(afterSalesDetailBean.afterSalesDetailStatusBean))
                .apply {
                    //发票退回
                    if (afterSalesDetailBean.refundInvoiceInfo != null && (afterSalesDetailBean.auditProcessState == 6 || afterSalesDetailBean.isShowCancelBtn == 1)) {
                        addAdapter(AfterSalesDetailWithdrawInvoiceAdapter(afterSalesDetailBean.refundInvoiceInfo!!,afterSalesDetailBean.canPlatformIn) {
                            if (it == TYPE_WITHDRAW_AFTER_SALES_APPLICATION) {
                                //撤回售后申请
                                mViewModel.withdrawAfterSaleApplication()
                            } else if (it== TYPE_ENSURE_INVOICE_RESULT){
                                //确认发票已退回
                                mViewModel.returnInvoice()
                            }else if (it==TYPE_PLATFORM){
                                //平台介入
                                showProgress()
                                mViewModel.checkIntervention(mAfterSalesDetailBean?.afterSalesNo?: "",mAfterSalesDetailBean?.orderNo?: "" )
                            }
                        })
                    }else{
                        if (afterSalesDetailBean.canPlatformIn){
                            clConfirmLayout.visibility=View.VISIBLE
                        }
                        tvPlatformin.visibility=if (afterSalesDetailBean.canPlatformIn) View.VISIBLE else View.GONE
                    }
                    //确认商业收货信息
                    if (afterSalesDetailBean.sellerAddressInfo != null) {
                        addAdapter(AfterSalesDetailEnsureReceiveProductAdapter(afterSalesDetailBean.sellerAddressInfo))
                    }
                    //售后申请详情
                    addAdapter(AfterSalesDetailApplyDetailAdapter(afterSalesDetailBean) {
                        mViewModel.getThirdCompanyCustomerServiceRouter()
                    })
                    //资质
                    val companyLicenseState =
                        !afterSalesDetailBean.corpCredential.isNullOrEmpty()
                    val goodsLicenseState =
                        !afterSalesDetailBean.productCredentialList.isNullOrEmpty()
                    if (companyLicenseState || goodsLicenseState) {
                        addAdapter(AfterSalesDetailLicenseTitleAdapter("需要补发的资质"))
                    }
                    if (companyLicenseState) {
                        addAdapter(AfterSalesDetailLicenseSubTitleAdapter("企业相关资质"))
                        addAdapter(AfterSalesDetailCompanyLicenseAdapter(afterSalesDetailBean.corpCredential!!))
                    }
                    if (goodsLicenseState) {
                        addAdapter(AfterSalesDetailLicenseSubTitleAdapter("商品相关资质"))
                        addAdapter(AfterSalesDetailGoodsLicenseAdapter(afterSalesDetailBean.productCredentialList!!))
                    }
                    //协商历史
                    if (!afterSalesDetailBean.afterSalesConsultHistoryList.isNullOrEmpty()) {
                        addAdapter(AfterSalesDetailLicenseTitleAdapter("协商历史"))
                        addAdapter(AfterSalesDetailConsultHistoryAdapter(afterSalesDetailBean.afterSalesConsultHistoryList))
                    }
                }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        IntervalUtil.unRegisterInterval(this)
    }

    override fun callback() {
        val remainTime = mViewModel.updateCountDown()
        if (remainTime < 0) {
            IntervalUtil.unRegisterInterval(this)
            mViewModel.getAfterSalesDetailInfo(mAfterSalesNo)
            return
        }
        mAdapter?.notifyItemChanged(0)
    }
}