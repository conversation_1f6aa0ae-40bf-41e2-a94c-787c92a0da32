package com.ybmmarket20.activity.afterSales.activity

import com.github.mzule.activityrouter.annotation.Router
import com.luck.picture.lib.tools.ScreenUtils
import com.ybmmarket20.R
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.utils.RoutersUtils
import kotlinx.android.synthetic.main.activity_company_after_sales.rtvConfirm
import kotlinx.android.synthetic.main.activity_company_after_sales.webview

//发票
const val TIPS_TYPE_INVOICE = 1
//资质
const val TIPS_TYPE_LICENSE = 2

/**
 * 售后须知
 */
@Router("companyaftersalestips")
class CompanyAfterSalesTipsActivity: BaseActivity() {

    override fun getContentViewId(): Int = R.layout.activity_company_after_sales

    override fun initData() {
        val orderNo = intent.getStringExtra("orderNo")
        val orgName = intent.getStringExtra("orgName")
        val contentTips = RoutersUtils.getStringFromBase64(intent.getStringExtra("contentTips"))
        val tipsType = intent.getStringExtra("tipsType")

        //标题值使用须知
        setTitle("售后须知")
        webview.settings.apply {
            defaultFontSize = ScreenUtils.dip2px(this@CompanyAfterSalesTipsActivity, 13f)

        }
        webview.loadData(contentTips, "text/html; charset=UTF-8", null)
        rtvConfirm.setOnClickListener {
            val router = when (tipsType?.toInt()) {
                TIPS_TYPE_INVOICE -> {
                    //发票
                    "ybmpage://invoiceaftersalesservice?orderNo=$orderNo"
                }
                TIPS_TYPE_LICENSE -> {
                    //资质
                    "ybmpage://licenseaftersalesservice?orderNo=$orderNo&orgName=$orgName"
                }
                else -> {
                    ""
                }
            }
            RoutersUtils.open(router)
            finish()
        }
    }
}