package com.ybmmarket20.activity.afterSales.adapter

import android.widget.FrameLayout
import com.chad.library.adapter.base.BaseViewHolder
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.bean.VideoPicPreviewEntity
import com.ybmmarket20.bean.aftersales.AfterSalesUploadImage
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.fragments.AddImage3Fragment
import com.ybmmarket20.fragments.UploadSuccessCallback

/**
 * 上传凭据
 */
class AfterSalesUploadImageAdapter(private val afterSalesUploadImage: AfterSalesUploadImage) :
    YBMSingleViewAdapter<AfterSalesUploadImage>(
        R.layout.item_after_sales_upload_image,
        afterSalesUploadImage
    ) {

    override fun bindSingleView(holder: YBMBaseHolder, bean: AfterSalesUploadImage) {
//        val replaceView = holder.getView<FrameLayout>(R.id.rlReplace)
//        val imageFragment = AddImage3Fragment()

//        (mContext as BaseActivity).supportFragmentManager.beginTransaction()
//            .replace(R.id.rlReplace, imageFragment)
//            .commit()
    }

    override fun onViewAttachedToWindow(holder: BaseViewHolder) {
        val fl = holder.getView<FrameLayout>(R.id.rlReplace)
        val fm = (mContext as BaseActivity).supportFragmentManager
        val containsFragment = fm.fragments.firstOrNull { it is AddImage3Fragment }
        val fragment = if (containsFragment == null) {
            AddImage3Fragment().apply {
                val arg = AddImage3Fragment.getBundle2Me(9, true, false, true)
                arg.putBoolean("allowe_add", true)
                arg.putCharSequence("hint", "")
                arguments = arg
            }
        } else if (fl.childCount == 0) {
            fm.beginTransaction().remove(containsFragment).commitNowAllowingStateLoss()
            containsFragment as AddImage3Fragment
        } else null

        if (fragment != null) {
            (mContext as BaseActivity).supportFragmentManager.beginTransaction()
                .add(R.id.rlReplace, fragment).commitNowAllowingStateLoss()
            fragment.setOnUploadSuccessCallback(object : UploadSuccessCallback {
                override fun onUploadSuccess(localPathList: List<VideoPicPreviewEntity>) {
                    afterSalesUploadImage.imagePathList = localPathList.map { it.v_url }.toMutableList()
                }
            })
        }
        super.onViewAttachedToWindow(holder)
    }
}