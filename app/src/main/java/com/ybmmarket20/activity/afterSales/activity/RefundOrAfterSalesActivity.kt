package com.ybmmarket20.activity.afterSales.activity

import android.content.Intent
import android.view.View
import android.view.inputmethod.EditorInfo
import android.widget.TextView
import androidx.core.widget.addTextChangedListener
import androidx.viewpager.widget.ViewPager
import com.github.mzule.activityrouter.annotation.Router
import com.lxj.xpopup.XPopup
import com.tencent.tddiag.logger.TDLog
import com.ybmmarket20.R
import com.ybmmarket20.activity.afterSales.fragment.RefundOrAfterSalesFragment
import com.ybmmarket20.adapter.RefundOrAfterSalesPagerAdapter
import com.ybmmarket20.bean.aftersales.RefundOrAfterSalesEvent
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.common.eventbus.Event
import com.ybmmarket20.view.OrderTimeFilterPopWindow
import kotlinx.android.synthetic.main.activity_refund_or_sales.etSearch
import kotlinx.android.synthetic.main.activity_refund_or_sales.ivEditClose
import kotlinx.android.synthetic.main.activity_refund_or_sales.ps_tab
import kotlinx.android.synthetic.main.activity_refund_or_sales.tvAll
import kotlinx.android.synthetic.main.activity_refund_or_sales.tvGoods
import kotlinx.android.synthetic.main.activity_refund_or_sales.tvInvoice
import kotlinx.android.synthetic.main.activity_refund_or_sales.tvQualification
import kotlinx.android.synthetic.main.activity_refund_or_sales.tvSales
import kotlinx.android.synthetic.main.activity_refund_or_sales.vp

/**
 * 退款/售后
 */
//退款/售后状态-您需处理
const val REFUND_OR_AFTER_SALES_STATUS_NEED_HANDLED = 0

//退款/售后状态-进行中
const val REFUND_OR_AFTER_SALES_STATUS_PROCESSING = 1

//退款/售后状态-已完成
const val REFUND_OR_AFTER_SALES_STATUS_FINISH = 2

////退款/售后状态-申请记录
const val REFUND_OR_AFTER_SALES_STATUS_HISTORY = 3

@Router("refundoraftersales")
class RefundOrAfterSalesActivity : BaseActivity() {

    private var mOrderNo: String? = null
    private var mKeyWord = ""
    private var orderStartDateString = ""
    private var orderEndDateString = ""
    private var mTimeCode = 0

    //售后类型 全部都不传
    //商品  docType = 1
    //资质  afterSalesType = 2
    //发票 afterSalesType = 1
    private var afterSalesType = 0
    private var mCurrentPosition: Int = REFUND_OR_AFTER_SALES_STATUS_NEED_HANDLED
    private var fragmentList = mutableListOf<RefundOrAfterSalesFragment>()
    private var isReceiveNotificationForRefreshData = false
    override fun getContentViewId(): Int = R.layout.activity_refund_or_sales

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        mOrderNo = intent?.getStringExtra("orderNo")
        etSearch.setText(mOrderNo)
        etSearch.setSelection(mOrderNo?.length ?: 0)
        mKeyWord = mOrderNo ?: ""
        vp.postDelayed({
            searchList()
        }, 500)
    }

    override fun initData() {
        setTitle("退款/售后")
        mOrderNo = intent.getStringExtra("orderNo")
        mKeyWord = mOrderNo ?: ""
        setVPFragment()
        etSearch.imeOptions = EditorInfo.IME_ACTION_SEARCH
        etSearch.setOnEditorActionListener { _, actionId, _ ->
            if (actionId == EditorInfo.IME_ACTION_SEARCH) {
                mKeyWord = etSearch.text.toString()
                searchList()
                hideSoftInput()
            }
            return@setOnEditorActionListener true
        }
        etSearch.addTextChangedListener {
            if (it.isNullOrEmpty()) {
                View.GONE
            } else {
                View.VISIBLE
            }.let(ivEditClose::setVisibility)
        }
        etSearch.setText(mOrderNo)
        etSearch.setSelection(mOrderNo?.length ?: 0)
        ivEditClose.setOnClickListener {
            mKeyWord = ""
            etSearch.setText("")
            searchList()
            //手动调第一个第二个 tab 刷新红点消息数量
            refreshMsgTips()
        }
        setSelect(tvAll)
        tvAll.setOnClickListener {
            afterSalesType = 0
            setSelect(tvAll)
            searchList()
        }
        tvGoods.setOnClickListener {
            afterSalesType = 3
            setSelect(tvGoods)
            searchList()
        }
        tvQualification.setOnClickListener {
            afterSalesType = 2
            setSelect(tvQualification)
            searchList()
        }
        tvInvoice.setOnClickListener {
            afterSalesType = 1
            setSelect(tvInvoice)
            searchList()
        }
        tvSales.setOnClickListener {
            showTimePop()
        }
        vp.postDelayed({
            searchList()
        }, 500)
    }

    private fun showTimePop() {
        val pop = OrderTimeFilterPopWindow(
            this, mTimeCode ,orderStartDateString,orderEndDateString, { startTime: String, endTime: String ,timeCode:Int->
                if(timeCode == 0){
                    tvSales.isActivated = false
                    orderStartDateString = ""
                    orderEndDateString = ""
                }else{
                    tvSales.isActivated = true
                    orderStartDateString = startTime
                    orderEndDateString = endTime
                }
                mTimeCode = timeCode
                searchList()
            }
        )
        XPopup.Builder(this)
            .dismissOnTouchOutside(false)
            .dismissOnBackPressed(false)
            .asCustom(pop)
            .show()
    }

    private fun setSelect(textView: TextView) {
        tvAll.isSelected = false
        tvGoods.isSelected = false
        tvQualification.isSelected = false
        tvInvoice.isSelected = false
        textView.isSelected = true
    }

    private fun setVPFragment() {
        val fragment1 =
            RefundOrAfterSalesFragment(6,mKeyWord)
        fragment1.setItemCountCallback {
            if (it > 0) {
                ps_tab.showMsg(REFUND_OR_AFTER_SALES_STATUS_NEED_HANDLED, it)
            } else {
                ps_tab.hideMsg(REFUND_OR_AFTER_SALES_STATUS_NEED_HANDLED)
            }
        }
        val fragment2 =
            RefundOrAfterSalesFragment(0,mKeyWord)
        fragment2.setItemCountCallback {
            if (it > 0) {
                ps_tab.showMsg(REFUND_OR_AFTER_SALES_STATUS_PROCESSING, it)
            } else {
                ps_tab.hideMsg(REFUND_OR_AFTER_SALES_STATUS_PROCESSING)
            }
        }
        val fragment3 = RefundOrAfterSalesFragment(1,mKeyWord)
        val fragment4 = RefundOrAfterSalesFragment(REFUND_OR_AFTER_SALES_STATUS_HISTORY,mKeyWord)
        fragmentList = ArrayList<RefundOrAfterSalesFragment>()
        fragmentList.add(fragment1)
        fragmentList.add(fragment2)
        fragmentList.add(fragment3)
        fragmentList.add(fragment4)
        val adapter = RefundOrAfterSalesPagerAdapter(supportFragmentManager, fragmentList)
        vp.adapter = adapter
        vp.setScroll(false)
        vp.offscreenPageLimit = fragmentList.size + 1
        ps_tab.setViewPager(vp)
        ps_tab.setIndicatorWidthEqualTitleHalf(true)
        vp.addOnPageChangeListener(object : ViewPager.OnPageChangeListener {
            override fun onPageScrolled(
                position: Int,
                positionOffset: Float,
                positionOffsetPixels: Int
            ) {
            }

            override fun onPageSelected(position: Int) {
                afterSalesType = 0
                setSelect(tvAll)
                mCurrentPosition = position
                searchList()
            }

            override fun onPageScrollStateChanged(state: Int) {
            }
        })
    }
    override fun isRegisterEventBus(): Boolean = true

    override fun receiveEvent(event: Event<*>?) {
        super.receiveEvent(event)
        if (event?.data is RefundOrAfterSalesEvent) {
            isReceiveNotificationForRefreshData = true
        }
    }

    override fun onResume() {
        super.onResume()
        if (isReceiveNotificationForRefreshData) {
            isReceiveNotificationForRefreshData = false
            var mCurrentFragment = fragmentList.get(mCurrentPosition)
            mCurrentFragment.refreshData()
        }
    }
    /**
     * 搜索列表
     */
    private fun searchList() {
        var mCurrentFragment = fragmentList.get(mCurrentPosition)
        mCurrentFragment.search(
                mKeyWord,
                orderStartDateString,
                orderEndDateString,
                afterSalesType
        )
        refreshMsgTips()
    }
    private fun refreshMsgTips(){
        if (mCurrentPosition == 0){
            fragmentList.get(1).search(
                mKeyWord,
                orderStartDateString,
                orderEndDateString,
                0
            )

        }else if (mCurrentPosition==1){
            fragmentList.get(0).search(
                mKeyWord,
                orderStartDateString,
                orderEndDateString,
                0
            )
        }else{
            fragmentList.get(0).search(
                mKeyWord,
                orderStartDateString,
                orderEndDateString,
                0
            )
            fragmentList.get(1).search(
                mKeyWord,
                orderStartDateString,
                orderEndDateString,
                0
            )
        }
    }
}