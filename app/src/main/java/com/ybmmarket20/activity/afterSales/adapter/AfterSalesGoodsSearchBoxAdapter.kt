package com.ybmmarket20.activity.afterSales.adapter

import android.widget.EditText
import androidx.core.widget.addTextChangedListener
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.bean.aftersales.AfterSalesLicense

/**
 * 商品资质搜索框
 */
class AfterSalesGoodsSearchBoxAdapter(afterSalesLicense: AfterSalesLicense, private val editCallback: ((String)->Unit)?) :
    YBMSingleViewAdapter<AfterSalesLicense>(R.layout.item_after_sales_goods_search, afterSalesLicense) {

    override fun bindSingleView(holder: YBMBaseHolder, bean: AfterSalesLicense) {
        val edit = holder.getView<EditText>(R.id.etLicense)
        edit.setText(bean.searchKeyWord)
        edit.requestFocus()
        edit.addTextChangedListener {
            bean.searchKeyWord = it.toString()
            editCallback?.invoke(it?.toString()?: "")
        }
    }
}