package com.ybmmarket20.activity.afterSales.adapter

import android.widget.CheckBox
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.bean.aftersales.AfterSalesAcceptElectronicInvoice

/**
 * 发票售后专票标题
 */
class AfterSalesSpecialInvoiceTitleAdapter(afterSalesAcceptElectronicInvoice: AfterSalesAcceptElectronicInvoice) :
    YBMSingleViewAdapter<AfterSalesAcceptElectronicInvoice>(R.layout.item_after_sales_check_special_info_title, afterSalesAcceptElectronicInvoice) {

    override fun bindSingleView(holder: YBMB<PERSON>Holder, bean: AfterSalesAcceptElectronicInvoice) {
        val cb = holder.getView<CheckBox>(R.id.cb)
        cb.isChecked = bean.isAccepted
        notifyEdit(bean.isAccepted)
        cb.setOnClickListener {
            cb.isChecked = !bean.isAccepted
            bean.isAccepted = !bean.isAccepted
            notifyEdit(bean.isAccepted)
        }
    }
}