package com.ybmmarket20.activity.afterSales.adapter

import android.text.InputFilter
import android.widget.EditText
import androidx.core.widget.addTextChangedListener
import com.google.android.material.textfield.TextInputLayout
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.bean.aftersales.AfterSalesSpecialInvoiceItem

/**
 * 发票售后专票列表
 */
class AfterSalesSpecialInvoiceListAdapter(private val afterSalesSpecialInvoiceList: MutableList<AfterSalesSpecialInvoiceItem>) :
    YBMMultiViewAdapter<AfterSalesSpecialInvoiceItem>(R.layout.item_after_sales_check_special_info_item, afterSalesSpecialInvoiceList) {

    override fun bindMultiView(holder: YBMBaseHolder, bean: AfterSalesSpecialInvoiceItem) {
        val textInputLayout = holder.getView<TextInputLayout>(R.id.til)
        val editText = holder.getView<EditText>(R.id.et)
        textInputLayout.hint = bean.hintText
        editText.setText(bean.content)
        var preCount = 0
        val index = afterSalesSpecialInvoiceList.indexOfFirst { it.content.isNullOrEmpty() }
        notifyEdit(index == -1)
        editText.filters = arrayOf(InputFilter.LengthFilter(bean.maxLength))
        editText.addTextChangedListener(beforeTextChanged = {_, _, count, _ ->
            preCount = count
        }) {editable->
            bean.content = editable.toString()
            if (editable?.length == preCount) return@addTextChangedListener
            val selectedIndex = afterSalesSpecialInvoiceList.indexOfFirst { it.content.isNullOrEmpty() }
            notifyEdit(selectedIndex == -1)
        }
    }

    override fun enableNotify(): Boolean = true
}