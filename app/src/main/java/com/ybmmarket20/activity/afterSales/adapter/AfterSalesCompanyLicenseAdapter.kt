package com.ybmmarket20.activity.afterSales.adapter

import android.graphics.Color
import android.view.View
import android.widget.TextView
import com.google.android.flexbox.FlexboxLayout
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.bean.CompanyLicenseBean
import com.ybmmarket20.bean.CompanyLicenseItem

/**
 * 企业资质
 */
class AfterSalesCompanyLicenseAdapter(singleData: CompanyLicenseBean) :
    YBMSingleViewAdapter<CompanyLicenseBean>(R.layout.item_after_sales_company_license, singleData) {

    override fun bindSingleView(holder: YBMBaseHolder, bean: CompanyLicenseBean) {
        val flexboxLayout = holder.getView<FlexboxLayout>(R.id.flLicense)
        val tvShopName = holder.getView<TextView>(R.id.tvShopName)
        tvShopName.text = bean.labelTitle
        handleCompanyLicense(bean.items, flexboxLayout)
    }

    private fun handleCompanyLicense(items: MutableList<CompanyLicenseItem>?, flexboxLayout: FlexboxLayout) {
        if (items.isNullOrEmpty()) {
            flexboxLayout.visibility = View.GONE
            return
        }
        items.map {
            val itemView = View.inflate(mContext, R.layout.item_company_license_tag, null)
            val tv = itemView.findViewById(R.id.tv) as TextView
            tv.text = it.itemName
            handleCompanyLicenseState(it, tv)
            //是否可点击
            itemView.setOnClickListener {_ ->
                it.isSelected = !it.isSelected
                handleCompanyLicenseState(it, tv)
            }
            itemView
        }.forEach {
            flexboxLayout.addView(it)
        }
    }

    private fun handleCompanyLicenseState(item: CompanyLicenseItem?, itemView: TextView) {
        val isContainsLicense = item?.isSelected?: false
        itemView.isActivated = isContainsLicense
        itemView.setTextColor(Color.parseColor(if(isContainsLicense) "#00B377" else "#292933"))
    }
}