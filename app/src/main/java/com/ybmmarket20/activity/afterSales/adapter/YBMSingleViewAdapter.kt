package com.ybmmarket20.activity.afterSales.adapter

import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.view.homesteady.whenAllNotNull

/**
 * 单个View的Adapter
 */
abstract class YBMSingleViewAdapter<T>(layoutId: Int, singleData: T) :
    YBMNotifyAdapter<T>(layoutId, mutableListOf(singleData)) {

    override fun bindItemView(baseViewHolder: YBMBaseHolder?, t: T) {
        whenAllNotNull(baseViewHolder, t) { holder, bean ->
            bindSingleView(holder, bean)
        }
    }

    abstract fun bindSingleView(holder: YBMBaseHolder, bean: T)
}