package com.ybmmarket20.activity.afterSales.adapter

import android.widget.TextView
import com.ybm.app.adapter.YBMBaseAdapter
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.bean.aftersales.AFTER_SALES_INVOICE_TYPE_ERROR
import com.ybmmarket20.bean.aftersales.AFTER_SALES_INVOICE_TYPE_NO
import com.ybmmarket20.bean.aftersales.AFTER_SALES_INVOICE_TYPE_SPECIAL
import com.ybmmarket20.bean.aftersales.AfterSalesInvoiceType
import com.ybmmarket20.view.homesteady.whenAllNotNull

/**
 * 发票售后类型
 */
class AfterSalesInvoiceTypeAdapter(invoiceType: AfterSalesInvoiceType, private val changeInvoiceType: ((type: Int)->Unit)? = null) :
    YBMSingleViewAdapter<AfterSalesInvoiceType>(R.layout.item_after_sales_invoice_type, invoiceType) {

    override fun bindSingleView(holder: Y<PERSON>B<PERSON>Holder, bean: AfterSalesInvoiceType) {
        val tvNoInvoice = holder.getView<TextView>(R.id.tvNoInvoice)
        val tvErrorInvoice = holder.getView<TextView>(R.id.tvErrorInvoice)
        val tvSpecialInvoice = holder.getView<TextView>(R.id.tvSpecialInvoice)
        tvNoInvoice.isActivated = false
        tvErrorInvoice.isActivated = false
        tvSpecialInvoice.isActivated = false
        when (bean.type) {
            AFTER_SALES_INVOICE_TYPE_NO -> {
                tvNoInvoice.isActivated = true
                notifyEdit(true)
            }
            AFTER_SALES_INVOICE_TYPE_ERROR -> {
                tvErrorInvoice.isActivated = true
                notifyEdit(true)
            }
            AFTER_SALES_INVOICE_TYPE_SPECIAL -> {
                tvSpecialInvoice.isActivated = true
                notifyEdit(true)
            }
        }
        tvNoInvoice.setOnClickListener {
            if (it.isActivated) return@setOnClickListener
            changeInvoiceType?.invoke(AFTER_SALES_INVOICE_TYPE_NO)
        }
        tvErrorInvoice.setOnClickListener {
            if (it.isActivated) return@setOnClickListener
            changeInvoiceType?.invoke(AFTER_SALES_INVOICE_TYPE_ERROR)
        }
        tvSpecialInvoice.setOnClickListener {
            if (it.isActivated) return@setOnClickListener
            changeInvoiceType?.invoke(AFTER_SALES_INVOICE_TYPE_SPECIAL)
        }
    }

    override fun enableNotify(): Boolean = true
}