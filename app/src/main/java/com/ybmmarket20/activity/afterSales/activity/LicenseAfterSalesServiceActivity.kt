package com.ybmmarket20.activity.afterSales.activity

import android.annotation.SuppressLint
import androidx.activity.viewModels
import com.github.mzule.activityrouter.annotation.Router
import com.ybmmarket20.activity.afterSales.AdapterComposeManager
import com.ybmmarket20.activity.afterSales.IComposeAdapterState
import com.ybmmarket20.activity.afterSales.adapter.AfterSalesCompanyLicenseAdapter
import com.ybmmarket20.activity.afterSales.adapter.AfterSalesGoodsLicenseAdapter
import com.ybmmarket20.activity.afterSales.adapter.AfterSalesGoodsSearchBoxAdapter
import com.ybmmarket20.activity.afterSales.adapter.AfterSalesTipsAdapter
import com.ybmmarket20.activity.afterSales.adapter.AfterSalesUploadImageAdapter
import com.ybmmarket20.bean.aftersales.AfterSalesLicense
import com.ybmmarket20.bean.aftersales.RefundOrAfterSalesEvent
import com.ybmmarket20.common.eventbus.Event
import com.ybmmarket20.common.eventbus.EventBusUtil
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.viewmodel.AfterSalesLicenseViewModel
import com.ybmmarket20.viewmodel.BaseViewModel
import kotlinx.android.synthetic.main.activity_after_sales.rv
import kotlinx.android.synthetic.main.activity_after_sales.rvConfirm

/**
 * 资质售后
 */
@Router("licenseaftersalesservice")
class LicenseAfterSalesServiceActivity : AbstractAfterSalesServiceActivity() {

    private val licenseViewModel: AfterSalesLicenseViewModel by viewModels()

    override fun getPageTitle(): String = "选择需要补发的资质"

    override fun initData() {
        super.initData()
//        val afterSalesInfoJson = intent.getStringExtra("afterSalesInfoJson")
//        val afterSalesInfo = Gson().fromJson(afterSalesInfoJson, AfterSalesInfo::class.java)
        val orderNo = intent.getStringExtra("orderNo")
        val orgName = intent.getStringExtra("orgName")
        showProgress()
        licenseViewModel.getLicense(orderNo?: "", orgName?: "")
        rvConfirm.setOnClickListener {
            showProgress()
            licenseViewModel.submitLicenseInfo(orderNo ?: "")
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun setObserver() {
        licenseViewModel.licenseRequirementLiveData.observe(this) {
            dismissProgress()
            rv.adapter = AdapterComposeManager().run {
                addWatchEdit { isSelected ->
                    rvConfirm.isActivated = isSelected
                    rvConfirm.isEnabled = isSelected
                }
                changeState {
                    LicenseAfterSalesState(this, it) {
                        licenseViewModel.searchGoods(it)
                    }
                }
            }.concatAdapter()
        }

        licenseViewModel.licenseSubmitResultLiveData.observe(this) {
            dismissProgress()
            if (it.isSuccess) {
                RoutersUtils.open("ybmpage://aftersalesdetail?afterSalesNo=${it.data?.afterSalesNo?: ""}")
                EventBusUtil.sendEvent(Event(0, RefundOrAfterSalesEvent()))
                finish()
            }
        }
    }

    class LicenseAfterSalesState(
        private val manager: AdapterComposeManager,
        private val afterSalesLicense: AfterSalesLicense,
        private val editCallback: ((String) -> Unit)?
    ) : IComposeAdapterState {
        override fun onAddAdapter() {

            manager.addAdapter(AfterSalesCompanyLicenseAdapter(afterSalesLicense.licenseCompany))
                .addAdapter(AfterSalesGoodsSearchBoxAdapter(afterSalesLicense, editCallback))
                .addAdapter(AfterSalesGoodsLicenseAdapter(afterSalesLicense.licenseGoods))
                .addAdapter(AfterSalesTipsAdapter(afterSalesLicense.afterSalesTips))
                .addAdapter(AfterSalesUploadImageAdapter(afterSalesLicense.afterSalesUploadImage))
        }
    }

    override fun getBaseViewModel(): BaseViewModel = licenseViewModel

}