package com.ybmmarket20.activity.afterSales.adapter

import android.graphics.Color
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.style.AbsoluteSizeSpan
import android.text.style.ForegroundColorSpan
import android.view.View
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.view.isVisible
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.bean.RefundOrAfterSalesItem
import com.ybmmarket20.common.widget.RoundTextView
import com.ybmmarket20.common.widget.RoundedImageView
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.utils.ImageUtil
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.view.homesteady.whenAllNotNull
import com.ybmmarketkotlin.adapter.YBMBaseMultiItemAdapter
import com.ybmmarketkotlin.utils.TimeUtils

/**
 * 退款/售后列表
 */
//售后
const val REFUND_OR_AFTER_SALES_ITEM_TYPE_AFTER_SALES = 2

//订单
const val REFUND_OR_AFTER_SALES_ITEM_TYPE_ORDER = 1

class RefundOrAfterSalesAdapter(
    data: MutableList<RefundOrAfterSalesItem>,
    private val buyAgainCallback: ((String) -> Unit)?=null,
    private val platformDetailCallBack: ((String,String) -> Unit)?=null
) :
    YBMBaseMultiItemAdapter<RefundOrAfterSalesItem>(data) {

    init {
        addItemType(REFUND_OR_AFTER_SALES_ITEM_TYPE_AFTER_SALES, R.layout.item_order_after_sales)
        addItemType(REFUND_OR_AFTER_SALES_ITEM_TYPE_ORDER, R.layout.item_order)
    }

    override fun bindItemView(baseViewHolder: YBMBaseHolder?, t: RefundOrAfterSalesItem?) {
        whenAllNotNull(baseViewHolder, t) { holder, bean ->
            when (bean.itemType) {
                REFUND_OR_AFTER_SALES_ITEM_TYPE_AFTER_SALES -> bindAfterSalesItemView(holder, bean)
                REFUND_OR_AFTER_SALES_ITEM_TYPE_ORDER -> bindOrderItemView(holder, bean)
            }
        }
    }

    /**
     * 售后
     */
    private fun bindAfterSalesItemView(holder: YBMBaseHolder, bean: RefundOrAfterSalesItem) {
        val tvAfterSalesOrgName = holder.getView<TextView>(R.id.tvAfterSalesOrgName)
        val tvAfterSaleStatus = holder.getView<TextView>(R.id.tvAfterSaleStatus)
        val tvAfterSalesType = holder.getView<TextView>(R.id.tvAfterSalesType)
        val tvAfterSaleCreateTime = holder.getView<TextView>(R.id.tvAfterSaleCreateTime)
        val rtvAfterSalesBtn = holder.getView<RoundTextView>(R.id.rtvAfterSalesBtn)
        val tvAfterSaleOrderNo = holder.getView<TextView>(R.id.tvAfterSaleOrderNo)
        val rtvPlatformDetail = holder.getView<RoundTextView>(R.id.rtvPlatformDetail)
        val rtvLogisticsInfo = holder.getView<RoundTextView>(R.id.rtvLogisticsInfo)
        tvAfterSalesOrgName.text = bean.origName
        tvAfterSaleStatus.text = bean.statusName
        tvAfterSalesType.text = bean.afterSalesTypeName
        tvAfterSaleOrderNo.text = "订单编号：${bean.orderNo}"
        tvAfterSaleCreateTime.text = "申请时间：${TimeUtils.getFormatTime1(bean.createTime)}"
        try {
            tvAfterSaleStatus.setTextColor(Color.parseColor(if (bean.statusColor.isNullOrEmpty()) "#676773" else bean.statusColor))
        } catch (e: Exception) {
            e.printStackTrace()
        }
        val clickAction: (View) -> Unit = {
            if (bean.afterSaleDetailHtmlUrl.isNullOrEmpty()) {
                RoutersUtils.open(getCheckDetailRouter(bean))
            } else {
                RoutersUtils.open("ybmpage://commonh5activity?url=" + bean.afterSaleDetailHtmlUrl + "&isShowCart=0")
            }
        }
        rtvAfterSalesBtn.setOnClickListener(clickAction)
        holder.itemView.setOnClickListener(clickAction)
        rtvPlatformDetail.isVisible = bean.canPlatformIn
        rtvPlatformDetail.setOnClickListener {
            platformDetailCallBack?.invoke(bean.orderNo ?: "",bean.afterSalesNo?:"")
        }
        if (bean.deliveryInfoHtmlUrl.isNullOrEmpty()){
            rtvLogisticsInfo.isVisible = false
        } else {
            rtvLogisticsInfo.isVisible = true
        }
        rtvLogisticsInfo.setOnClickListener {
            RoutersUtils.open("ybmpage://commonh5activity?url=" + bean.deliveryInfoHtmlUrl + "&isShowCart=0")
        }
    }

    /**
     * 退款
     */
    private fun bindOrderItemView(holder: YBMBaseHolder, bean: RefundOrAfterSalesItem) {
        val tvRefundOrgName = holder.getView<TextView>(R.id.tvRefundOrgName)
        val tvAfterSaleStatus = holder.getView<TextView>(R.id.tvAfterSaleStatus)
        val ivOrder = holder.getView<RoundedImageView>(R.id.ivOrder)
        val tvOrderCount = holder.getView<TextView>(R.id.tvOrderCount)
        val tvGoodsTotal = holder.getView<TextView>(R.id.tvGoodsTotal)
        val tvApplyTime = holder.getView<TextView>(R.id.tvApplyTime)
        val llRemainTime = holder.getView<LinearLayout>(R.id.llRemainTime)
        val rtvAfterSalesDetail = holder.getView<RoundTextView>(R.id.rtvAfterSalesDetail)
        val rtvPlatformDetail = holder.getView<RoundTextView>(R.id.rtvPlatformDetail)
        val rtvLogisticsInfo = holder.getView<RoundTextView>(R.id.rtvLogisticsInfo)
        val tvRemainTimeText = holder.getView<TextView>(R.id.tvRemainTimeText)
        val tvOrderNum = holder.getView<TextView>(R.id.tvOrderNo)
        tvRefundOrgName.text = bean.origName
        tvAfterSaleStatus.text = bean.statusName
        ImageUtil.load(mContext, "${AppNetConfig.LORD_IMAGE}${bean.imageUrl}", ivOrder)
        tvOrderCount.text = "${bean.varietyNum}种商品"
        setAmount(tvGoodsTotal, bean)
        tvApplyTime.text = "申请时间：${TimeUtils.getFormatTime1(bean.createTime)}"
        llRemainTime.visibility = if (bean.countDownNewTime <= 0L) View.GONE else View.VISIBLE
        tvRemainTimeText.text = TimeUtils.timeFormatToMinuteWithUnit(bean.countDownNewTime * 1000)
        tvOrderNum.visibility = View.VISIBLE
        tvOrderNum.text = bean.orderNo
        try {
            tvAfterSaleStatus.setTextColor(Color.parseColor(if (bean.statusColor.isNullOrEmpty()) "#676773" else bean.statusColor))
        } catch (e: Exception) {
            e.printStackTrace()
        }

        val clickAction: (View) -> Unit = {
            if (bean.afterSaleDetailHtmlUrl.isNullOrEmpty()) {
                RoutersUtils.open(getCheckDetailRouter(bean))
            } else {
                RoutersUtils.open("ybmpage://commonh5activity?url=" + bean.afterSaleDetailHtmlUrl + "&isShowCart=0")
            }
        }
        rtvAfterSalesDetail.setOnClickListener(clickAction)
        holder.itemView.setOnClickListener(clickAction)
        rtvPlatformDetail.isVisible = bean.canPlatformIn
        rtvPlatformDetail.setOnClickListener {
            platformDetailCallBack?.invoke(bean.orderNo ?: "",bean.afterSalesNo?:"")
        }
        if (bean.deliveryInfoHtmlUrl.isNullOrEmpty()){
            rtvLogisticsInfo.isVisible = false
        } else {
            rtvLogisticsInfo.isVisible = true
        }
        rtvLogisticsInfo.setOnClickListener {
            RoutersUtils.open("ybmpage://commonh5activity?url=" + bean.deliveryInfoHtmlUrl + "&isShowCart=0")
        }
    }

    /**
     * 设置金额
     */
    private fun setAmount(tv: TextView, bean: RefundOrAfterSalesItem) {
        val amountText = if (bean.isSmallPayment()) "小额赔偿" else "总计"
        val builder = SpannableStringBuilder("$amountText:￥${bean.money}")
        try {
            if (bean.isIndemnityMoney()) {
                val indemnityMoneyBuilder = SpannableStringBuilder("(含额外赔偿￥${bean.indemnityMoney})")
                indemnityMoneyBuilder.setSpan(ForegroundColorSpan(Color.parseColor("#FF2121")), 0, indemnityMoneyBuilder.length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
                indemnityMoneyBuilder.setSpan(AbsoluteSizeSpan(12, true), 0, indemnityMoneyBuilder.length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
                builder.append(indemnityMoneyBuilder)
            }
            tv.text = builder
        } catch (e: Exception) {
            e.printStackTrace()
            tv.text = "$amountText：￥${bean.money}"
        }
    }

    /**
     * 获取查看详情路由
     */
    private fun getCheckDetailRouter(bean: RefundOrAfterSalesItem): String {
        return if (bean.afterSalesType == 3) {
            //退款
            "ybmpage://refunddetail?refundOrderNo=${bean.afterSalesNo}&orderRefundId=${bean.afterSalesId}"
        } else {
            //售后
            "ybmpage://aftersalesdetail?afterSalesNo=${bean.afterSalesNo}"
        }
    }
}