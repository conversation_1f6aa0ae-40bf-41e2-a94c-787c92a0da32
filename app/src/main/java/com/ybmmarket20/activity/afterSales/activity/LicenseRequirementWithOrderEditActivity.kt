package com.ybmmarket20.activity.afterSales.activity

import android.view.View
import com.github.mzule.activityrouter.annotation.Router
import com.ybm.app.adapter.YBMBaseAdapter
import com.ybmmarket20.activity.AbstractLicenseRequirementWithOrderActivity
import com.ybmmarket20.adapter.GoodsLicenseEditAdapter
import com.ybmmarket20.bean.CompanyLicenseBean
import com.ybmmarket20.bean.RefundProductListBean
import kotlinx.android.synthetic.main.activity_license_requirement_with_order.tvTipOrderList

/**
 * 随货资质需求
 */
@Router("licenserequirementwithorderEdit")
class LicenseRequirementWithOrderEditActivity: AbstractLicenseRequirementWithOrderActivity() {
    override fun getCompanyLicense() {
        showProgress()
        val shopType = intent.getStringExtra("shopType")
        licenseRequirementViewModel.getCompanyLicense(shopType?: "")
    }

    override fun getGoodsLicenseAdapter(list: MutableList<RefundProductListBean>): YBMBaseAdapter<RefundProductListBean> {
        return GoodsLicenseEditAdapter(list, paymentGoodsViewModel)
    }


    override fun canClickCompanyLicense(): Boolean = true
    override fun isShowConfirmButton(): Boolean = true

    override fun onLicenseRequirement(companyLicenseBean: CompanyLicenseBean?) {
        val shopType = intent.getStringExtra("shopType")
        val tips = companyLicenseBean?.tips
        if (shopType == "2" && !tips.isNullOrEmpty()) {
            //自营
            tvTipOrderList.visibility = View.VISIBLE
            tvTipOrderList.text = tips
        }
    }


}