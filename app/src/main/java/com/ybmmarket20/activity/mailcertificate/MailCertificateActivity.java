package com.ybmmarket20.activity.mailcertificate;

import android.content.Context;
import android.content.Intent;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.ybm.app.bean.NetError;
import com.ybm.app.common.ImageLoader.ImageHelper;
import com.ybmmarket20.R;
import com.ybmmarket20.activity.BaseSelectPictureActivity;
import com.ybmmarket20.activity.BigPicActivity;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.CommentUploadPicBean;
import com.ybmmarket20.bean.EmptyBean;
import com.ybmmarket20.bean.LogisticsWayBean;
import com.ybmmarket20.bean.MailCertificateBean;
import com.ybmmarket20.common.AlertDialogEx;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.common.util.ToastUtils;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.constant.IntentCanst;
import com.ybmmarket20.utils.SpUtil;
import com.ybmmarket20.view.LogisticsWayPopWindow;

import java.io.File;
import java.util.List;

import butterknife.Bind;
import butterknife.OnClick;

/**
 * Created by dengmingjia on 2019/4/2
 * 上传邮寄凭证
 */
public class MailCertificateActivity extends BaseSelectPictureActivity {
    private String mOrderNo;
    private MailCertificateBean mMailCertificateBean;

    @Bind(R.id.mCompanyNameTv)
    TextView mCompanyNameTv;
    @Bind(R.id.mReceiverTv)
    TextView mReceiverTv;
    @Bind(R.id.mPhoneTv)
    TextView mPhoneTv;
    @Bind(R.id.mAddressTv)
    TextView mAddressTv;
    @Bind(R.id.mRemarksTv)
    TextView mRemarksTv;
    @Bind(R.id.mMailWayTv)
    TextView mMailWayTv;
    @Bind(R.id.mCodeEt)
    EditText mCodeEt;
    @Bind(R.id.mPicIv)
    ImageView mPicIv;
    @Bind(R.id.mDeletePicIv)
    ImageView mDeletePicIv;
    @Bind(R.id.mRootLayout)
    View mRootLayout;


    private LogisticsWayPopWindow mLogisticsWayPopWindow;


    public static void jumpTo(Context context, String orderNo) {
        Intent intent = new Intent(context, MailCertificateActivity.class);
        intent.putExtra(IntentCanst.ORDER_NO, orderNo);
        context.startActivity(intent);
    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_mail_certificate;
    }

    @Override
    protected void initData() {
        setTitle(getString(R.string.title_mail_certificate));
        mOrderNo = getIntent().getStringExtra(IntentCanst.ORDER_NO);
        mCodeEt.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                if (mMailCertificateBean != null) {
                    mMailCertificateBean.setOrderno(s.toString().trim());
                }
            }
        });
        getMailCertificateInfo(mOrderNo);
    }

    private void getMailCertificateInfo(String orderNo) {
        showProgress();
        RequestParams params = new RequestParams();
        if (!TextUtils.isEmpty(orderNo)) {
            params.put("orderNO", orderNo);
//            params.put("orderNO", "YBM20190322145525100060");
        }
        String url = AppNetConfig.GET_POST_MAIL_INFO;
//        String url = "http://192.168.110.53:8015/app/getPostMailInfo";
        HttpManager.getInstance().post(url, params, new BaseResponse<MailCertificateBean>() {
            @Override
            public void onSuccess(String content, BaseBean<MailCertificateBean> obj, MailCertificateBean data) {
                dismissProgress();
                if (data != null) {
                    onGetMailCertificateInfo(data);
                }
            }

            @Override
            public void onFailure(NetError error) {
                dismissProgress();
            }

        });


    }

    private void onGetMailCertificateInfo(MailCertificateBean data) {
        mMailCertificateBean = data;
        mCompanyNameTv.setText(data.getCorporationName());
        mReceiverTv.setText(data.getContactUser());
        mPhoneTv.setText(data.getMobile());
        mAddressTv.setText(data.getAddress());
        mRemarksTv.setText(data.getRemark());
        refreshMailWay(data.getPostName());
        if (!TextUtils.isEmpty(data.getOrderno())) {
            mCodeEt.setText(data.getOrderno());
        }
        refreshPic(data.getImageUrl());
    }

    private void refreshPic(String imageUrl) {
        if (TextUtils.isEmpty(imageUrl)) {
            mPicIv.setImageResource(R.drawable.ic_add_pic);
            mDeletePicIv.setVisibility(View.GONE);
        } else {
            ImageHelper.with(this).load(imageUrl).placeholder(R.drawable.jiazaitu_min).diskCacheStrategy(DiskCacheStrategy.SOURCE).dontTransform().dontAnimate().into(mPicIv);
            mDeletePicIv.setVisibility(View.VISIBLE);
        }
    }

    private String getImageUrl(String url) {
        if (!TextUtils.isEmpty(url) && url.startsWith("http")) {
            return url;
        }
        return AppNetConfig.CDN_HOST + url;
    }

    private void refreshMailWay(String postName) {
        if (TextUtils.isEmpty(postName)) {
            mMailWayTv.setTextColor(getResources().getColor(R.color.text_9494A6));
            mMailWayTv.setText("请选择");
        } else {
            mMailWayTv.setText(postName);
            mMailWayTv.setTextColor(getResources().getColor(R.color.text_292933));
        }
    }

    @OnClick({R.id.mPicIv, R.id.mMailWayTv, R.id.mMailWayArrow, R.id.mSubmitTv, R.id.mDeletePicIv})
    public void onViewClicked(View v) {
        if (mMailCertificateBean == null) return;
        switch (v.getId()) {
            case R.id.mMailWayTv:
            case R.id.mMailWayArrow:
                //选择物流方式
                if (mLogisticsWayPopWindow == null) {
                    getLogisticsWayInfo();
                } else {
                    mLogisticsWayPopWindow.show(mRootLayout);
                }
                break;
            case R.id.mSubmitTv:
                //提交
                doSave();

                break;
            case R.id.mPicIv:
                //图片
                if (TextUtils.isEmpty(mMailCertificateBean.getImageUrl())) {
                    showSelectImgDialog();
                } else {
                    startActivity(BigPicActivity.getIntent(this, new String[]{getImageUrl(mMailCertificateBean.getImageUrl())}, 0, mMailCertificateBean.getImageUrl(), getString(R.string.title_mail_certificate)));
                }
                break;
            case R.id.mDeletePicIv:
                //删除图片
                showDeletePhotoDialog();
                break;
        }
    }

    //保存信息
    private void doSave() {
        if (!validate()) return;

        showProgress();
        //邮寄信息
        RequestParams requestParams = new RequestParams();
        requestParams.put("orderNo", mOrderNo);
        requestParams.put("address", mMailCertificateBean.getAddress());
        requestParams.put("contactUser", mMailCertificateBean.getContactUser());
        requestParams.put("corporationName", mMailCertificateBean.getCorporationName());
        requestParams.put("icon", mMailCertificateBean.getIcon());
        requestParams.put("mobile", mMailCertificateBean.getMobile());
        requestParams.put("remark", mMailCertificateBean.getRemark());

        //邮寄凭证保存
        requestParams.put("areaOrgId", mMailCertificateBean.getAreaOrgId());
        requestParams.put("imageUrl", mMailCertificateBean.getImageUrl());
        requestParams.put("merchantCode", mMailCertificateBean.getMerchantCode());
        requestParams.put("merchantId", mMailCertificateBean.getMerchantId());
        requestParams.put("orderno", mMailCertificateBean.getOrderno());
        requestParams.put("postName", mMailCertificateBean.getPostName());
        requestParams.put("postType", mMailCertificateBean.getPostType());
        requestParams.put("type", mMailCertificateBean.getType());

        HttpManager.getInstance().post(AppNetConfig.ADD_POST_MAIL_INFO, requestParams, new BaseResponse<EmptyBean>() {
            @Override
            public void onSuccess(String content, BaseBean<EmptyBean> obj, EmptyBean data) {
                dismissProgress();
                if (obj != null && obj.isSuccess()) {
                    ToastUtils.showShort("保存成功");
                    finish();
                } else {
                    ToastUtils.showShort("保存失败");
                }
            }

            @Override
            public void onFailure(NetError error) {
                ToastUtils.showShort("保存失败");
                dismissProgress();
            }

        });

    }

    private boolean validate() {
        if (TextUtils.isEmpty(mMailCertificateBean.getPostName())) {
            ToastUtils.showShort("请选择物流方式");
            return false;
        }

        if (TextUtils.isEmpty(mMailCertificateBean.getOrderno())) {
            ToastUtils.showShort("请输入正确的运单号");
            return false;
        }


        return true;
    }

    //删除图片提示框
    private void showDeletePhotoDialog() {
        final AlertDialogEx alert = new AlertDialogEx(this);
        alert.setTitle("提示");
        alert.setMessage("确认删除此照片吗？");
        alert.setCancelButton("取消", null);
        alert.getButton(AlertDialogEx.BUTTON_CANCEL).setTextColor(getResources().getColor(R.color.text_9494A6));
        alert.setConfirmButton("删除", (AlertDialogEx.OnClickListener) (dialog, button) -> {
            mMailCertificateBean.setImageUrl(null);
            refreshPic("");
            alert.dismiss();
        });
        alert.show();
    }

    private void getLogisticsWayInfo() {
        showProgress();
        HttpManager.getInstance().post(AppNetConfig.GET_LOGISTICS_WAY, null, new BaseResponse<List<LogisticsWayBean>>() {
            @Override
            public void onSuccess(String content, BaseBean<List<LogisticsWayBean>> obj, List<LogisticsWayBean> data) {
                dismissProgress();
                if (data != null && data.size() > 0) {
                    onGetLogistics(data);
                } else {
                    ToastUtils.showShort("无物流方式信息");
                }
            }

            @Override
            public void onFailure(NetError error) {
                dismissProgress();
            }

        });
    }

    private void onGetLogistics(List<LogisticsWayBean> data) {
        mLogisticsWayPopWindow = new LogisticsWayPopWindow();
        mLogisticsWayPopWindow.setCurrentName(mMailCertificateBean.getPostName());
        mLogisticsWayPopWindow.setData(data);
        mLogisticsWayPopWindow.setListener((position, bean) -> {
            onSelectLogisticsWay(bean);
        });
        mLogisticsWayPopWindow.show(mRootLayout);
    }

    private void onSelectLogisticsWay(LogisticsWayBean bean) {
        mMailCertificateBean.setPostName(bean.getName());
        mMailCertificateBean.setPostType(String.valueOf(bean.getId()));
        mMailWayTv.setText(bean.getName());
    }

    /**
     * @param path 要上传的文件路径
     */
    public void uploadFile(String path, String url) {
        if (TextUtils.isEmpty(path)) return;
        showProgress("上传中");
        File file = new File(path);
        if (file.exists() && file.length() > 0) {
            final String fileName = path.substring(path.lastIndexOf("/") + 1, path.lastIndexOf("."));
            RequestParams params = new RequestParams();
            params.put("targetFileName", fileName);
            params.put("file", file);
            params.put("merchantId", SpUtil.getMerchantid());
            showProgress("图片上传中...", true, false);
            HttpManager.getInstance().post(url, params, new BaseResponse<CommentUploadPicBean>() {
                @Override
                public void onSuccess(String content, BaseBean<CommentUploadPicBean> obj, CommentUploadPicBean data) {
                    if (obj != null && obj.isSuccess() && data != null && !TextUtils.isEmpty(data.fileUrl)) {
                        ToastUtils.showShort("上传完成");
                        String url = getImageUrl(data.fileUrl);
                        mMailCertificateBean.setImageUrl(url);
                        refreshPic(url);
                    } else {
                        ToastUtils.showShort("上传失败");
                    }
                    dismissProgress();
                }


                @Override
                public void onFailure(NetError error) {
                    ToastUtils.showShort("上传失败");
                    dismissProgress();
                }

            });

        } else {
            ToastUtils.showShort("文件不存在");
        }
    }

    @Override
    protected void onSelectedPic(String pics) {
        uploadFile(pics, AppNetConfig.UPLOAD_COMMENT_PICTURE);
    }
}
