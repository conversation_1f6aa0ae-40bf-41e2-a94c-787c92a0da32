package com.ybmmarket20.activity.jdpay

import android.graphics.Color
import android.view.View
import androidx.activity.viewModels
import com.github.mzule.activityrouter.annotation.Router
import com.ybmmarket20.R
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.utils.FingerprintUtil
import com.ybmmarket20.utils.SpUtil
import com.ybmmarket20.viewmodel.JDPWSettingViewModel
import kotlinx.android.synthetic.main.activity_enable_fingerprint_check.*

/**
 * 开启指纹
 */
@Router("enablefingerprintcheck")
class EnableFingerprintCheckActivity: BaseActivity() {

    private val jDPWSettingViewModel: JDPWSettingViewModel by viewModels()

    override fun getContentViewId(): Int = R.layout.activity_enable_fingerprint_check

    override fun initData() {
        tvTitlePage.text = "开通指纹验证"
        ivBackPage.setOnClickListener { finish() }
        ivFingerprintBottom.setOnClickListener {
            checkFingerprint()
        }
        initObserver()
        checkFingerprint()
    }

    private fun initObserver() {
        jDPWSettingViewModel.registerFingerprintLiveData.observe(this) {
            dismissProgress()
            if (it.isSuccess){
                SpUtil.setPayFingerprintStatus(true)
                setResult(100)
                finish()
            }
        }
    }

    private fun checkFingerprint() {
        FingerprintUtil.fingerprintAuthenticate(
            this,
            "开启指纹支付，付款更便捷",
            "请将手指放在感应区进行指纹验证",
            "取消",
            object: FingerprintUtil.FingerprintCallback {
                override fun onSuccess() {
                    tvFingerprintTip.text = "指纹验证成功"
                    tvFingerprintTip.setTextColor(Color.parseColor("#292933"))
                    ivFingerprintIcon.visibility = View.VISIBLE
                    showProgress()
                    jDPWSettingViewModel.registerAndUpdateFingerprint("1")
                }

                override fun onFail(errorCode: Int, errString: CharSequence) {
                    tvFingerprintTip.text = errString
                    tvFingerprintTip.setTextColor(Color.parseColor("#FE2021"))
                    ivFingerprintIcon.visibility = View.GONE
                }
            }
        )
    }
}