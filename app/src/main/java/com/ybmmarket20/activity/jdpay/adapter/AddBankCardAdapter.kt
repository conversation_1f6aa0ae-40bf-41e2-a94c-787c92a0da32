package com.ybmmarket20.activity.jdpay.adapter

import android.app.Activity
import android.content.Context
import android.text.TextUtils
import android.util.Base64
import android.view.View
import android.view.inputmethod.InputMethodManager
import android.widget.EditText
import android.widget.ImageView
import android.widget.TextView
import com.google.gson.Gson
import com.luck.picture.lib.tools.ScreenUtils
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarketkotlin.adapter.YBMBaseMultiItemAdapter
import com.ybm.app.bean.AbstractMutiItemEntity
import com.ybmmarket20.R
import com.ybmmarket20.bean.*
import com.ybmmarket20.common.YBMAppLike
import com.ybmmarket20.common.util.ToastUtils
import com.ybmmarket20.common.widget.RoundConstraintLayout
import com.ybmmarket20.common.widget.RoundTextView
import com.ybmmarket20.utils.ImageUtil
import com.ybmmarket20.utils.analysis.XyyIoUtil
import com.ybmmarket20.view.homesteady.whenAllNotNull
import com.ybmmarket20.viewmodel.AddBankViewModel

/**
 * 添加银行卡
 */
class AddBankCardAdapter(
    val mList: MutableList<AbstractMutiItemEntity>,
    val viewModel: AddBankViewModel
) : YBMBaseMultiItemAdapter<AbstractMutiItemEntity>(mList) {

    //交互回调
    val callback: ((isHeaderExpand: Boolean, isFooterExpand: Boolean, isActiveEdit: Boolean) -> Unit)? =
        null

    //银行卡数量
    private var mTypeItemCount = 0

    init {
        addItemType(ADD_BANK_CARD_HEADER, R.layout.item_add_bank_card_header)
        addItemType(ADD_BANK_CARD_FOOTER, R.layout.item_add_bank_card_footer)
        addItemType(ADD_BANK_CARD_TYPE, R.layout.item_add_bank_card_type)
        addItemType(ADD_BANK_CARD_INPUT_NUM, R.layout.item_add_bank_card_input_num)
        mTypeItemCount = mList.count { it is BankCardItemType }
    }

    override fun bindItemView(baseViewHolder: YBMBaseHolder?, t: AbstractMutiItemEntity?) {
        whenAllNotNull(baseViewHolder, t) { holder, bean ->
            when (bean.itemType) {
                ADD_BANK_CARD_HEADER -> AddBankCardItemState.AddBankCardItemHeaderState(viewModel)
                    .bindItemView(
                        holder,
                        bean as BankCardItemHeader
                    )
                ADD_BANK_CARD_FOOTER -> AddBankCardItemState.AddBankCardItemFooterState(viewModel)
                    .bindItemView(
                        holder,
                        bean as BankCardItemFooter
                    )
                ADD_BANK_CARD_TYPE -> AddBankCardItemState.AddBankCardItemTypeState(
                    mContext,
                    viewModel,
                    mTypeItemCount
                ).bindItemView(
                    holder,
                    bean as BankCardItemType
                )
                ADD_BANK_CARD_INPUT_NUM -> AddBankCardItemState.AddBankCardItemInputNumState(
                    mContext,
                    viewModel
                ).bindItemView(
                    holder,
                    bean as BankCardItemInputNum
                )
            }
        }
    }

    sealed class AddBankCardItemState<T : AbstractMutiItemEntity> {

        abstract fun bindItemView(holder: YBMBaseHolder, bean: T)

        /**
         * 头部
         */
        class AddBankCardItemHeaderState(val viewModel: AddBankViewModel) :
            AddBankCardItemState<BankCardItemHeader>() {
            override fun bindItemView(
                holder: YBMBaseHolder,
                bean: BankCardItemHeader
            ) {
                holder.setText(R.id.tvTitle, bean.title)
                holder.setVisible(R.id.ivArrow, !bean.isExpand)
                holder.itemView.setOnClickListener {
                    viewModel.switchByExpandHeader()
                }
                val roundView = holder.itemView as RoundConstraintLayout
                //根据头部是否展开处理圆角
                if (viewModel.isHeaderExpand) {
                    roundView.setCornerRadius(ScreenUtils.dip2px(YBMAppLike.getApp().currActivity, 6F), ScreenUtils.dip2px(YBMAppLike.getApp().currActivity, 6F),0, 0)
                } else {
                    roundView.setCornerRadius(ScreenUtils.dip2px(YBMAppLike.getApp().currActivity, 6F), ScreenUtils.dip2px(YBMAppLike.getApp().currActivity, 6F),ScreenUtils.dip2px(YBMAppLike.getApp().currActivity, 6F), ScreenUtils.dip2px(YBMAppLike.getApp().currActivity, 6F))
                }
            }
        }

        /**
         * 底部
         */
        class AddBankCardItemFooterState(val viewModel: AddBankViewModel) :
            AddBankCardItemState<BankCardItemFooter>() {
            override fun bindItemView(
                holder: YBMBaseHolder,
                bean: BankCardItemFooter
            ) {
                holder.setText(R.id.tvTitle, "查看全部 ${bean.itemCount} 家银行")
                holder.itemView.setOnClickListener {
                    viewModel.switchByExpandFooter()
                }
            }
        }

        /**
         * 银行卡
         */
        class AddBankCardItemTypeState(val context: Context, val viewModel: AddBankViewModel, val typeItemCount: Int) :
            AddBankCardItemState<BankCardItemType>() {
            override fun bindItemView(
                holder: YBMBaseHolder,
                bean: BankCardItemType
            ) {
                val ivBankIcon = holder.getView<ImageView>(R.id.ivBankIcon)
                val divider = holder.getView<View>(R.id.divider)
                val itemView = holder.itemView as RoundConstraintLayout
                ImageUtil.load(context, bean.bankLogo, ivBankIcon)
                holder.setText(R.id.tvBankName, bean.bankShortName)
                holder.itemView.setOnClickListener {
                    val jsonStr = Gson().toJson(bean)
                    val params = Base64.encodeToString(jsonStr.toByteArray(), Base64.URL_SAFE)
                    viewModel.payTypeItemClick("ybmpage://addbankcarddetail?params=$params")
                    XyyIoUtil.track("no_put_card_number_add_bank_card")
                }
                //底部展开状态处理圆角和分割线
                if (viewModel.isFooterExpand) {
                    if (holder.layoutPosition == typeItemCount) {
                        divider.visibility = View.GONE
                        itemView.setCornerRadius(0, 0, ScreenUtils.dip2px(YBMAppLike.getApp().currActivity, 6F), ScreenUtils.dip2px(YBMAppLike.getApp().currActivity, 6F))
                    } else {
                        divider.visibility = View.VISIBLE
                        itemView.setCornerRadius(0)
                    }
                } else {
                    divider.visibility = View.VISIBLE
                    itemView.setCornerRadius(0)
                }
            }
        }

        /**
         * 输入银行卡
         */
        class AddBankCardItemInputNumState(val context: Context, val viewModel: AddBankViewModel) :
            AddBankCardItemState<BankCardItemInputNum>() {
            override fun bindItemView(
                holder: YBMBaseHolder,
                bean: BankCardItemInputNum
            ) {
                holder.setText(R.id.tvTitle, bean.title)
                val tVBankCardNum = holder.getView<TextView>(R.id.tVBankCardNum)
                val eVBankCardNum = holder.getView<EditText>(R.id.etBankCardNum)
                val rtvNext = holder.getView<RoundTextView>(R.id.rtvNext)
                tVBankCardNum.visibility = if (bean.isActiveEdit) View.GONE else View.VISIBLE
                eVBankCardNum.visibility = if (!bean.isActiveEdit) View.GONE else View.VISIBLE
                rtvNext.visibility = if (bean.isActiveEdit) View.VISIBLE else View.GONE
                val imm = (context as Activity).getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
                if (bean.isActiveEdit) {
                    eVBankCardNum.isFocusable = true
                    eVBankCardNum.isFocusableInTouchMode = true
                    eVBankCardNum.requestFocus()
                    try {
                        imm.showSoftInput(context.currentFocus, InputMethodManager.SHOW_IMPLICIT)
                    } catch (e: Exception) {
                        e.printStackTrace()
                    }
                } else {
                    try {
                        imm.hideSoftInputFromWindow(context.currentFocus?.windowToken, 0)
                    } catch (e: Exception) {
                        e.printStackTrace()
                    }
                    eVBankCardNum.isFocusable = false
                    eVBankCardNum.isFocusableInTouchMode = false
                }
                tVBankCardNum.setOnClickListener {
                    viewModel.payTypeInputNumClick()
                }
                rtvNext.setOnClickListener {
                    if (checkBankCard(eVBankCardNum.text.toString())) {
//                        viewModel.checkCardBindState(eVBankCardNum.text.toString())
//                        RoutersUtils.open("ybmpage://addbankcarddetailwithnum?bankCardNo=)
                        viewModel.queryBankCardInfo("${eVBankCardNum.text}")
                        XyyIoUtil.track("put_card_number_add_bank_card")
                    }
                }

            }
        }

        fun checkBankCard(cardNo: String): Boolean {
            return if (TextUtils.isEmpty(cardNo)) {
                ToastUtils.showShort("请填写卡号")
                false
            } else true
        }

    }
}