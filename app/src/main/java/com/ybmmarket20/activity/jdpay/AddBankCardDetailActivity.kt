package com.ybmmarket20.activity.jdpay

import android.content.Intent
import android.graphics.Color
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.TextPaint
import android.text.TextUtils
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.text.style.ForegroundColorSpan
import android.util.Base64
import android.view.LayoutInflater
import android.view.View
import android.widget.RadioButton
import androidx.activity.viewModels
import androidx.core.widget.addTextChangedListener
import com.github.mzule.activityrouter.annotation.Router
import com.google.gson.Gson
import com.ybmmarket20.R
import com.ybmmarket20.activity.CommonH5Activity
import com.ybmmarket20.bean.BankCardItemType
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.common.util.ToastUtils
import com.ybmmarket20.common.widget.RoundTextView
import com.ybmmarket20.utils.ImageUtil
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.viewmodel.AddBankCardDetailViewModel
import kotlinx.android.synthetic.main.activity_add_bank_card_detail.*
import kotlinx.android.synthetic.main.item_single_text.view.*

/**
 * 添加银行卡详情
 */
@Router("addbankcarddetail")
class AddBankCardDetailActivity: BaseActivity(), View.OnClickListener {

    lateinit var params: BankCardItemType
    val mViewModel: AddBankCardDetailViewModel by viewModels()
    var validateUserName = false
    var validateUserIDCardNum = false
    //选中的卡类型位置
    var mSelectedCardTypePosition = 0
    //卡类型
    var mCardType: String? = ""
    var bindResultFrom: String? = ""

    override fun getContentViewId(): Int = R.layout.activity_add_bank_card_detail

    override fun initData() {
        setTitle("添加银行卡")
        params = try {
            val base64Code = String(Base64.decode(intent.getStringExtra("params"), Base64.URL_SAFE))
            Gson().fromJson(base64Code, BankCardItemType::class.java)
        } catch (e: Exception) {
            e.printStackTrace()
            BankCardItemType()
        }
        try {
            bindResultFrom = intent.getStringExtra("bindResultFrom")?: null
        } catch (e: Exception) {
            e.printStackTrace()
        }
        ImageUtil.load(this, params.bankLogo, ivBankIcon)
        tvBankName.text = params.bankShortName

        var isSetChecked = false
        params.cardList?.forEachIndexed {index, cardInfo ->
            if (cardInfo.state == 1) {
                val itemView = LayoutInflater.from(this).inflate(R.layout.item_add_card_radio_validate, rgBank)
                val validateCard = itemView.findViewById<RadioButton>(R.id.rb)
                validateCard.id = index
                validateCard.text = cardInfo.cardTypeName
                if (!isSetChecked) {
                    mSelectedCardTypePosition = index
                    validateCard.isChecked = true
                    isSetChecked = true
                }
            } else {
                val view = LayoutInflater.from(this).inflate(R.layout.item_add_card_radio_invalidate, rgBank)
                val unUseText = view.findViewById<RoundTextView>(R.id.rtv)
                unUseText.text = cardInfo.tips
            }
        }

        rgBank.setOnCheckedChangeListener { _, checkedId ->
            mSelectedCardTypePosition = checkedId
            handlePrivacy()
        }
        handlePrivacy()
        setOnListener()
        setObserver()
        showProgress()
        mViewModel.queryIdentityInfo()
    }

    /**
     * 处理协议
     */
    private fun handlePrivacy() {
        params.cardList?.get(mSelectedCardTypePosition)?.agreementList
        val cardList = params.cardList ?: return
        if (cardList.isEmpty()) tvPrivacy.text = ""
        if (mSelectedCardTypePosition >= cardList.size) return
        val cardInfo = cardList[mSelectedCardTypePosition]
        mCardType = cardInfo.cardType
        val agreementList = cardInfo.agreementList
        val builder = SpannableStringBuilder("请仔细阅读,")
        agreementList?.forEach {
            val privacyBuilder = SpannableStringBuilder(" ${it.agreementDesc}")
            privacyBuilder.setSpan(ForegroundColorSpan(Color.parseColor("#00B377")), 0, privacyBuilder.length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
            privacyBuilder.setSpan(object : ClickableSpan() {
                override fun onClick(widget: View) {
                    val url = if(TextUtils.isEmpty(it.agreementUrl)) {
                        ""
                    } else if (it.agreementUrl!!.contains("?")){
                        "${it.agreementUrl}&ybm_title=${it.agreementDesc}"
                    } else {
                        "${it.agreementUrl}?ybm_title=${it.agreementDesc}"
                    }
                    RoutersUtils.open("ybmpage://commonh5activity?url=$url&isShowCart=0")
                }

                override fun updateDrawState(ds: TextPaint) {
                    super.updateDrawState(ds)
                    ds.isUnderlineText = false
                }
            }, 0, privacyBuilder.length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
            builder.append(privacyBuilder)
        }
        tvPrivacy.text = builder
        tvPrivacy.movementMethod = LinkMovementMethod.getInstance()
    }

    private fun setObserver() {
        mViewModel.onKeySignApplyLiveData.observe(this) {
            dismissProgress()
            if (it.isSuccess) {
//                Log.i("htmlStr_send_space", "")
//                val htmlStr = URLEncoder.encode(it.data.formData, "UTF-8")
//                Log.i("htmlStr_send_encode", htmlStr)
//                Log.i("htmlStr_send_en_space", "")
//                RoutersUtils.open("ybmpage://commonh5activity?htmlStr=$htmlStr")
                val intent = Intent(this, CommonH5Activity::class.java).apply {
                    putExtra("htmlStr", it.data.formData)
                    putExtra("url", "ybmpage://space")
                    if (bindResultFrom != null) {
                        putExtra("bindResultFrom", bindResultFrom)
                    }
                }
                startActivity(intent)
            }
        }

        mViewModel.identityInfoLiveData.observe(this) {
            dismissProgress()
            if (it.isSuccess) {
                if (!TextUtils.isEmpty(it.data?.name)) {
                    etUserName.isFocusable = false
                    etUserName.setText(it.data.name)
                    validateUserName = true
                    ivUserNameClear.visibility = View.GONE
                    etUserName.setBackgroundResource(R.drawable.shape_add_bank_card_bg)
                }
                if (!TextUtils.isEmpty(it.data?.certId)) {
                    etUserIdCard.isFocusable = false
                    etUserIdCard.setText(it.data.certId)
                    validateUserIDCardNum = true
                    ivUserIdCardClear.visibility = View.GONE
                    etUserIdCard.setBackgroundResource(R.drawable.shape_add_bank_card_bg)
                }
            }
        }
    }

    private fun setOnListener() {
        rtvNext.setOnClickListener(this)
        ivUserNameClear.setOnClickListener(this)
        ivUserIdCardClear.setOnClickListener(this)
        etUserName.addTextChangedListener(afterTextChanged = {
            if (validateUserName) return@addTextChangedListener
            ivUserNameClear.visibility = if (it.isNullOrEmpty()) View.GONE else View.VISIBLE
        })
        etUserIdCard.addTextChangedListener(afterTextChanged = {
            if (validateUserIDCardNum) return@addTextChangedListener
            ivUserIdCardClear.visibility = if (it.isNullOrEmpty()) View.GONE else View.VISIBLE
        })
    }

    private fun checkAndSubmit() {
        tvUserNameTitleTips.text = ""
        tvUserIdCardTitleTips.text = ""
        val userName = etUserName.text.toString()
        val idCardNum = etUserIdCard.text.toString()
//        if (etUserName.isFocusable) userName = ""
//        if (etUserIdCard.isFocusable) idCardNum = ""
        if (TextUtils.isEmpty(userName)) {
            tvUserNameTitleTips.text = "请输入持卡人姓名"
            ToastUtils.showShort("请输入持卡人姓名")
        } else if (TextUtils.isEmpty(idCardNum)) {
            tvUserIdCardTitleTips.text = "请输入身份证号"
            ToastUtils.showShort("请输入身份证号")
        } else if((idCardNum.length != 15 && idCardNum.length != 18) && etUserIdCard.isFocusable) {
            tvUserIdCardTitleTips.text = "请输入15或18位身份证号"
            ToastUtils.showShort("请输入15或18位身份证号")
        } else {
            showProgress()
            mViewModel.oneKeySignApply(
                mCardType?: "",
                params.bankCode?: "",
                params.bankShortName?: "",
                idCardNum,
                userName
            )
        }
    }

    override fun onClick(v: View?) {
        when(v?.id) {
            R.id.ivUserNameClear -> {
                etUserName.setText("")
            }

            R.id.ivUserIdCardClear -> {
                etUserIdCard.setText("")
            }

            R.id.rtvNext -> {
                checkAndSubmit()
            }
        }
    }
}