package com.ybmmarket20.activity.jdpay

import android.content.Context
import android.content.Intent
import android.graphics.Rect
import android.view.View
import androidx.activity.viewModels
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.github.mzule.activityrouter.annotation.Router
import com.luck.picture.lib.tools.ScreenUtils
import com.ybmmarket20.R
import com.ybmmarket20.activity.jdpay.adapter.BankCardAdapter
import com.ybmmarket20.common.AlertDialogEx
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.utils.analysis.XyyIoUtil
import com.ybmmarket20.view.CheckPayPasswordDialog
import com.ybmmarket20.viewmodel.BankCardViewModel
import com.ybmmarket20.viewmodel.CheckPayPasswordViewModel
import com.ybmmarket20.viewmodel.JDPWSettingViewModel
import kotlinx.android.synthetic.main.activity_bank_card.*

/**
 * 银行卡
 */
@Router("bankcard")
class BankCardActivity : BaseActivity(), View.OnClickListener {

    private val bankCardViewModel: BankCardViewModel by viewModels()
    private val paySettingViewModel: JDPWSettingViewModel by viewModels()
    private val checkPayPasswordViewModel: CheckPayPasswordViewModel by viewModels()
    private var checkPayPasswordDialog: CheckPayPasswordDialog? = null
    private var mAdapter: BankCardAdapter? = null
    private var mItemCount = 0


    override fun getContentViewId(): Int = R.layout.activity_bank_card

    override fun initData() {
        setTitle("银行卡")
        setRigthImg({
           RoutersUtils.open("ybmpage://paysetting")
           XyyIoUtil.track("payment_setting")
        }, R.drawable.icon_bank_card_setting)
        rclAddBankCard.setOnClickListener(this)
        rvBankCard.addItemDecoration(BankCardDecoration(this))
        setObserve()
        showProgress()
        bankCardViewModel.getBankCardList()
        val space = View.inflate(this, R.layout.view_bank_card_space, null)
    }

    private fun setObserve() {
        bankCardViewModel.bankCardListLiveData.observe(this) {
            dismissProgress()
            rclNoCard.visibility = View.VISIBLE
            rvBankCard.visibility = View.GONE
            if (it.isSuccess) {
                mItemCount = it.data.size
                if (it.data.isNotEmpty()) {
                    rclNoCard.visibility = View.GONE
                    rvBankCard.visibility = View.VISIBLE
                    mAdapter = BankCardAdapter(it.data)
                    rvBankCard.layoutManager =
                        LinearLayoutManager(this, LinearLayoutManager.VERTICAL, false)
                    rvBankCard.adapter = mAdapter
                }
            }
        }

        paySettingViewModel.jDPWSettingLiveData.observe(this) {
            dismissProgress()
            checkPayPasswordDialog?.clearReceive()
            if (it.isSuccess) {
                if (it.data.pwSettingStatus == 1) {
                    //已设置
                    val viewMode = checkPayPasswordViewModel
                    if (checkPayPasswordDialog == null) {
                        checkPayPasswordDialog =
                            CheckPayPasswordDialog(this, viewMode)
                        checkPayPasswordDialog!!.setOnCheckPasswordCallback {
                            RoutersUtils.openForResult("ybmpage://addbankcard?bindResultFrom=$BIND_RESULT_FROM_PAY_BANK_CARD_LIST", 100)
                            checkPayPasswordDialog!!.clearReceive()
                        }
                        checkPayPasswordDialog!!.setInit()
                    }
                    checkPayPasswordDialog!!.cleanErrorMsg()
                    checkPayPasswordDialog!!.show()
                } else {
                    //未设置
                    val dialogEx = AlertDialogEx(this)
                    dialogEx.setTitle("")
                        .setMessage("为了您的资金安全，请先设置支付密码")
                        .setCancelButton("稍后设置", "#9494A5",
                            AlertDialogEx.OnClickListener { _, _ -> })
                        .setConfirmButton("去设置",
                            AlertDialogEx.OnClickListener { _, _ ->
                                RoutersUtils.openForResult("ybmpage://setpaypw?settingStatus=$SET_PAY_PASSWORD_SETTING", 100)
                            })
                        .show()
                }
            }
        }
    }

    inner class BankCardDecoration(val context: Context?): RecyclerView.ItemDecoration() {

        val dp = ScreenUtils.dip2px(context, 1f)

        override fun getItemOffsets(
            outRect: Rect,
            view: View,
            parent: RecyclerView,
            state: RecyclerView.State
        ) {
            super.getItemOffsets(outRect, view, parent, state)
            outRect.set(10*dp, 10*dp, 10*dp, 0)
        }
    }

    override fun onClick(v: View?) {
        when(v?.id) {
            R.id.rclAddBankCard -> {
//                showProgress()
//                paySettingViewModel.queryPWSettingStatus()
                RoutersUtils.openForResult("ybmpage://addbankcard?bindResultFrom=$BIND_RESULT_FROM_PAY_BANK_CARD_LIST", 100)
                XyyIoUtil.track("add_bank_card")
            }
        }
    }

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        showProgress()
        bankCardViewModel.getBankCardList()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == SET_PAY_PASSWORD_RESULT_CODE) {
            RoutersUtils.openForResult("ybmpage://addbankcard?bindResultFrom=$BIND_RESULT_FROM_PAY_BANK_CARD_LIST", 100)
        }
    }
}