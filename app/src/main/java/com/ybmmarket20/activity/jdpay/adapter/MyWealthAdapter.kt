package com.ybmmarket20.activity.jdpay.adapter

import android.graphics.Color
import android.graphics.Typeface
import android.widget.ImageView
import android.widget.TextView
import com.luck.picture.lib.tools.ScreenUtils
import com.ybm.app.adapter.YBMBaseAdapter
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.activity.jdpay.MyWealthActivity
import com.ybmmarket20.bean.MyWealthItem
import com.ybmmarket20.common.YBMAppLike
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.utils.analysis.XyyIoUtil
import com.ybmmarket20.view.homesteady.whenAllNotNull

//银行卡
const val MY_WEALTH_BANK_CARD = 1

//购物金
const val MY_WEALTH_VIRTUAL_MONEY = 2

//平安商户
const val MY_WEALTH_PING_AN_MERCHANT = 3

//平安贷
const val MY_WEALTH_PING_AN = 4

//京东采购融资
const val MY_WEALTH_JD = 5

//农行e链贷
const val MY_WEALTH_NONG_E = 6
//小雨点白条
const val MY_WEALTH_XYD = 7
//金蝶白条
const val MY_WEALTH_JINDIE= 8

/**
 * 我的财富
 */
class MyWealthAdapter(
    list: MutableList<MyWealthItem>,
    private val pingAnCallback: ((MyWealthItem) -> Unit)? = null,
    private val jdCallback: ((MyWealthItem) -> Unit)? = null,
    private val nongCallback: ((MyWealthItem) -> Unit)? = null,
    private val xydCallback:((MyWealthItem) -> Unit)? = null,
    private val jinDieCallback:((MyWealthItem) -> Unit)? = null,
) : YBMBaseAdapter<MyWealthItem>(R.layout.item_wealth, list) {

    override fun bindItemView(baseViewHolder: YBMBaseHolder?, t: MyWealthItem?) {
        whenAllNotNull(baseViewHolder, t) { holder, bean ->
            val wealthState = when (bean.wealthItemType) {
                MY_WEALTH_BANK_CARD -> WealthState.BankCardWealthState()
                MY_WEALTH_VIRTUAL_MONEY -> WealthState.VirtualMoneyWealthState()
                MY_WEALTH_PING_AN_MERCHANT -> WealthState.PingAnMerchantWealthState()
                MY_WEALTH_PING_AN -> WealthState.PingAnWealthState(pingAnCallback)
                MY_WEALTH_JD -> WealthState.JDWealthState(jdCallback)
                MY_WEALTH_NONG_E -> WealthState.NONGWealthState(nongCallback)
                MY_WEALTH_XYD -> WealthState.XYDWealthState(xydCallback)
                MY_WEALTH_JINDIE -> WealthState.JinDieWealthState(jinDieCallback)
                else -> null
            }
            wealthState?.bindData(holder, bean)
            holder.itemView.setOnClickListener {
                wealthState?.onClick(bean)
                MyWealthActivity.jgTrackBtnClick(it.context, bean.title ?:"")
            }
        }
    }

    sealed class WealthState {

        lateinit var icon: ImageView
        lateinit var ivDes: TextView
        lateinit var title: TextView

        abstract fun onClick(bean: MyWealthItem)

        open fun bindData(holder: YBMBaseHolder, bean: MyWealthItem) {
            icon = holder.getView(R.id.ivIcon)
            title = holder.getView(R.id.tvTitle)
            ivDes = holder.getView<TextView?>(R.id.tvDes).apply {
                setBackgroundResource(R.color.white)
                typeface = Typeface.defaultFromStyle(Typeface.BOLD)
                text = bean.tips
            }
        }

        /**
         * 银行卡
         */
        class BankCardWealthState : WealthState() {
            override fun onClick(bean: MyWealthItem) {
                RoutersUtils.open("ybmpage://bankcard")
                XyyIoUtil.track("my_bank_card_click")
            }

            override fun bindData(holder: YBMBaseHolder, bean: MyWealthItem) {
                super.bindData(holder, bean)
                icon.setImageResource(R.drawable.icon_my_wealth_bank_card)
                title.text = bean.title
                ivDes.typeface = Typeface.defaultFromStyle(Typeface.NORMAL)
                ivDes.setTextColor(Color.parseColor("#9494A6"))
            }
        }

        /**
         * 购物金
         */
        class VirtualMoneyWealthState : WealthState() {
            override fun onClick(bean: MyWealthItem) {
                RoutersUtils.open("ybmpage://myvirtualmoney")
                XyyIoUtil.track("my_shopping_gold_click")
            }

            override fun bindData(holder: YBMBaseHolder, bean: MyWealthItem) {
                super.bindData(holder, bean)
                icon.setImageResource(R.drawable.icon_my_wealth_virtual_money)
                title.text = bean.title
                ivDes.setTextColor(Color.parseColor("#676773"))
            }
        }

        /**
         * 我的平安商户
         */
        class PingAnMerchantWealthState : WealthState() {
            override fun onClick(bean: MyWealthItem) {
                RoutersUtils.open(bean.reqUrl)
                XyyIoUtil.track("my_ping_an_merchant_click")
            }

            override fun bindData(holder: YBMBaseHolder, bean: MyWealthItem) {
                super.bindData(holder, bean)
                icon.setImageResource(R.drawable.icon_my_wealth_my_pingan_merchant)
                title.text = bean.title
                ivDes.setTextColor(Color.parseColor("#676773"))
            }
        }

        /**
         * 平安贷
         */
        class PingAnWealthState(private val pingAnCallback: ((MyWealthItem) -> Unit)?) :
            WealthState() {
            override fun onClick(bean: MyWealthItem) {
                if (bean.reqUrl.isNullOrEmpty()) {
                    pingAnCallback?.invoke(bean)
                } else {
                    RoutersUtils.open(bean.reqUrl)
                }
                XyyIoUtil.track("my_ping_an_loan_click")
            }

            override fun bindData(holder: YBMBaseHolder, bean: MyWealthItem) {
                super.bindData(holder, bean)
                icon.setImageResource(R.drawable.icon_my_wealth_pingan)
                title.text = bean.title
//                if (bean.pingAnCreditState == 0) {
//                    //未申请 添加背景
//                    ivDes.setBackgroundResource(R.drawable.shape_pingan_gradient)
//                    ivDes.setTextColor(Color.parseColor("#ffffff"))
//                    ivDes.setPadding(
//                        ScreenUtils.dip2px(YBMAppLike.getApp().currActivity, 8F),
//                        ScreenUtils.dip2px(YBMAppLike.getApp().currActivity, 2F),
//                        ScreenUtils.dip2px(YBMAppLike.getApp().currActivity, 8F),
//                        ScreenUtils.dip2px(YBMAppLike.getApp().currActivity, 2F)
//                    )
//                } else {
//                    ivDes.setTextColor(Color.parseColor("#676773"))
//                }
                //未申请 添加背景
                ivDes.setBackgroundResource(R.drawable.shape_pingan_gradient)
                ivDes.setTextColor(Color.parseColor("#ffffff"))
                ivDes.setPadding(
                    ScreenUtils.dip2px(YBMAppLike.getApp().currActivity, 8F),
                    ScreenUtils.dip2px(YBMAppLike.getApp().currActivity, 2F),
                    ScreenUtils.dip2px(YBMAppLike.getApp().currActivity, 8F),
                    ScreenUtils.dip2px(YBMAppLike.getApp().currActivity, 2F)
                )
            }
        }

        class JDWealthState(private val jdCallback: ((MyWealthItem) -> Unit)?): WealthState() {
            override fun onClick(bean: MyWealthItem) {
                jdCallback?.invoke(bean)
            }

            override fun bindData(holder: YBMBaseHolder, bean: MyWealthItem) {
                super.bindData(holder, bean)
                icon.setImageResource(R.drawable.icon_my_wealth_jd)
                title.text = bean.title
                ivDes.setBackgroundResource(R.drawable.shape_pingan_gradient)
                ivDes.setTextColor(Color.parseColor("#ffffff"))
                ivDes.setPadding(
                    ScreenUtils.dip2px(YBMAppLike.getApp().currActivity, 8F),
                    ScreenUtils.dip2px(YBMAppLike.getApp().currActivity, 2F),
                    ScreenUtils.dip2px(YBMAppLike.getApp().currActivity, 8F),
                    ScreenUtils.dip2px(YBMAppLike.getApp().currActivity, 2F)
                )
            }
        }

        class NONGWealthState(private val nongCallback: ((MyWealthItem) -> Unit)?) : WealthState() {
            override fun onClick(bean: MyWealthItem) {
               nongCallback?.invoke(bean)
            }

            override fun bindData(holder: YBMBaseHolder, bean: MyWealthItem) {
                super.bindData(holder, bean)
                icon.setImageResource(R.drawable.icon_my_wealth_nong)
                title.text = bean.title
                ivDes.setTextColor(Color.parseColor("#676773"))
            }

        }

        class XYDWealthState(private val xydCallback: ((MyWealthItem) -> Unit)?) : WealthState() {
            override fun onClick(bean: MyWealthItem) {
                xydCallback?.invoke(bean)
            }

            override fun bindData(holder: YBMBaseHolder, bean: MyWealthItem) {
                super.bindData(holder, bean)
                icon.setImageResource(R.drawable.icon_my_wealth_xyd)
                title.text = bean.title
                ivDes.setTextColor(Color.parseColor("#676773"))
                ivDes.typeface = Typeface.defaultFromStyle(Typeface.BOLD)
            }
        }
        class JinDieWealthState(private val jinDieCallback: ((MyWealthItem) -> Unit)?) : WealthState() {
            override fun onClick(bean: MyWealthItem) {
                jinDieCallback?.invoke(bean)
            }

            override fun bindData(holder: YBMBaseHolder, bean: MyWealthItem) {
                super.bindData(holder, bean)
                icon.setImageResource(R.drawable.icon_my_wealth_jindie)
                when(bean.state){
                    0->{
                        ivDes.setBackgroundResource(R.drawable.shape_pingan_gradient)
                        ivDes.setTextColor(Color.parseColor("#ffffff"))
                        ivDes.setPadding(
                            ScreenUtils.dip2px(YBMAppLike.getApp().currActivity, 8F),
                            ScreenUtils.dip2px(YBMAppLike.getApp().currActivity, 2F),
                            ScreenUtils.dip2px(YBMAppLike.getApp().currActivity, 8F),
                            ScreenUtils.dip2px(YBMAppLike.getApp().currActivity, 2F)
                        )
                    }
                    3->{
                        ivDes.setTextColor(Color.parseColor("#333333"))
                        ivDes.typeface = Typeface.defaultFromStyle(Typeface.BOLD)
                    }
                    4->{
                        ivDes.setTextColor(Color.parseColor("#FF2A00"))
                        ivDes.typeface = Typeface.defaultFromStyle(Typeface.BOLD)
                    }
                    9->{
                        ivDes.setTextColor(Color.parseColor("#FF2A00"))
                        ivDes.typeface = Typeface.defaultFromStyle(Typeface.BOLD)
                    }
                    else->{
                        ivDes.setTextColor(Color.parseColor("#676773"))
                    }
                }
                title.text = bean.title

            }
        }
    }

}