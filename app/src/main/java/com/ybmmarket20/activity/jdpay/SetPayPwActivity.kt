package com.ybmmarket20.activity.jdpay

import android.graphics.Color
import android.text.Editable
import android.view.ActionMode
import android.view.Menu
import android.view.MenuItem
import android.view.View
import android.widget.Toast
import androidx.activity.viewModels
import androidx.core.widget.addTextChangedListener
import androidx.lifecycle.lifecycleScope
import com.github.mzule.activityrouter.annotation.Router
import com.ybmmarket20.R
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.common.util.ToastUtils
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.view.disableCopy
import com.ybmmarket20.viewmodel.SetPayPWViewModel
import kotlinx.android.synthetic.main.activity_set_pay_pw.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.*
import java.util.function.Function

/**
 * 设置支付密码
 */

//设置密码
const val SET_PAY_PASSWORD_SETTING = "1"
//修改密码
const val SET_PAY_PASSWORD_MODIFY = "2"
//payment设置支付密码
const val SET_PAY_PASSWORD_SETTING_PAY = "3"
//生成订单后修改密码
const val SET_PAY_PASSWORD_MODIFY_ORDER = "4"

const val SET_PAY_PASSWORD_RESULT_CODE = 101

const val SET_PAY_PASSWORD_RESULT_CODE_BACK = 102
const val SET_PAY_PASSWORD_RESULT_ORDER = 103


@Router("setpaypw")
class SetPayPwActivity: BaseActivity(), View.OnClickListener {

    private val setPayPWViewModel: SetPayPWViewModel by viewModels()
    private var settingStatus = ""

    override fun getContentViewId(): Int = R.layout.activity_set_pay_pw

    override fun initData() {
        settingStatus = intent.getStringExtra("settingStatus")?: ""
        setTitle(if(settingStatus == SET_PAY_PASSWORD_MODIFY) "修改支付密码" else "设置支付密码")
        observeData()
        setPayPWViewModel.getUserInfoByMerchantId()
        tvCodeBtn.setOnClickListener(this)
        rtvSubmit.setOnClickListener(this)
//        etInputPW.disableCopy(true)
//        etInputPWSecond.disableCopy(true)
        etInputPW.isLongClickable = false
        etInputPWSecond.isLongClickable = false
        ivBack.setOnClickListener {
            if (settingStatus == SET_PAY_PASSWORD_MODIFY_ORDER) {
                setResult(SET_PAY_PASSWORD_RESULT_ORDER)
            } else if (settingStatus != SET_PAY_PASSWORD_SETTING_PAY) {
                setResult(SET_PAY_PASSWORD_RESULT_CODE_BACK)
            }
            finish()
        }
    }

    private fun observeData() {
        setPayPWViewModel.userLiveData.observe(this) {
            tvSendCodeTips.text =
                """
                收不到验证码？
                请确认${handlePhone(it?.phone?: "").first}是否能正常正使用，若该手机号已停用，请联系销售运营更换手机号，您还可以尝试：
                1.确认短信是否被手机安全软件拦截或折叠隐藏；
                2.查看手机网络状况是否良好，是否可正常接收其他号码短信。
                """.trimIndent()
        }

        setPayPWViewModel.verifyCodeLiveData.observe(this) {
            dismissProgress()
            if (it.isSuccess) {
                tvCodeBtn.text = "重新获取(${it.data.expireTime}s)"
                tvCodeBtn.setTextColor(Color.parseColor("#676773"))
                tvCodeBtn.isEnabled = false
                countDown(it.data.expireTime)
                tvCodeTips.text = "已向${handlePhone(setPayPWViewModel.userLiveData.value?.phone?: "").first}发送验证码，注意查收"
            }
        }

        setPayPWViewModel.jdPWLiveData.observe(this) {
            dismissProgress()
            if (it.isSuccess) {
                if (settingStatus == SET_PAY_PASSWORD_MODIFY) {
                    ToastUtils.showShort("修改成功")
                } else {
                    ToastUtils.showShort("设置成功")
                }

                if (settingStatus == SET_PAY_PASSWORD_MODIFY_ORDER) {
                    setResult(SET_PAY_PASSWORD_RESULT_ORDER)
                } else if (settingStatus != SET_PAY_PASSWORD_SETTING_PAY) {
                    setResult(SET_PAY_PASSWORD_RESULT_CODE)
                }
                finish()
            }
        }
    }

    /**
     * 倒计时
     */
    private fun countDown(time: Int) {
        flow {
            for (i in time downTo 0) {
                emit(i)
                delay(1000)
            }
        }
        .flowOn(Dispatchers.Default)
        .onCompletion {
            tvCodeBtn.text = "获取验证码"
            tvCodeBtn.setTextColor(Color.parseColor("#00B377"))
            tvCodeBtn.isEnabled = true
            tvCodeTips.text = ""
        }
        .onEach{
            tvCodeBtn.text = "重新获取(${it}s)"
        }
        .flowOn(Dispatchers.Main)
        .launchIn(lifecycleScope)

    }

    private fun handlePhone(phone: String): Pair<String, String> {
        var tempPhone = phone
        if (tempPhone.length != 11) {
            tempPhone = "***********"
        }
        val firstValue = tempPhone.replaceRange(3, 7, "****")
        val secondValue = tempPhone.replaceRange(0, 7, "****")
        return Pair(firstValue, secondValue)
    }

    private fun checkInput(): Boolean {
        tvPWTips.text = ""
        tvPWSecondTips.text = ""
        val inputPW = etInputPW.text.toString()
        val inputPwSecond = etInputPWSecond.text.toString()
        val inputVerifyCode = etVerifyCode.text.toString()
        if(checkPasswordStrength(inputPW)) {
            tvPWTips.text = "密码过于简单，请避免输入相同或连续数字"
            ToastUtils.showShort("密码过于简单，请避免输入相同或连续数字")
            etInputPW.setText("")
            etInputPWSecond.setText("")
        } else if (inputPW.isEmpty()) {
            tvPWTips.text = "请输入密码"
            ToastUtils.showShort("请输入密码")
        } else if (inputPW.length < 6) {
            tvPWTips.text = "请输入6位数字"
            ToastUtils.showShort("请输入6位数字")
        } else if (inputPW != inputPwSecond) {
            tvPWSecondTips.text = "两次密码输入不一致，请重新输入密码"
            ToastUtils.showShort("两次密码输入不一致，请重新输入密码")
        } else if (inputVerifyCode.isEmpty()) {
            ToastUtils.showShort("请输入验证码")
        } else if (inputVerifyCode.length < 6) {
            ToastUtils.showShort("请输入6位验证码")
        } else {
            return true
        }
        return false
    }

    private fun checkPasswordStrength(pw: String): Boolean {
        val checkRule = "000000,111111,222222,333333,444444,555555,666666,777777,888888,999999,012345,123456,234567,345678,456789,987654,876543,765432,654321,543210"
        val list = checkRule.split(",")
        return list.contains(pw)
    }

    override fun onClick(v: View?) {
        when(v?.id) {
            R.id.tvCodeBtn -> {
                //发送验证码
                showProgress()
                setPayPWViewModel.sendVerifyCode()
            }

            R.id.rtvSubmit -> {
                //提交
                if (checkInput()) {
                    showProgress()
                    setPayPWViewModel.setJDPayPassword(etInputPW.text.toString(), etVerifyCode.text.toString())
                }
            }
        }
    }
}