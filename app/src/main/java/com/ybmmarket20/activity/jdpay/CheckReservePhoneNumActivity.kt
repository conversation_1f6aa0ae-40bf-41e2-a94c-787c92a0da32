package com.ybmmarket20.activity.jdpay

import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.text.TextUtils
import android.text.style.ForegroundColorSpan
import android.util.Base64
import android.view.WindowManager
import android.widget.EditText
import androidx.activity.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.github.mzule.activityrouter.annotation.Router
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.ybmmarket20.R
import com.ybmmarket20.bean.BindCardInfo
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.common.util.ToastUtils
import com.ybmmarket20.constant.IntentCanst
import com.ybmmarket20.db.AccountTable
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.utils.SpUtil
import com.ybmmarket20.view.INPUT_PASSWORD_INPUT_ITEM_TYPE_DELETE
import com.ybmmarket20.view.INPUT_PASSWORD_INPUT_ITEM_TYPE_NUM
import com.ybmmarket20.viewmodel.CheckReservePhoneNumViewModel
import kotlinx.android.synthetic.main.activity_check_reserve_phone_num.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.*

/**
 * 预留手机号
 */
@Router("checkreservephonenum")
class CheckReservePhoneNumActivity: BaseActivity() {

    private var bindCardInfo: BindCardInfo? = null
    private val mViewModel: CheckReservePhoneNumViewModel by viewModels()
    private var mobile = ""
    private var isAddBankCard = false

    override fun onCreate(savedInstanceState: Bundle?) {
        window.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_ALWAYS_HIDDEN);
        super.onCreate(savedInstanceState)
    }

    override fun getContentViewId(): Int = R.layout.activity_check_reserve_phone_num

    override fun initData() {
        setTitle("预留手机号验证")
        val params = intent.getStringExtra("params")
        isAddBankCard = params != null
        if (!isAddBankCard) {
            val account = AccountTable.getAccount(this, SpUtil.getMerchantid())
            mobile = account.phone
            tvGetVerifyCode.text = "获取验证码"
            tvGetVerifyCode.setTextColor(Color.parseColor("#00B377"))
            tvGetVerifyCode.isEnabled = true
            showProgress()
            mViewModel.sendVerifyCode()
        } else {
            //添加银行卡
            val base64Code = String(Base64.decode(params, Base64.URL_SAFE))
            bindCardInfo = Gson().fromJson(base64Code, BindCardInfo::class.java)
            mobile = bindCardInfo?.mobile?: ""
            countDown()
        }

        val sendVerifyCodeBuilder = SpannableStringBuilder("已向银行预留的 ")
        val mobileBuilder = SpannableStringBuilder(getIncompleteMobile(mobile))
        val foregroundColorSpan = ForegroundColorSpan(Color.parseColor("#191919"))
        mobileBuilder.setSpan(foregroundColorSpan, 0, mobileBuilder.length, Spanned.SPAN_INCLUSIVE_EXCLUSIVE)
        sendVerifyCodeBuilder.append(mobileBuilder).append(" 手机号发送验证码")
        tvSendVerifyCodeTips.text = sendVerifyCodeBuilder
        tvUnsendVerifyCodeTips.text =
            """
                收不到验证码？
                请确认${getIncompleteMobile(bindCardInfo?.mobile)}是否能正常正使用，若该手机号已停用，请联系银行更换手机号，您还可以尝试：
                1.确认短信是否被手机安全软件拦截或折叠隐藏；
                2.查看手机网络状况是否良好，是否可正常接收其他号码短信。 
            """.trimIndent()
        setListener()
    }

    private fun setListener() {
        tvGetVerifyCode.setOnClickListener {
            showProgress()
            val params = hashMapOf(
                "cardNo" to (bindCardInfo?.cardNum?: ""),
                "cardType" to (bindCardInfo?.cardType?: ""),
                "bankCode" to (bindCardInfo?.bankCode?: ""),
                "mobile" to (bindCardInfo?.mobile?: ""),
                "bankName" to (bindCardInfo?.bankName))
            val idNo = bindCardInfo?.idNo
            if (idNo != null && !idNo.contains("*")) {
                params["idNo"] = (bindCardInfo?.idNo?: "")
                params["idName"] = (bindCardInfo?.idName?: "")
            }
            mViewModel.applyBindCardWithNum(params)
        }
        inputView.setOnInputChange {type, num ->
            when (type) {
                INPUT_PASSWORD_INPUT_ITEM_TYPE_NUM -> {
                    //数字
                    receiveView.inputWord("$num")
                }

                INPUT_PASSWORD_INPUT_ITEM_TYPE_DELETE -> {
                    //删除
                    receiveView.deleteWord()
                }
            }
        }
        receiveView.addTextChangeListener { _: EditText, code: String ->
            if (code.length == 6) {
                // 输入完成
                showProgress()
                if (bindCardInfo == null) {
                    mViewModel.checkVerifyCode(code)
                    return@addTextChangeListener
                }
//                val jsonStr = Gson().toJson(bindCardInfo)
//                val type = object : TypeToken<Map<String, String?>>() {}.type
//                val mapParams = Gson().fromJson<Map<String, String?>>(jsonStr, type).toMutableMap()
//                mapParams["verifyCode"] = code
//                mapParams["bankName"] = bindCardInfo?.idName?: ""
//                mapParams["cardNo"] = bindCardInfo?.idNo?: ""

                val mapParams = mutableMapOf(
                    "verifyCode" to  code,
                    "cardNo" to (bindCardInfo?.cardNum?: ""),
                    "idNo" to (bindCardInfo?.idNo?: ""),
                    "idName" to (bindCardInfo?.idName?: ""),
                    "cardType" to (bindCardInfo?.cardType?: ""),
                    "bankCode" to (bindCardInfo?.bankCode?: ""),
                    "mobile" to mobile,
                    "contractNo" to (bindCardInfo?.contractNo?: ""),
                    "bankName" to (bindCardInfo?.bankName?: "")
                )
                mViewModel.confirmBindCardWithNum(mapParams)
            }
        }

        //（按卡号）提交绑卡申请
        mViewModel.applyBindCardWithNumLiveData.observe(this) {
            dismissProgress()
            if (it.isSuccess) {
                countDown()
            }
        }

        mViewModel.confirmBindCardWithNumLiveData.observe(this) {
            dismissProgress()
            if (it.isSuccess) {
                ToastUtils.showShort("绑卡成功")
                LocalBroadcastManager.getInstance(this).sendBroadcast(Intent(IntentCanst.ACTION_MY_WEALTH_REFRESH))
                RoutersUtils.open("ybmpage://addbankcard")
            }
        }

        mViewModel.verifyCodeLiveData.observe(this) {
            dismissProgress()
            if (it.isSuccess) {
                countDown()
            }
        }
        
        mViewModel.checkVerifyCodeLiveData.observe(this) {
            if (it.isSuccess) setResult(100)
        }
    }

    /**
     * 倒计时
     */
    private fun countDown() {
        flow {
            (60 downTo 0).forEach {
                kotlinx.coroutines.delay(1000)
                emit(it)
            }
        }.flowOn(Dispatchers.Default)
            .onCompletion {
                tvGetVerifyCode.text = "重新获取"
                tvGetVerifyCode.setTextColor(Color.parseColor("#00B377"))
                tvGetVerifyCode.isEnabled = true
            }
            .onEach {
                tvGetVerifyCode.text = "重新获取(${it}s)"
                tvGetVerifyCode.setTextColor(Color.parseColor("#676773"))
                tvGetVerifyCode.isEnabled = false
            }
            .flowOn(Dispatchers.Main)
            .launchIn(lifecycleScope)
    }

    private fun getIncompleteMobile(mobile: String?): String {
        if (TextUtils.isEmpty(mobile) || mobile!!.length != 11) return "***********"
        return mobile.replaceRange(3, 7, "****")
    }
}