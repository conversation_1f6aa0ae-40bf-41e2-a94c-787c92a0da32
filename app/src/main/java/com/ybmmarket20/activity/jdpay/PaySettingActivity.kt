package com.ybmmarket20.activity.jdpay

import android.content.Intent
import android.view.View
import androidx.activity.viewModels
import com.github.mzule.activityrouter.annotation.Router
import com.ybmmarket20.R
import com.ybmmarket20.bean.JDPW_CHECK_TYPE_CHECK_FINGERPRINT
import com.ybmmarket20.bean.JDPW_CHECK_TYPE_INITIALIZE
import com.ybmmarket20.common.AlertDialogEx
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.utils.FingerprintUtil
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.utils.SpUtil
import com.ybmmarket20.utils.analysis.XyyIoUtil
import com.ybmmarket20.view.CheckPayPasswordDialog
import com.ybmmarket20.viewmodel.CheckPayPasswordViewModel
import com.ybmmarket20.viewmodel.JDPWSettingViewModel
import kotlinx.android.synthetic.main.activity_pay_setting.*

@Router("paysetting")
class PaySettingActivity: BaseActivity() {

    private val paySettingViewModel: JDPWSettingViewModel by viewModels()
    private val checkPayPasswordViewModel: CheckPayPasswordViewModel by viewModels()
    private var settingStatus = -1
    private var mCheckPayPasswordDialog: CheckPayPasswordDialog? = null

    //初始化时是否已验证状态
    private var isInitializeStateChecked = false
    //是否需要验证初始化状态
    private var isNeedCheckInitializeState = 0

    override fun getContentViewId(): Int = R.layout.activity_pay_setting

    override fun initData() {
        setTitle("支付设置")
        try {
            isNeedCheckInitializeState = intent.getStringExtra("isNeedCheckInitializeState")?.toInt()?: 0
        } catch (e: Exception) {
            e.printStackTrace()
        }
        initObserver()
        clPayPassword.setOnClickListener {
            if (settingStatus == -1) return@setOnClickListener
            RoutersUtils.open("ybmpage://setpaypw?settingStatus=${if(settingStatus == 1) SET_PAY_PASSWORD_MODIFY else SET_PAY_PASSWORD_SETTING}")
            XyyIoUtil.track("set_payment_password")
        }

        switchDelegate.setOnClickListener {
            switchFingerprint()
        }
    }

    private fun switchFingerprint() {
        if (switchFingerPrint.isChecked) {
            //已经开启
            unRegisterFingerprint()
        } else {
            //未开启,先检测密码设置状态
            showProgress()
            paySettingViewModel.queryPWSettingStatusWithFingerprint(JDPW_CHECK_TYPE_CHECK_FINGERPRINT)
        }
    }

    private fun initObserver() {
        //查看设备状态
        paySettingViewModel.jDPWSettingLiveData.observe(this) {
            dismissProgress()
            tvPWStatus.text = "去设置"
            if (it.isSuccess) {
                settingStatus = it.data.pwSettingStatus
                if (it.data.pwSettingStatus == 1) {
                    tvPWStatus.text = "去修改"
                    tvPWDes.visibility = View.GONE
                }
                //是否支持指纹
                clFingerprint.visibility = if (it.data.deviceStatusOnPay?.switchStatus == 1
                    && FingerprintUtil.checkFingerprintSupported(this)) View.VISIBLE else View.GONE
                if (it.data.deviceStatusOnPay?.switchStatus == 0) return@observe
                //设置指纹功能是否已经开启
                if (it.data.checkType == JDPW_CHECK_TYPE_CHECK_FINGERPRINT) {
                    if (it.data.pwSettingStatus == 1) {
                        checkPayPw()
                    } else {
                        goSettingPw()
                    }
                } else if (it.data.checkType == JDPW_CHECK_TYPE_INITIALIZE) {
                    switchFingerPrint.isChecked = paySettingViewModel.getPayFingerprintStatus()
                }
                if (!isInitializeStateChecked && isNeedCheckInitializeState == 1) {
                    isInitializeStateChecked = true
                    switchFingerprint()
                }
            }
        }

        //关闭指纹
        paySettingViewModel.registerFingerprintLiveData.observe(this) {
            if (it.isSuccess) {
                SpUtil.setPayFingerprintStatus(false)
                paySettingViewModel.queryPWSettingStatusWithFingerprint(JDPW_CHECK_TYPE_INITIALIZE)
            } else dismissProgress()
        }
    }

    private fun checkPayPw() {
        if (mCheckPayPasswordDialog == null) {
            mCheckPayPasswordDialog = CheckPayPasswordDialog(this, checkPayPasswordViewModel)
        }
        mCheckPayPasswordDialog?.apply {
            setOnCheckPasswordCallback {
                dismissProgress()
                RoutersUtils.openForResult("ybmpage://enablefingerprintcheck", 100)
            }
            setOnForgetPassword {
                RoutersUtils.open("ybmpage://setpaypw?settingStatus=$SET_PAY_PASSWORD_MODIFY")
                dismiss()
            }
            this.setTitle("输入支付密码验证身份")
            setInit(isPayCheck = false)
            clearReceive()
            show()
        }
    }

    override fun onResume() {
        super.onResume()
        showProgress()
        paySettingViewModel.queryPWSettingStatusWithFingerprint(JDPW_CHECK_TYPE_INITIALIZE)
    }

    /**
     * 去设置密码
     */
    private fun goSettingPw() {
        AlertDialogEx(this)
            .setTitle(null)
            .setMessage("请先设置支付密码\n" +
                    "再开启指纹支付")
            .setCorner()
            .setCancelButton("取消", "#9494A5"){_, _-> }
            .setConfirmButton("去设置", "#00B377") {_, _ ->
//                RoutersUtils.openForResult("ybmpage://setpaypw?settingStatus=$SET_PAY_PASSWORD_MODIFY", 101)
                RoutersUtils.open("ybmpage://setpaypw?settingStatus=$SET_PAY_PASSWORD_SETTING")
            }.show()
    }

    /**
     * 关闭指纹
     */
    private fun unRegisterFingerprint() {
        AlertDialogEx(this)
            .setTitle(null)
            .setMessage("确定要关闭指纹支付吗？")
            .setCorner()
            .setCancelButton("仍要关闭", "#9494A5"){_, _->
                showProgress()
                paySettingViewModel.registerAndUpdateFingerprint("0")
            }
            .setConfirmButton("继续使用", "#00B377") {_, _ -> }
            .show()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == 101) {
            RoutersUtils.open("ybmpage://enablefingerprintcheck")
        } else if (requestCode == 100) {
            //设置指纹
            switchFingerPrint.isChecked = true
        }
    }
}