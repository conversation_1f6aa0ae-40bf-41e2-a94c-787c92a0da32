package com.ybmmarket20.activity.jdpay

import com.github.mzule.activityrouter.annotation.Router
import com.ybmmarket20.R
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.utils.RoutersUtils
import kotlinx.android.synthetic.main.activity_add_bank_card_fail_result.*
import kotlinx.android.synthetic.main.common_header_items.*

/**
 * 添加银行卡失败页面
 */
@Router("addbankcardfailresult")
class AddBankCardFailResultActivity: BaseActivity() {
    override fun getContentViewId(): Int = R.layout.activity_add_bank_card_fail_result

    override fun initData() {
        iv_back.setImageResource(R.drawable.icon_close)
        setLeft {
            RoutersUtils.open("ybmpage://bankcard")
        }
        val tips = intent.getStringExtra("failMsg")
        tvTips.text = tips
        rtvFinish.setOnClickListener {
            RoutersUtils.open("ybmpage://bankcard")
        }
    }

    override fun onBackPressed() {
        super.onBackPressed()
        RoutersUtils.open("ybmpage://bankcard")
    }
}