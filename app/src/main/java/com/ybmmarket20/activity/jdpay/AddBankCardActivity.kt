package com.ybmmarket20.activity.jdpay

import android.content.Intent
import android.graphics.Rect
import android.os.Bundle
import android.text.TextUtils
import android.util.Base64
import android.view.View
import android.view.WindowManager
import androidx.activity.viewModels
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.github.mzule.activityrouter.annotation.Router
import com.google.gson.Gson
import com.ybmmarket20.R
import com.ybmmarket20.activity.jdpay.adapter.AddBankCardAdapter
import com.ybmmarket20.bean.ADD_BANK_CARD_HEADER
import com.ybmmarket20.bean.ADD_BANK_CARD_INPUT_NUM
import com.ybmmarket20.bean.BankCardItemHeader
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.common.util.ConvertUtils
import com.ybmmarket20.common.util.ToastUtils
import com.ybmmarket20.utils.AlertDialogHtml
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.viewmodel.AddBankViewModel
import kotlinx.android.synthetic.main.activity_add_bank_card.*

//从收银台开始绑卡
const val BIND_RESULT_FROM_PAY_WAY = "1"
//从银行卡列表开始绑卡
const val BIND_RESULT_FROM_PAY_BANK_CARD_LIST = "2"
//从提单页开始绑卡
const val BIND_RESULT_FROM_PAYMENT = "3"

/**
 * 添加银行卡
 */
@Router("addbankcard")
class AddBankCardActivity: BaseActivity() {

    val mViewModel: AddBankViewModel by viewModels()
    private var bindResultFrom = BIND_RESULT_FROM_PAY_BANK_CARD_LIST

    override fun onCreate(savedInstanceState: Bundle?) {
        window.setSoftInputMode( WindowManager.LayoutParams.SOFT_INPUT_STATE_HIDDEN);
        super.onCreate(savedInstanceState)
    }

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        val status = intent?.getStringExtra("status")?: "4"
        val router = if (bindResultFrom == BIND_RESULT_FROM_PAY_WAY) {
            "ybmpage://paywayactivity"
        } else if(bindResultFrom == BIND_RESULT_FROM_PAYMENT) {
            "ybmpage://payment?status=$status"
        } else {
            "ybmpage://bankcard"
        }
        RoutersUtils.open(router)
    }

    override fun getContentViewId(): Int = R.layout.activity_add_bank_card

    override fun initData() {
        setTitle("添加银行卡")
        bindResultFrom = intent?.getStringExtra("bindResultFrom")?: BIND_RESULT_FROM_PAY_BANK_CARD_LIST
        rv.layoutManager = LinearLayoutManager(this, LinearLayoutManager.VERTICAL, false)
        rv.addItemDecoration(AddBankCardItemDecoration())
        setObserver()
        showProgress()
        mViewModel.getSignBankInitList()
    }

    private fun setObserver() {
        mViewModel.signBankListLiveData.observe(this) {
            dismissProgress()
            if (it.isSuccess) {
                rv.adapter = it.data.bankNodeList?.let { it1 -> AddBankCardAdapter(it1, mViewModel) }
            }
        }

        mViewModel.cardBindLiveData.observe(this) {
            dismissProgress()
            if (it.isSuccess) {
                if (it.data.bindState == 1) {
                    ToastUtils.showLong(it.data.message)
                } else {
                    RoutersUtils.open("ybmpage://addbankcarddetailwithnum?bankCardNo=${it.data.cardNo}")
                }
            }
        }

        mViewModel.payTypeItemClickLiveData.observe(this) {
            if (TextUtils.isEmpty(it.first)) {
                if (TextUtils.isEmpty(it.second)) {
                    mViewModel.switchByActiveEdit()
                } else {
                    RoutersUtils.open(it.second)
                }
            } else {
                AlertDialogHtml.showAlertDialogAuthorization(this, it.first?:"", it.second) {router ->
                    if (router == null) {
                        mViewModel.switchByActiveEdit()
                    } else {
                        RoutersUtils.open(router)
                    }
                }
            }
        }

        mViewModel.bankCardInfoLiveData.observe(this) {
            dismissProgress()
            if (it.isSuccess) {
                try {
                    val params = Base64.encodeToString(Gson().toJson(it.data).toByteArray(), Base64.URL_SAFE)
                    RoutersUtils.open("ybmpage://addbankcarddetailwithnum?bankCardInfo=$params")
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
        }
    }

    inner class AddBankCardItemDecoration: RecyclerView.ItemDecoration() {

        private val dp = ConvertUtils.dp2px(1f)

        override fun getItemOffsets(
            outRect: Rect,
            view: View,
            parent: RecyclerView,
            state: RecyclerView.State
        ) {
            super.getItemOffsets(outRect, view, parent, state)
            val list = mViewModel.signBankListLiveData.value?.data?.bankNodeList ?: return
            val childLayoutPosition = parent.getChildLayoutPosition(view)
            val itemBean = list[childLayoutPosition]
            when(itemBean.itemType) {
                ADD_BANK_CARD_HEADER -> {
                    val headerBean = itemBean as BankCardItemHeader
                    if (headerBean.isExpand) {
                        outRect.bottom = dp
                    }
                }

                ADD_BANK_CARD_INPUT_NUM -> {
                    outRect.top = 10 * dp
                }
            }
        }
    }


}