package com.ybmmarket20.view.searchFilter.adapter

import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.bean.searchfilter.FullSearchFilterBean

/**
 * 搜索过滤条件，价格区间Adapter
 */
class SearchFilterPriceRangeAdapter(list: MutableList<FullSearchFilterBean> = mutableListOf()) : AbsSearchFilterAdapter<FullSearchFilterBean>(list, R.layout.item_search_filter_service) {
    override fun getItemSpanSize(): Int = SEARCH_FILTER_SPAN_SIZE_PRICE_RANGE

    override fun getAdapterType(): Int = SEARCH_FILTER_DATA_TYPE_PRICE_RANGE

    override fun bindItemViewData(holder: YBMBaseHolder, bean: FullSearchFilterBean) {

    }

}