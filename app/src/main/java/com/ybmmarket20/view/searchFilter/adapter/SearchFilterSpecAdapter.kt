package com.ybmmarket20.view.searchFilter.adapter

import android.graphics.Color
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.style.ForegroundColorSpan
import android.widget.CheckBox
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.bean.SearchFilterBean
import com.ybmmarket20.bean.searchfilter.FullSearchFilterBean
import com.ybmmarket20.bean.searchfilter.SearchFilterContentBean

/**
 * 搜索筛选
 */
class SearchFilterSpecAdapter(list: MutableList<FullSearchFilterBean> = mutableListOf()) : AbsSearchFilterAdapter<FullSearchFilterBean>(list, R.layout.item_search_filter_spec) {
    override fun getItemSpanSize(): Int = SEARCH_FILTER_SPAN_SIZE_SPEC

    override fun getAdapterType(): Int = SEARCH_FILTER_DATA_TYPE_SPEC

    override fun bindItemViewData(holder: <PERSON><PERSON>BaseHolder, fullSearchFilterBean: FullSearchFilterBean) {
        if (fullSearchFilterBean is SearchFilterContentBean<*> && fullSearchFilterBean.contentBean is SearchFilterBean) {
            val bean = fullSearchFilterBean.contentBean as SearchFilterBean
            val cbItem = holder.getView<CheckBox>(R.id.cb_item)
            val specCount = if (!bean.specCount.isNullOrEmpty()) " (${bean.specCount})" else ""
            val specCountSpannable = SpannableStringBuilder(specCount).also {
                it.setSpan(ForegroundColorSpan(Color.parseColor(if(bean.isSelected) "#00B377" else "#9090A1")), 0, it.length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
            }
            cbItem.text = SpannableStringBuilder(bean.key ?: "").append(specCountSpannable)
            cbItem.isChecked = bean.isSelected
            holder.setOnClickListener(R.id.ll_item) {
                bean.apply {
                    isSelected = !isSelected
                    notifyDataSetChanged()
                }
            }
        }
    }
}