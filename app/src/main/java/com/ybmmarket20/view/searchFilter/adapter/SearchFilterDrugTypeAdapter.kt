package com.ybmmarket20.view.searchFilter.adapter

import android.widget.CheckBox
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.bean.searchfilter.SearchFilterDrugType
import com.ybmmarket20.bean.searchfilter.FullSearchFilterBean
import com.ybmmarket20.bean.searchfilter.SearchFilterContentBean

/**
 * 搜索过滤条件，药品类型Adapter
 */
class SearchFilterDrugTypeAdapter(list: MutableList<FullSearchFilterBean> = mutableListOf()) :
    AbsSearchFilterAdapter<FullSearchFilterBean>(list, R.layout.item_search_filter_spec) {
    override fun getItemSpanSize(): Int = SEARCH_FILTER_SPAN_SIZE_DRUG_TYPE

    override fun getAdapterType(): Int = SEARCH_FILTER_DATA_TYPE_DRUG_TYPE

    override fun bindItemViewData(holder: <PERSON><PERSON><PERSON><PERSON><PERSON>older, bean: FullSearchFilterBean) {
        if (bean is SearchFilterContentBean<*> && bean.contentBean is SearchFilterDrugType) {
            val cb = holder.getView<CheckBox>(R.id.cb_item)
            cb.text = (bean.contentBean as SearchFilterDrugType).text
        }
    }
}