package com.ybmmarket20.view.searchFilter.adapter

import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarketkotlin.adapter.YBMBaseMultiItemAdapter
import com.ybmmarket20.bean.searchfilter.FullSearchFilterBean
import com.ybmmarket20.view.homesteady.whenAllNotNull

open class BaseSearchFilterAdapter<T: FullSearchFilterBean>(list: MutableList<T>): YBMBaseMultiItemAdapter<T>(list) {

    private val dataTypeMapping = mutableMapOf<Int, AbsSearchFilterAdapter<T>>()

    override fun bindItemView(baseViewHolder: YBMBaseHolder?, t: T) {
        whenAllNotNull(baseViewHolder, t) {holder, bean ->
            val adapter = dataTypeMapping[bean.itemType]
            adapter?.bindItemViewData(holder, bean)
        }
    }

    fun registerAdapter(adapterList: MutableList<AbsSearchFilterAdapter<T>>) {
        adapterList.forEach {
            dataTypeMapping[it.getAdapterType()] = it
            addItemType(it.getAdapterType(), mLayoutResId)
        }
    }
}