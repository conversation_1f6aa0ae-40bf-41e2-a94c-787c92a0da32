package com.ybmmarket20.view.searchFilter.view

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Color
import android.graphics.drawable.Drawable
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.style.ForegroundColorSpan
import android.util.AttributeSet
import android.widget.CheckBox
import android.widget.TextView
import androidx.activity.ComponentActivity
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.android.flexbox.FlexboxLayoutManager
import com.google.gson.reflect.TypeToken
import com.luck.picture.lib.tools.ScreenUtils
import com.ybm.app.adapter.YBMBaseAdapter
import com.ybm.app.adapter.YBMBaseHolder
import com.ybm.app.utils.JsonUtils
import com.ybmmarket20.R
import com.ybmmarket20.bean.SearchDynamicLabelConfig
import com.ybmmarket20.bean.SearchDynamicLabelItem
import com.ybmmarket20.view.homesteady.whenAllNotNull
import com.ybmmarket20.viewmodel.SearchDataViewModel

/**
 * @desc 大搜->筛选->动态标签 抽离的 中药扩展属性
 */
class SearchDynamicLabelExtView @JvmOverloads constructor(context: Context, attrs: AttributeSet? = null, def: Int = 0) :
    ConstraintLayout(context, attrs, def) {
    private var tvTitle: TextView? = null
    private var tvCollas: TextView? = null
    private var rv: RecyclerView? = null
    var mSelectedItemCallback: ((config: SearchDynamicLabelConfig?, data: List<String>?) -> Unit)? = null
    var mAdapter: DynamicLabelExtAdapter? = null
    var selectedList: HashMap<Int, String> = hashMapOf()
    private var mConfig: SearchDynamicLabelConfig? = null
    private var isCollapse = false
    var collapseListener: ((String, Boolean) -> Unit)? = null

    init {
        inflate(context, R.layout.layout_search_dynamic_cnmedine_label, this)
        initData()
    }

    /**
     * 初始化数据
     */
    fun initData() {
        rv = findViewById<RecyclerView>(R.id.rvCnMedineExt)
    }

    fun setData(vm: SearchDataViewModel, data: SearchDynamicLabelConfig, collaspe: Boolean? = false) {
        this.mConfig = data
        this.isCollapse = collaspe == true
        if (1 == data.isDynamicParam) {
            rv?.layoutManager = FlexboxLayoutManager(context)
            mAdapter = DynamicLabelExtAdapter(R.layout.item_dynamic_label_ext_grid_flex, null)
        } else {
            rv?.layoutManager = GridLayoutManager(context, 3)
            mAdapter = DynamicLabelExtAdapter(R.layout.item_dynamic_label_ext_grid, null)
        }
        rv?.adapter = mAdapter
        // 选中监听：每次Classificy show的时候会更新生效的筛选条件，未生效会被重置(做了选择，未提交接口)
        vm.dynamicLabelSelectedLiveData.observe(context as ComponentActivity) { param ->
            updateSeleted(data, param)
        }
        //初始化选中列表
        if (!data.items.isNullOrEmpty()) {
            data.items.forEachIndexed { index, item ->
                if (item.getSelectedStatus()) {
                    selectedList[index] = item.itemKey
                }
            }
        }
        tvTitle = findViewById<TextView>(R.id.tvCnMedineExtTitle)
        tvCollas = findViewById<TextView>(R.id.tvCnMedineExtCollapse)
        tvTitle?.text = data.labelName
        if (rv?.layoutManager is GridLayoutManager) {
            setGridColla(data)
        } else {
            setFlexColla(data)
        }
    }

    /**
     * 更新选中
     */
    private fun updateSeleted(data: SearchDynamicLabelConfig, param: Map<String, String>) {
        val json = param[data.paramKey]
        // json为空：未选中
        if (json.isNullOrEmpty()) {
            data.items?.forEach { item ->
                item.setSelectedStatus(false)
            }
            mAdapter?.notifyDataSetChanged()
            return
        }
        if (1 == data.isDynamicParam) {
            // 子数组判空设置状态
            val extMap = JsonUtils.fromJson<HashMap<String, List<String>>>(json, object : TypeToken<HashMap<String, List<String>>>() {}.type)
            val extList = extMap?.get(data.dynamicParamKey)
            if (extList.isNullOrEmpty()) {
                data.items?.forEach { item ->
                    item.setSelectedStatus(false)
                }
                mAdapter?.notifyDataSetChanged()
                return
            }
            data.items?.forEach { item ->
                item.setSelectedStatus(extList.contains(item.itemKey))
            }
            mAdapter?.notifyDataSetChanged()
        } else {
            // 产地：列表判空设置状态
            val products = JsonUtils.fromJson<List<String>>(json)
            if (products.isNullOrEmpty()) {
                data.items?.forEach { item ->
                    item.setSelectedStatus(false)
                }
                mAdapter?.notifyDataSetChanged()
                return
            }
            data.items?.forEach { item ->
                item.setSelectedStatus(products.contains(item.itemKey))
            }
            mAdapter?.notifyDataSetChanged()
        }
    }

    /**
     * 计算两行高度能完整容纳的 Item 数量
     */
    private fun calculateVisibleItemCount(): Int {
        val flexLines = (rv?.layoutManager as? FlexboxLayoutManager)?.flexLines
        if ((flexLines?.size ?: 0) >= 2) {
            val firstCount = flexLines!![0].itemCount
            val secondCount = flexLines!![1].itemCount
            return firstCount + secondCount
        } else {
            return mAdapter?.itemCount ?: 0
        }
    }

    var visibleItemCount = -1

    /**
     * 动态标签
     */
    private fun setFlexColla(data: SearchDynamicLabelConfig) {
        mAdapter?.setNewData(data.items!!)
        rv?.post {
            visibleItemCount = calculateVisibleItemCount()
            if (visibleItemCount < (data.items?.size ?: 0)) {
                tvCollas?.visibility = VISIBLE
                if (!isCollapse && (data.items?.size ?: 0) > visibleItemCount) {
                    mAdapter?.setNewData(data.items!!.subList(0, visibleItemCount).toList())
                } else {
                    mAdapter?.setNewData(data.items!!)
                }
            } else {
                tvCollas?.visibility = GONE
            }
        }
        setArrow()
        setOnClickListener {
            isCollapse = !isCollapse
            if (!isCollapse) {
                mAdapter?.setNewData(data.items!!.subList(0, visibleItemCount).toList())
            } else {
                mAdapter?.setNewData(data.items!!)
            }
            setArrow()
            if (!data.labelKey.isNullOrEmpty()) {
                collapseListener?.invoke(data.labelKey!!, isCollapse)
            }
        }
    }

    /**
     * 产地扩展展示、点击
     */
    private fun setGridColla(data: SearchDynamicLabelConfig) {
        tvCollas?.visibility = if ((data.items?.size ?: 0) > 6) {
            VISIBLE
        } else {
            GONE
        }
        if (!isCollapse && (data.items?.size ?: 0) > 6) {
            mAdapter?.setNewData(data.items!!.subList(0, 6).toList())
        } else {
            mAdapter?.setNewData(data.items!!)
        }
        setArrow()
        setOnClickListener {
            isCollapse = !isCollapse
            mAdapter?.setNewData(
                if (!isCollapse) {
                    data.items!!.subList(0, 6).toList()
                } else {
                    data.items!!
                }
            )
            setArrow()
            if (!data.labelKey.isNullOrEmpty()) {
                collapseListener?.invoke(data.labelKey!!, isCollapse)
            }
        }
    }

    private fun setArrow() {
        val drawable: Drawable = context.getResources()
            .getDrawable(if (!isCollapse) R.drawable.icon_search_filter_title_arrow_down else R.drawable.icon_search_filter_title_arrow_up)
        tvCollas?.text = if (!isCollapse) "展开" else "收起"
        drawable.setBounds(0, 0, drawable.getMinimumWidth(), drawable.getMinimumHeight())
        tvCollas?.setCompoundDrawables(null, null, drawable, null)
    }

    fun setOnSelectedItemCallback(callback: ((config: SearchDynamicLabelConfig?, data: List<String>?) -> Unit)?) {
        mSelectedItemCallback = callback
    }

    @SuppressLint("NotifyDataSetChanged")
    fun reset() {
        selectedList.clear()
        this.mConfig?.setSelectedStatus(false)
        mAdapter?.let {
            val data = it.data
            data.forEach { item ->
                (item as SearchDynamicLabelItem).setSelectedStatus(false)
            }
            it.notifyDataSetChanged()
        }
    }

    inner class DynamicLabelExtAdapter(layoutResId: Int, val list: MutableList<SearchDynamicLabelItem>?) :
        YBMBaseAdapter<SearchDynamicLabelItem>(layoutResId, list) {

        @SuppressLint("NotifyDataSetChanged")
        override fun bindItemView(baseViewHolder: YBMBaseHolder, t: SearchDynamicLabelItem?) {
            whenAllNotNull(baseViewHolder, t) { holder, bean ->
                val cbItem = baseViewHolder.getView<CheckBox>(R.id.tvDynamicLabel)
                val specCount = if (bean.itemCount != 0) " (${bean.itemCount})" else ""
                val specCountSpannable = SpannableStringBuilder(specCount).also {
                    it.setSpan(
                        ForegroundColorSpan(Color.parseColor(if (bean.getSelectedStatus()) "#00B377" else "#9090A1")),
                        0,
                        it.length,
                        Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                    )
                }
                val key = if (1 == mConfig?.isDynamicParam) {
                    cbItem.minWidth = (ScreenUtils.getScreenWidth(mContext) - ScreenUtils.dip2px(mContext,45f)) / 3
                    bean.itemName
                } else {
                    if (bean.itemName.length > 5) "${bean.itemName.subSequence(0, 5)}..." else bean.itemName
                }
                cbItem.text = SpannableStringBuilder(key).append(specCountSpannable)
                cbItem.isChecked = bean.getSelectedStatus()
                holder.setOnClickListener(R.id.tvDynamicLabel) {
                    bean.apply {
                        if (bean.getSelectedStatus()) {
                            bean.setSelectedStatus(false)
                            selectedList.remove(holder.layoutPosition)
                            mConfig?.setSelectedStatus(!selectedList.isEmpty())
                        } else {
                            bean.setSelectedStatus(true)
                            mConfig?.setSelectedStatus(true)
                            selectedList.put(holder.layoutPosition, bean.itemKey)
                        }
                        mSelectedItemCallback?.invoke(mConfig, selectedList.map { it.value })
                        notifyDataSetChanged()
                    }
                }
            }
        }

    }
}