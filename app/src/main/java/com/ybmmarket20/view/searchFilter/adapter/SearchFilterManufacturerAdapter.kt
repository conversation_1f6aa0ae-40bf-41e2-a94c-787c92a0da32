package com.ybmmarket20.view.searchFilter.adapter

import android.widget.TextView
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.bean.ManufacturersBean
import com.ybmmarket20.bean.searchfilter.FullSearchFilterBean
import com.ybmmarket20.bean.searchfilter.SearchFilterContentBean

/**
 * 搜索过滤条件，厂家Adapter
 */
class SearchFilterManufacturerAdapter(list: MutableList<FullSearchFilterBean> = mutableListOf()) : AbsSearchFilterAdapter<FullSearchFilterBean>(list, R.layout.item_search_filter_manufacturer) {

    val lastNames: MutableList<String> = mutableListOf()

    override fun getItemSpanSize(): Int = SEARCH_FILTER_SPAN_SIZE_MANUFACTURER

    override fun getAdapterType(): Int = SEARCH_FILTER_DATA_TYPE_MANUFACTURER

    override fun bindItemViewData(holder: <PERSON><PERSON><PERSON><PERSON><PERSON>older, fullSearchFilterBean: FullSearchFilterBean) {
        if (fullSearchFilterBean is SearchFilterContentBean<*> && fullSearchFilterBean.contentBean is ManufacturersBean) {
            val bean = fullSearchFilterBean.contentBean as ManufacturersBean
            val tv = holder.getView<TextView>(R.id.tv)
            tv.isActivated = lastNames.contains(bean.manufacturer)
            tv.text = bean.manufacturer
            tv.setOnClickListener { view ->
                val activated = view.isActivated
                if (activated) {
                    view.isActivated = false
                    if (lastNames != null) {
                        lastNames.remove(bean.manufacturer)
                    }
                } else {
                    view.isActivated = true
                    if (lastNames != null) {
                        lastNames.add(bean.manufacturer)
                    }
                    notifyDataSetChanged()
                }
            }
        }
    }
}