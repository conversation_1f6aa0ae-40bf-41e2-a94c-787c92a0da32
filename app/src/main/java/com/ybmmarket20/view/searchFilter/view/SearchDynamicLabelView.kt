package com.ybmmarket20.view.searchFilter.view

import android.annotation.SuppressLint
import android.content.Context
import android.text.TextUtils
import android.util.AttributeSet
import android.util.Log
import android.view.Gravity
import android.view.View
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.activity.ComponentActivity
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.isVisible
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.google.gson.reflect.TypeToken
import com.xyy.canary.utils.DensityUtil
import com.ybm.app.adapter.YBMBaseHolder
import com.ybm.app.utils.JsonUtils
import com.ybmmarket20.R
import com.ybmmarket20.adapter.YBMBaseListAdapter
import com.ybmmarket20.bean.SearchDynamicLabelConfig
import com.ybmmarket20.bean.SearchDynamicLabelItem
import com.ybmmarket20.bean.SearchFilterBean
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.search.DynamicLabelExtPopWindow
import com.ybmmarket20.view.BaseFilterPopWindow
import com.ybmmarket20.view.homesteady.whenAllNotNull
import com.ybmmarket20.viewmodel.SearchDataViewModel

/**
 * 搜索动态标签
 * 应用到1.新大搜（SearchProductOPActivity）筛选项下方 DYNAMIC_LABEL_STYLE_LINEAR
 *      2.新大搜（SearchProductOPActivity）底部筛选框 DYNAMIC_LABEL_STYLE_GRID
 */
//水平线性
const val DYNAMIC_LABEL_STYLE_HORIZONTAL_LINEAR = 1

//网格
const val DYNAMIC_LABEL_STYLE_GRID = 2


class SearchDynamicLabelView(context: Context, attrs: AttributeSet) :
    RecyclerView(context, attrs) {

    val mDataList = mutableListOf<SearchDynamicLabelConfig>()
    var mAdapter: SearchDynamicLabelAdapter? = null
    var mVm: SearchDataViewModel? = null
    private var mSelectCallback: ((Map<String, String>, List<SearchDynamicLabelConfig>) -> Unit)? =
        null

    //曝光回调
    private var mExposureCallback: ((item: SearchDynamicLabelConfig, position: Int, dlStyle: Int) -> Unit)? =
        null

    //曝光点混存
    private val mExposureCache = mutableSetOf<Int>()

    //动态标签样式
    private var mDLStyle = DYNAMIC_LABEL_STYLE_HORIZONTAL_LINEAR

    /**
     * 设置viewModel并监听动态标签数据变化
     */
    @SuppressLint("NotifyDataSetChanged")
    fun setData(vm: SearchDataViewModel, style: Int) {
        mDLStyle = style
        when (style) {
            DYNAMIC_LABEL_STYLE_HORIZONTAL_LINEAR -> setDynamicLabelStyleHorizontalLinear()
            DYNAMIC_LABEL_STYLE_GRID -> setDynamicLabelStyleGrid()
        }
        if (context !is ComponentActivity) return
        mVm = vm
        vm.dynamicLabelConfigLiveData.observe(context as ComponentActivity) {
            mDataList.clear()
            // 筛选弹框监听
            if(DYNAMIC_LABEL_STYLE_GRID == style){
                mDataList.addAll(it.filterNot { SearchDynamicLabelConfig.TYPE_POP == it.labelType })
            }else{
                mDataList.addAll(it)
            }
            mAdapter?.notifyDataSetChanged()
        }
        vm.dynamicLabelSelectedLiveData.observe(context as ComponentActivity) {
            mDataList.forEach { item ->
                // 多选
                if(SearchDynamicLabelConfig.TYPE_POP == item.labelType){
                    val json = it[item.paramKey]
                    // json为空：未选中
                    if(json.isNullOrEmpty()){
                        item.setSelectedStatus(false)
                        item.selectedItemNamesStr = ""
                    }else{
                        if(1 == item.isDynamicParam){
                            // 子数组判空设置状态
                            val extMap = JsonUtils.fromJson<HashMap<String,List<String>>>(json, object:TypeToken<HashMap<String,List<String>>>(){}.type)
                            val extList = extMap?.get(item.dynamicParamKey)
                            item.setSelectedStatus(!extList.isNullOrEmpty())
                            item.selectedItemNamesStr = extList?.joinToString("/")
                        }else{
                            // 产地：列表判空设置状态
                            val products = JsonUtils.fromJson<List<String>>(json)
                            item.setSelectedStatus(!products.isNullOrEmpty())
                            item.selectedItemNamesStr = products?.joinToString("/")
                        }
                    }
                }else{
                    item.setSelectedStatus(it[item.paramKey] == item.selectedParamValue)
                }
            }
            mAdapter?.notifyDataSetChanged()
        }
        //每次设置数据时清空曝光缓存
        mExposureCache.clear()
    }

    /**
     * 水平线性
     */
    private fun setDynamicLabelStyleHorizontalLinear() {
        layoutManager = LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
        mAdapter =
            SearchDynamicLabelAdapter(R.layout.item_dynamic_label_horizontal_linear, mDataList)
        adapter = mAdapter
    }

    /**
     * 网格
     */
    private fun setDynamicLabelStyleGrid() {
        layoutManager = GridLayoutManager(context, 4)
        mAdapter = SearchDynamicLabelAdapter(R.layout.item_dynamic_label_grid, mDataList)
        adapter = mAdapter
    }

    /**
     * 设置选择结果回调
     */
    fun setItemSelectCallback(selectCallback: ((Map<String, String>, List<SearchDynamicLabelConfig>) -> Unit)?) {
        mSelectCallback = selectCallback
    }

    /**
     * 设置曝光回调
     */
    fun setOnExposureCallback(callback: ((item: SearchDynamicLabelConfig, position: Int, dlStyle: Int) -> Unit)?) {
        mExposureCallback = callback
    }

    /**
     * 获取选择结果
     */
    private fun getSelectedResult(): Map<String, String> {
        val result = mutableMapOf<String, String>()
        // 扩展多选: 多个数组插入paramKey中
        val extMap = HashMap<String,List<String>>()
        var extKey = ""
        mDataList.forEach {
            if(SearchDynamicLabelConfig.TYPE_POP == it.labelType){
                if(1 == it.isDynamicParam){
                    extKey = it.paramKey?:""
                    // 单个多选属性
                    if(it.selectedItemNamesStr.isNullOrEmpty()){
                            extMap.remove(it.dynamicParamKey)
                    }else{
                        val data = it.selectedItemNamesStr!!.split("/")
                        extMap.put(it.dynamicParamKey?:"", data)
                    }
                }else{
                    // 产地多选
                    if(it.selectedItemNamesStr.isNullOrEmpty()){
                        result.remove(it.paramKey)
                    }else{
                        val data = it.selectedItemNamesStr!!.split("/")
                        result[it.paramKey ?: ""] = JsonUtils.toJson(data)
                    }
                    Log.d("dynamicLabel","产地: ${result[it.paramKey]}")
                }
            }else{
                result[it.paramKey ?: ""] = if (it.getSelectedStatus()) (it.selectedParamValue
                    ?: "") else (it.unselectedParamValue ?: "")
                if(it.getSelectedStatus()){
                    Log.d("dynamicLabel","动态标签: ${it.paramKey}:${result[it.paramKey]}")
                }
            }
        }
        // 扩展属性合并完后，存到跟map
        if(extMap.isNotEmpty()){
            result[extKey] = JsonUtils.toJson(extMap)
        }else{
            result.remove(extKey)
        }
        Log.d("dynamicLabel","ext: ${result[extKey]}")
        return result
    }

    private fun getSelectedResultConfig(): List<SearchDynamicLabelConfig> {
        return mDataList.filter {
            it.getSelectedStatus()
        }.toMutableList()
    }

    /**
     * 重置，所有item置为未选中状态
     */
    @SuppressLint("NotifyDataSetChanged")
    fun reset() {
        mDataList.forEach {
            it.setSelectedStatus(false)
        }
        mAdapter?.notifyDataSetChanged()
    }

    /**
     * 动态标签中是否包含选中项
     */
    fun isSelectedItem(): Boolean {
        return mDataList.any { it.getSelectedStatus() }
    }

    /**
     * 根据不同的layoutId展示不同样式
     */
    inner class SearchDynamicLabelAdapter(
        layoutId: Int,
        dataList: MutableList<SearchDynamicLabelConfig>
    ) : YBMBaseListAdapter<SearchDynamicLabelConfig>(layoutId, dataList) {

        @SuppressLint("NotifyDataSetChanged")
        override fun bindItemView(baseViewHolder: YBMBaseHolder?, t: SearchDynamicLabelConfig?) {
            whenAllNotNull(baseViewHolder, t) { holder, bean ->
                val tvDynamicLabel = holder.getView<TextView>(R.id.tvDynamicLabel)
                val con1 = holder.getView<ConstraintLayout>(R.id.con1)
                val labelBg = holder.getView<LinearLayout>(R.id.lyLabel)
                val ivDynamicLabel = holder.getView<ImageView>(R.id.ivDynamicLabel)
                val viewBottom = holder.getView<View>(R.id.view_bottom)
                tvDynamicLabel.text = bean.labelName
                tvDynamicLabel.gravity = Gravity.CENTER
                tvDynamicLabel.isActivated = bean.getSelectedStatus()
                if (con1 != null) {
                    con1.isActivated = bean.getSelectedStatus()
                }
                if (ivDynamicLabel != null) {
                    if (!TextUtils.isEmpty(bean.icon)) {
                        ivDynamicLabel.isVisible = true
                        Glide.with(mContext).load(AppNetConfig.getCDNHost() + bean.icon)
                            .into(ivDynamicLabel)
                    } else {
                        ivDynamicLabel.isVisible = false
                    }
                }
                //曝光
                if (!mExposureCache.contains(holder.layoutPosition)) {
                    mExposureCache.add(holder.layoutPosition)
                    mExposureCallback?.invoke(bean, holder.layoutPosition, mDLStyle)
                }
                tvDynamicLabel.setOnClickListener {
                    bean.setSelectedStatus(!bean.getSelectedStatus())
                    notifyItemChanged(holder.layoutPosition)
                    mSelectCallback?.invoke(getSelectedResult(), getSelectedResultConfig())
                    mAdapter?.notifyDataSetChanged()
                }
                if(labelBg!=null){
                    labelBg.isActivated = bean.getSelectedStatus()
                }
                if(SearchDynamicLabelConfig.TYPE_POP == bean.labelType){
                    if(bean.getSelectedStatus() && !bean.selectedItemNamesStr.isNullOrEmpty()){
                        tvDynamicLabel.text = bean.selectedItemNamesStr
                    }
                    tvDynamicLabel.setOnClickListener {
                        initDynamicLabelPopwindow(bean.isDynamicParam,bean.items?:listOf(),holder.layoutPosition,{ position,multiSelectStr->
                            // confirm
                            bean.selectedItemNamesStr= multiSelectStr
                            bean.setSelectedStatus(!TextUtils.isEmpty(multiSelectStr))
                            mSelectCallback?.invoke(getSelectedResult(), getSelectedResultConfig())
                        }){position->
                            // dismiss
                            bean.popShow = false
                            notifyItemChanged(position)
                        }
                        bean.popShow = true
                        notifyItemChanged(holder.layoutPosition)
                        dynamicLabelPopWindow?.show(viewBottom?:tvDynamicLabel)
                    }
                    if(bean.popShow){
                        viewBottom?.setBackgroundResource(R.color.color_f7f7f8)
                        setRightDrawable(tvDynamicLabel,R.drawable.icon_search_dynamic_arrow_up)
                    }else{
                        viewBottom?.setBackgroundResource(android.R.color.white)
                        setRightDrawable(tvDynamicLabel,R.drawable.icon_search_dynamic_arrow_down)
                    }
                }else{
                    viewBottom?.setBackgroundResource(android.R.color.white)
                    tvDynamicLabel.setCompoundDrawablesRelativeWithIntrinsicBounds(0,0,0,0)
                }
            }
        }
    }

    private fun setRightDrawable(textView: TextView, drawableRes: Int) {
        val drawable = resources.getDrawable(drawableRes)
        // 这一步必须要做,否则不会显示.
        drawable.setBounds(0, 0, DensityUtil.dip2px(context,7.5f), DensityUtil.dip2px(context,5f))
        textView.setCompoundDrawables(null, null, drawable, null)
    }

    //region 规格筛选项下拉框
    var dynamicLabelPopWindow: DynamicLabelExtPopWindow? = null

    private fun initDynamicLabelPopwindow(isDynamicParam:Int,subItem: List<SearchDynamicLabelItem>,position:Int, confirm:(position:Int,multiSelectStr:String)->Unit,dismiss:(position:Int)->Unit) {
        dynamicLabelPopWindow = DynamicLabelExtPopWindow(isDynamicParam,subItem)
        dynamicLabelPopWindow!!.confirmListener = { multiSelectStr->
            confirm.invoke(position, multiSelectStr)
        }
        dynamicLabelPopWindow!!.setOnSelectListener(object : BaseFilterPopWindow.OnSelectListener {
            override fun getValue(show: SearchFilterBean) {
                // 此选项为多选，因此在ondissmiss中直接获取最终选择结果
            }


            override fun OnDismiss(multiSelectStr: String) {
                dismiss.invoke(position)
            }
        })
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        dynamicLabelPopWindow?.dismiss()
    }
}