package com.ybmmarket20.view.searchFilter.adapter

import android.widget.TextView
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.bean.searchfilter.SEARCH_FILTER_COLLAPSE_CLOSE
import com.ybmmarket20.bean.searchfilter.FullSearchFilterBean
import com.ybmmarket20.bean.searchfilter.SearchFilterTitleBean
import com.ybmmarket20.common.util.Abase.getResources


/**
 * 搜索过滤条件，标题Adapter
 */
class SearchFilterTitleAdapter(list: MutableList<FullSearchFilterBean> = mutableListOf()) : AbsSearchFilterAdapter<FullSearchFilterBean>(list, R.layout.item_search_filter_title) {

    override fun getItemSpanSize(): Int = SEARCH_FILTER_SPAN_SIZE_TITLE

    override fun getAdapterType(): Int = SEARCH_FILTER_DATA_TYPE_TITLE

    override fun bindItemViewData(holder: YBMBaseHolder, bean: FullSearchFilterBean) {
        if (bean is SearchFilterTitleBean) {
            holder.setText(R.id.tvTitle, bean.title)
            val tvCollapse = holder.getView<TextView>(R.id.tvCollapse)
            if (bean.state == SEARCH_FILTER_COLLAPSE_CLOSE) {
                tvCollapse.text = "展开"
                setDrawableEnd(tvCollapse, R.drawable.icon_search_filter_title_arrow_down)
            } else {
                tvCollapse.text = "收起"
                setDrawableEnd(tvCollapse, R.drawable.icon_search_filter_title_arrow_up)
            }

            if (bean.filterList.isEmpty()) {
                ""
            } else {
                "已选${bean.filterList.joinToString(",")}"
            }.let {
                holder.setText(R.id.tvSelectedContent, it)
            }
            tvCollapse
        }
    }

    private fun setDrawableEnd(tv: TextView, drawableId: Int) {
        val drawable = getResources().getDrawable(drawableId)
        drawable.setBounds(0, 0, drawable.minimumWidth, drawable.minimumHeight)
        tv.setCompoundDrawables(null, null, drawable, null)
    }

}