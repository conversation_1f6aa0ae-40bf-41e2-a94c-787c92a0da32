package com.ybmmarket20.view.searchFilter.adapter

import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.ybm.app.adapter.YBMBaseAdapter
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.bean.searchfilter.FullSearchFilterBean
import com.ybmmarket20.view.homesteady.whenAllNotNull

/**
 * 搜索过滤条件Adapter基类
 */
abstract class AbsSearchFilterAdapter<T: FullSearchFilterBean>(data: MutableList<T>, layoutId: Int): YBMBaseAdapter<T>(layoutId, data) {

    override fun onAttachedToRecyclerView(recyclerView: RecyclerView) {
        super.onAttachedToRecyclerView(recyclerView)
        val layoutManager = recyclerView.layoutManager
        if (layoutManager is GridLayoutManager) {
            layoutManager.spanSizeLookup = object : GridLayoutManager.SpanSizeLookup() {
                override fun getSpanSize(position: Int): Int = getItemSpanSize()
            }
        }
    }

    abstract fun getItemSpanSize(): Int

    abstract fun getAdapterType(): Int

    abstract fun bindItemViewData(holder: YBMBaseHolder, bean: T)

    override fun bindItemView(baseViewHolder: YBMBaseHolder?, t: T?) {
        whenAllNotNull(baseViewHolder, t) {holder, bean ->
            bindItemViewData(holder, bean)
        }
    }

}