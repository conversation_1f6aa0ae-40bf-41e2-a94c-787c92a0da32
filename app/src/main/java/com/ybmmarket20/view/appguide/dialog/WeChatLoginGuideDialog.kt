package com.ybmmarket20.view.appguide.dialog

import android.app.AlertDialog
import android.content.Context
import android.view.View
import android.widget.ImageView
import com.ybmmarket20.R
import com.ybmmarket20.common.util.ConvertUtils

class WeChatLoginGuideDialog {

    private var dialog: AlertDialog? = null

    fun showLoading(context: Context) {
        val builder = AlertDialog.Builder(context, R.style.AlertDialog)
        builder.setCancelable(false)
        val view = View.inflate(context, R.layout.dialog_wechat_login_guide, null)
        val closeBtn = view.findViewById<ImageView>(R.id.ivWechatLoginGuideClose)
        val confirmBtn = view.findViewById<ImageView>(R.id.ivWechatLoginGuideConfirm)
        closeBtn.setOnClickListener { dialog?.dismiss() }
        confirmBtn.setOnClickListener { dialog?.dismiss() }
        dialog = builder.create()
        dialog?.show()
        dialog?.window?.setContentView(view)
        dialog?.setCanceledOnTouchOutside(false)
        val params = dialog?.window!!.attributes
        params.width = ConvertUtils.dp2px(296f)
        params.height = ConvertUtils.dp2px(267f)
        dialog?.window!!.attributes = params
    }

    fun dismissLoading() {
        dialog?.dismiss()
    }
}

