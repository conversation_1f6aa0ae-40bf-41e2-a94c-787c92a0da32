package com.ybmmarket20.view.operationposition.content

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.text.TextUtils
import android.util.AttributeSet
import android.view.View
import androidx.constraintlayout.widget.ConstraintLayout
import com.google.gson.Gson
import com.ybmmarket20.R
import com.ybmmarket20.activity.ProductDetailActivity
import com.ybmmarket20.bean.ProductEditLayoutSuccessParams
import com.ybmmarket20.bean.RowsBean
import com.ybmmarket20.bean.product_detail.ReportPDExtendOuterBean
import com.ybmmarket20.common.JGTrackManager
import com.ybmmarket20.common.JgTrackBean
import com.ybmmarket20.common.RequestParams
import com.ybmmarket20.common.addToCartJGTrack
import com.ybmmarket20.common.getFullClassName
import com.ybmmarket20.common.splicingModule2Entrance
import com.ybmmarket20.constant.IntentCanst
import com.ybmmarket20.reportBean.JGPageListCommonBean
import com.ybmmarket20.utils.AuditStatusSyncUtil
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.utils.analysis.AnalysisConst
import com.ybmmarket20.utils.analysis.BaseFlowData
import com.ybmmarket20.utils.analysis.addAnalysisRequestParams
import com.ybmmarket20.utils.analysis.flowDataPageCommodityDetails
import com.ybmmarket20.view.ProductEditLayoutNew
import com.ybmmarket20.view.operationposition.content.goodsState.AuditGoodsViewState
import com.ybmmarket20.view.operationposition.content.goodsState.ControlledGoodsPriceViewState
import com.ybmmarket20.view.operationposition.content.goodsState.NormalGoodsViewState
import com.ybmmarket20.view.operationposition.content.goodsState.OPERATION_POSITION_GOODS_BTN_CLICK_TYPE_CARD
import com.ybmmarket20.view.operationposition.content.goodsState.PGBYGoodsViewState
import com.ybmmarket20.view.operationposition.content.goodsState.SecKillGoodsViewState
import com.ybmmarket20.view.operationposition.content.goodsState.SpellGroupGoodsState
import com.ybmmarket20.view.operationposition.track.OPTrackGoodsItem
import com.ybmmarket20.view.operationposition.track.OPTrackManager
import com.ybmmarketkotlin.utils.RouterJump

/**
 * 运营位商品
 */
class OPGoodsView(context: Context, attrs: AttributeSet?) :
    ConstraintLayout(context, attrs) {

    init {
        View.inflate(context, R.layout.item_search_operation_position_goods, this)
    }

    var productClickListener: ((rowsBean:RowsBean,productPosition:Int,isBtnClick:Boolean,btnContent:String,number:Int?)->Unit)? = null
    fun setData(rowsBean: RowsBean, position: Int, parentPosition: Int, isShowShop: Boolean, flowData: BaseFlowData?,mJgTrackBean:JgTrackBean?,jGPageListCommonBean: JGPageListCommonBean? = null) {
        when {
            //资质审核未通过
            !AuditStatusSyncUtil.getInstance().isAuditFirstPassed -> AuditGoodsViewState(this)
            //控销
            !TextUtils.isEmpty(rowsBean.controlTitle) -> ControlledGoodsPriceViewState(this)
            //拼团品(进行中和未开始)
            rowsBean.actPt?.assembleStatus == 1 || rowsBean.actPt?.assembleStatus == 0 -> SpellGroupGoodsState(this)
            //批购包邮
            rowsBean.actPgby != null -> PGBYGoodsViewState(this)
            //秒杀
            rowsBean.actSk != null && rowsBean.showPriceType() == 0 -> SecKillGoodsViewState(this)
            //平销品
            else -> NormalGoodsViewState(this)
        }.apply {
//            jgTrackBean = JgTrackBean(
//                    pageId = JGTrackManager.TrackSearchResult.PAGE_ID,
//                    title = JGTrackManager.TrackSearchResult.TITLE,
//                    module = JGTrackManager.TrackSearchResult.MODULE_OPERATIONS,
//                    entrance = splicingModule2Entrance("首页(搜索框)-${JGTrackManager.TrackSearchResult.TITLE}",JGTrackManager.TrackSearchResult.MODULE_OPERATIONS_LIST),
//            )

            jgTrackBean = mJgTrackBean
            this.jgPageListCommonBean = jGPageListCommonBean

            productClickListener = {isBtnClick,btnContent,number->
                <EMAIL>?.invoke(rowsBean, position,isBtnClick,btnContent,number)
            }

            handleData(rowsBean, parentPosition, isShowShop, flowData,position)
            handleSellOut(rowsBean)
            handleOffShelf(rowsBean)
            //商品点击
            setOnClickListener {
                OPTrackManager.opGoodsBtnClickTrack(OPTrackGoodsItem(rowsBean, parentPosition, flowData, OPERATION_POSITION_GOODS_BTN_CLICK_TYPE_CARD,position))
                <EMAIL>?.invoke(rowsBean, position,false,"",null)
                var url = "ybmpage://productdetail?${IntentCanst.PRODUCTID}=${rowsBean.id}&index=${position}&search_sort_strategy_id=${rowsBean.searchSortStrategyCode}"

                val mParams = Bundle().apply {
                    putString(IntentCanst.PRODUCTID,rowsBean.id.toString())
                    putString("search_sort_strategy_id",rowsBean.searchSortStrategyCode)
                    putInt("index",position)
                    putString(IntentCanst.JG_REFERRER,JGTrackManager.TrackSearchResult.TRACK_URL)
                    putString(IntentCanst.JG_REFERRER_TITLE,JGTrackManager.TrackSearchResult.TITLE)
                    putString(IntentCanst.JG_REFERRER_MODULE,JGTrackManager.Common.MODULE_OPERATIONS)
                    putString(IntentCanst.JG_ENTRANCE,jgTrackBean?.entrance?:"")
                    putSerializable(IntentCanst.JG_JSON_REPORT_PD_EXTEND_OUTER_BEAN,
                            ReportPDExtendOuterBean().apply {
                                sptype = jGPageListCommonBean?.sptype?:""
                                jgspid = jGPageListCommonBean?.jgspid?:""
                                sid = jGPageListCommonBean?.sid?:""
                                resultCnt = jGPageListCommonBean?.result_cnt?:0
                                pageNo = jGPageListCommonBean?.page_no?:0
                                pageSize = jGPageListCommonBean?.page_size?:0
                                totalPage = jGPageListCommonBean?.total_page?:0
                                rank = parentPosition+1
                                keyWord = rowsBean.searchKeyword?:""
                                searchSortStrategyId = rowsBean.searchSortStrategyCode?:""
                                operationId = rowsBean.operationId ?: ""
                                operationRank = position+1
                                listPositionType = rowsBean.positionType.toString()
                                listPositionTypename = rowsBean.positionTypeName?:""
                            }
                    )
                }

                //这里带参太多了 只能Intent跳了 不能用路由
                val intent = Intent(context, ProductDetailActivity::class.java)
                intent.putExtras(mParams)
                context.startActivity(intent)
//                RouterJump.jump2ProductDetail(url,mParams)
            }
        }
    }

}