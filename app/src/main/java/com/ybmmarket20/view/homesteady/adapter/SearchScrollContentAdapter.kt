package com.ybmmarket20.view.homesteady.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.RecyclerView
import com.ybmmarket20.R
import com.ybmmarket20.databinding.ItemSearchScrollContentBinding

class SearchScrollContentAdapter : RecyclerView.Adapter<SearchScrollContentAdapter.SearchScrollContentAdapterVH>() {

    var dataList = arrayListOf<String>()
        set(value) {
            field.clear()
            field.addAll(value)
            notifyDataSetChanged()
        }

    lateinit var mContext: Context

    var onItemClickListener: ((content: String) -> Unit)? = null

    companion object{
        private const val MAX_SCROLL_COUNT = 999999   //最大滚动次数
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): SearchScrollContentAdapterVH {
        mContext = parent.context
        return SearchScrollContentAdapterVH(
                DataBindingUtil.inflate(LayoutInflater.from(mContext), R.layout.item_search_scroll_content, parent, false)
        )
    }

    override fun getItemCount(): Int = MAX_SCROLL_COUNT

    override fun onBindViewHolder(holder: SearchScrollContentAdapterVH, position: Int) {
        holder.mBinding.apply {
            tvContent.text = dataList[position % dataList.size]

            root.setOnClickListener {
                onItemClickListener?.invoke(dataList[position % dataList.size])
            }
        }
    }

    class SearchScrollContentAdapterVH(val mBinding: ItemSearchScrollContentBinding) : RecyclerView.ViewHolder(mBinding.root)
}