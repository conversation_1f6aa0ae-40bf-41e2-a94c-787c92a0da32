package com.ybmmarket20.view.homesteady.callback


/**
 * 精选店铺埋点回调
 */
interface IShopAnalysisCallback {

    /**
     * 精选店铺顶栏更多按钮点击
     */
    fun onHomeSteadyAnalysisShopTopClick(action: String?, text: String?)

    /**
     * 精选店铺点击店铺
     */
    fun onHomeSteadyAnalysisShopClick(action: String?, offset: String?, text: String?, sku_id: String?)

    /**
     * 精选店铺店铺曝光
     */
    fun onHomeSteadyAnalysisShopGoodsExposure(action: String?, offset: String?, text: String?, sku_id: String?)
}