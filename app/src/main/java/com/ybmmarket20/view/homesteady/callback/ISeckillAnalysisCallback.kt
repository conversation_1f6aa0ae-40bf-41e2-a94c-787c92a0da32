package com.ybmmarket20.view.homesteady.callback

/**
 * 定时活动埋点回调
 */
interface ISeckillAnalysisCallback {

    /**
     * 定时活动顶栏点击
     */
    fun onHomeSteadyAnalysisSeckillTopClick(action: String?, text: String?)

    /**
     * 定时活动更多点击
     */
    fun onHomeSteadyAnalysisSeckillMoreClick(action: String?, text: String?)

    /**
     * 定时活动商品点击
     */
    fun onHomeSteadyAnalysisSeckillGoodsClick(action: String?, offset: Int, text: String?, id: String?, actionUrl: String?)

    /**
     * 定时活动商品曝光
     */
    fun onHomeSteadyAnalysisSeckillGoodsExposure(action: String?, offset: Int, text: String?, id: String?)
}