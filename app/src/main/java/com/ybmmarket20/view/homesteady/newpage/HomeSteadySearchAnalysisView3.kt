package com.ybmmarket20.view.homesteady.newpage

import android.content.Context
import android.util.AttributeSet
import androidx.constraintlayout.widget.ConstraintLayout
import com.ybmmarket20.xyyreport.SpmLogUtil
import com.ybmmarket20.xyyreport.page.home.HomeReportEvent
import com.ybmmarket20.xyyreport.session.SessionManager
import com.ybmmarket20.xyyreport.spm.IPageParams
import com.ybmmarket20.xyyreport.spm.ScmBean
import com.ybmmarket20.xyyreport.spm.SpmUtil
import com.ybmmarket20.xyyreport.spm.TrackData
import com.ybmmarket20.xyyreport.spm.XyyReportActivity

abstract class HomeSteadySearchAnalysisView3(val mContext: Context, attr: AttributeSet) : ConstraintLayout(mContext, attr) {

    var trackData: TrackData? = null

    /**
     * 点击放大镜图标
     */
    fun trackSearchIconClick() {
        val newSpm = trackData?.spmEntity?.apply { spmD = "searchBox@1" }
        SpmLogUtil.print("首页搜索组件-点击放大镜图标")
        HomeReportEvent.trackHomeSubComponentClick(mContext, newSpm, getScm())
    }

    /**
     * 点击扫一扫
     */
    fun trackSearchScanClick() {
        val newSpm = trackData?.spmEntity?.apply { spmD = "searchBox@3" }
        SpmLogUtil.print("首页搜索组件-点击扫一扫图标")
        HomeReportEvent.trackHomeSubComponentClick(mContext, newSpm, getScm())
    }

    /**
     * 点击语音
     */
    fun trackSearchVoiceClick() {
        val newSpm = trackData?.spmEntity?.apply { spmD = "searchBox@4" }
        SpmLogUtil.print("首页搜索组件-点击语音图标")
        HomeReportEvent.trackHomeSubComponentClick(mContext, newSpm, getScm())
    }

    /**
     * 点击消息
     */
    fun trackMessageClick() {
        val newSpm = trackData?.spmEntity?.newInstance()?.apply { spmD = "message@1" }
        SpmLogUtil.print("首页搜索组件-点击消息图标")
        HomeReportEvent.trackHomeSubComponentClick(mContext, newSpm, getScm())
    }

    /**
     * 搜索框热词点击
     */
    fun trackSearchViewClick(context: Context, trackData: TrackData?, position: Int, hotWord: String?, componentTrackData: TrackData?) {
        if (context !is IPageParams) return
        val pageSpm = (context as IPageParams).getSpmCtn()
        val newSpm = trackData?.spmEntity?.newInstance()?.apply {
            spmB = pageSpm?.spmB
            spmC = componentTrackData?.spmEntity?.spmC
            spmD = "searchBox@2_hotword@${position + 1}"
        }
        val newScm = trackData?.scmEntity?.newInstance()
        SpmLogUtil.print("首页-组件-搜索框热词点击")
        HomeReportEvent.trackHomeSubComponentClick(mContext, newSpm, newScm)
    }

    private fun getScm(): ScmBean {
        val scmC = if (mContext is XyyReportActivity) {
            mContext.getScmCnt()?.scmC
        } else "0_0"
        return ScmBean("cms", "0", scmC, "0", SessionManager.get().generateScmE())
    }
}