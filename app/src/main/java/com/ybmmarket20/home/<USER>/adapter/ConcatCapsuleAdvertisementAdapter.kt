package com.ybmmarket20.home.newpage.adapter

import android.content.Context
import android.graphics.Color
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.StaggeredGridLayoutManager
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.ybmmarket20.R
import com.ybmmarket20.databinding.ItemCapsuleAdvertisementBinding
import com.ybmmarket20.home.newpage.bean.Capsule
import com.ybmmarket20.home.newpage.bean.CapsuleAdvertisementItemBean
import com.ybmmarket20.xyyreport.SpmLogUtil

/**
 * @class   CapsuleAdvertisementAdapter
 * <AUTHOR>
 * @date  2024/4/10
 * @description  首页胶囊广告位（目前需求 一个图， 或 左右2个图）
 */
class ConcatCapsuleAdvertisementAdapter : HomeComponentAnalysisAdapter<ConcatCapsuleAdvertisementAdapter.CapsuleAdvertisementVH>() {

    var mCapsuleData: Capsule? = null
        set(value) {
            field = value
            notifyDataSetChanged()
        }

    var navigation = ""
    var homeJgspid: String? = null

    /**
     *
     * model: 埋点用  模式 1：1行1个  2：1行2个
     * offset：1:左  2：右
     */
    var mOnItemClickListener: ((Capsule?, CapsuleAdvertisementItemBean, model: Int, offset: Int) -> Unit)? = null
    private lateinit var mContext: Context

    //      value:当时埋点的时间戳
    private val resourceViewTrackMap = hashMapOf<String, Long>()

    companion object {
        private const val MODEL_ONE = 1
        private const val MODEL_TWO = 2
        private const val OFFSET_LEFT = 1
        private const val OFFSET_RIGHT = 2

        private const val TRACK_DURATION = 2 * 60 * 1000 //2分钟内不上报
        private const val TRACK_RESOURCE_VIEW_KEY = "track_resource_view_key"
    }

    override fun onCreateViewHolder(
            parent: ViewGroup,
            viewType: Int
    ): CapsuleAdvertisementVH {
        mContext = parent.context
        return CapsuleAdvertisementVH(DataBindingUtil.inflate(LayoutInflater.from(mContext), R.layout.item_capsule_advertisement, parent, false))
    }

    override fun getItemCount(): Int = mCapsuleData?.capsuleContent?.capsuleAdvertisementList?.let { if (it.size > 0) 1 else 0 }
            ?: 0

    override fun onBindViewHolder(
            holder: CapsuleAdvertisementVH,
            position: Int
    ) {

        val data = mCapsuleData?.capsuleContent ?: return

        onComponentExposure(mContext, mCapsuleData?.trackData, position) {
            SpmLogUtil.print("首页-组件-胶囊位曝光")
        }
        holder.mBinding.apply {
            val mList = data.capsuleAdvertisementList ?: arrayListOf()
            when (mList.size) {
                0->{}

                1 -> {
                    ivOnlyOne.isVisible = true
                    groupTwoPic.isVisible = false

                    val mData = mList[0]
                    Glide.with(mContext)
                            .load(mData.materialImg?:"").centerCrop().placeholder(R.drawable.jiazaitu_min).error(R.drawable.jiazaitu_min).dontAnimate().diskCacheStrategy(DiskCacheStrategy.SOURCE).into(ivOnlyOne)

                    ivOnlyOne.setOnClickListener {
                        mOnItemClickListener?.invoke(mCapsuleData, mData, MODEL_ONE, OFFSET_LEFT)
                        onSubcomponentClick(mContext, mData.trackData)
                    }
                }

                else ->{
                    ivOnlyOne.isVisible = false
                    groupTwoPic.isVisible = true

                    val mData = mList[0]
                    val mData2 = mList[1]
                    Glide.with(mContext)
                            .load(mData.materialImg?:"")
                            .centerCrop()
                            .placeholder(R.drawable.jiazaitu_min)
                            .error(R.drawable.jiazaitu_min)
                            .dontAnimate()
                            .diskCacheStrategy(DiskCacheStrategy.SOURCE)
                            .into(ivTwoLeft)

                    Glide.with(mContext)
                            .load(mData2.materialImg?:"")
                            .centerCrop()
                            .placeholder(R.drawable.jiazaitu_min)
                            .error(R.drawable.jiazaitu_min)
                            .dontAnimate()
                            .diskCacheStrategy(DiskCacheStrategy.SOURCE)
                            .into(ivTwoRight)

                    if (mData.isColor()){
                        mData.classBackground?.let {
                            try {
                                ivTwoLeftBg.setBackgroundColor(Color.parseColor(it))
                            } catch (e: Exception) {
                                e.printStackTrace()
                                ivTwoLeftBg.setBackgroundColor(Color.TRANSPARENT)
                            }
                        }?: kotlin.run {
                            ivTwoLeftBg.setBackgroundColor(Color.TRANSPARENT)
                        }

                        mData.classBackgroundTransparency?.let {
                            var mAlpha = 1f
                            try {
                                mAlpha = it.toFloat()/100
                            }catch (e:Exception){
                            }
                            ivTwoLeftBg.alpha = mAlpha
                        }
                    }else{
                        ivTwoLeftBg.setBackgroundColor(Color.TRANSPARENT)
                    }

                    if (mData2.isColor()){
                        mData2.classBackground?.let {
                            try {
                                ivTwoRightBg.setBackgroundColor(Color.parseColor(it))
                            } catch (e: Exception) {
                                e.printStackTrace()
                                ivTwoRightBg.setBackgroundColor(Color.TRANSPARENT)
                            }
                        }?: kotlin.run {
                            ivTwoRightBg.setBackgroundColor(Color.TRANSPARENT)
                        }

                        mData2.classBackgroundTransparency?.let {
                            var mAlpha = 1f
                            try {
                                mAlpha = it.toFloat()/100
                            }catch (e:Exception){
                            }
                            ivTwoRightBg.alpha = mAlpha
                        }
                    }else{
                        ivTwoRightBg.setBackgroundColor(Color.TRANSPARENT)
                    }

                    ivTwoLeft.setOnClickListener {
                        mOnItemClickListener?.invoke(mCapsuleData, mData, MODEL_TWO, OFFSET_LEFT)
                        onSubcomponentClick(mContext, mData.trackData)
                    }

                    ivTwoRight.setOnClickListener {
                        mOnItemClickListener?.invoke(mCapsuleData, mData2, MODEL_TWO, OFFSET_RIGHT)
                        SpmLogUtil.print("首页-组件-胶囊位点击")
                        onSubcomponentClick(mContext, mData.trackData)
                    }
                }
            }

            resourceViewTrackMap[TRACK_RESOURCE_VIEW_KEY]?.let {
                if (System.currentTimeMillis() - it > TRACK_DURATION) {
                    resourceViewTrackMap[TRACK_RESOURCE_VIEW_KEY] = System.currentTimeMillis()
                }
            } ?: kotlin.run {
                resourceViewTrackMap[TRACK_RESOURCE_VIEW_KEY] = System.currentTimeMillis()
            }

        }
    }

    override fun onViewAttachedToWindow(holder: CapsuleAdvertisementVH) {
        super.onViewAttachedToWindow(holder)
        val lp: ViewGroup.LayoutParams = holder.itemView.layoutParams
        if (lp is StaggeredGridLayoutManager.LayoutParams) {
            lp.isFullSpan = true
        }
    }

    class CapsuleAdvertisementVH(val mBinding: ItemCapsuleAdvertisementBinding) : RecyclerView.ViewHolder(mBinding.root)
}