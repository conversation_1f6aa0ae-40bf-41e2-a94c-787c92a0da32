package com.ybmmarket20.home.newpage.adapter

import android.content.Context
import android.graphics.Color
import android.graphics.Rect
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.ybmmarket20.R
import com.ybmmarket20.bean.RowsBean
import com.ybmmarket20.common.dp
import com.ybmmarket20.common.getColorById
import com.ybmmarket20.common.glideLoadWithPlaceHolder
import com.ybmmarket20.databinding.ItemPorcelainTilesBinding
import com.ybmmarket20.home.newpage.HomeTabCommonFragment.Companion.WHERE_CLICK_BLANK
import com.ybmmarket20.home.newpage.HomeTabCommonFragment.Companion.WHERE_CLICK_MAIN_TITLE
import com.ybmmarket20.home.newpage.HomeTabCommonFragment.Companion.WHERE_CLICK_SUBTITLE_TITLE
import com.ybmmarket20.home.newpage.bean.RecommendCommodity
import com.ybmmarket20.home.newpage.bean.RecommendCommodityItemBean

/**
 * @class   PorcelainTilesAdapter
 * <AUTHOR>
 * @date  2024/4/12
 * @description   瓷片Adapter
 */
class PorcelainTilesAdapter : RecyclerView.Adapter<PorcelainTilesAdapter.PorcelainTilesVH>() {

    var mDataList = ArrayList<RecommendCommodityItemBean>()
        set(value) {
            field.clear()
            field.addAll(value)
            notifyDataSetChanged()
        }
    var mSuperData: RecommendCommodity? = null
    /**
     * 埋点用：
     * type:  1为主标题，2为副标题
     * offset:  1为左楼层，2为右楼层
     * content:标题内容
     */
    var mOnItemClickListener: ((recommendCommodity: RecommendCommodity?, itemBean: RecommendCommodityItemBean, type: Int, offset: Int, content: String, where:Int) -> Unit)? = null
    var mOnProductItemClickListener: ((recommendCommodity: RecommendCommodity?, itemBean: RecommendCommodityItemBean,RowsBean,type: Int,offset:Int) -> Unit)? = null

    private lateinit var mContext: Context

    companion object {
        private const val SPAN_COUNT = 2
        private const val TRACK_TYPE_MAIN = 1
        private const val TRACK_TYPE_DEPUTY = 2
        private const val TRACK_OFFSET_LEFT = 1
        private const val TRACK_OFFSET_RIGHT = 2
    }

    override fun onCreateViewHolder(
            parent: ViewGroup,
            viewType: Int
    ): PorcelainTilesVH {
        mContext = parent.context
        return PorcelainTilesVH(DataBindingUtil.inflate(LayoutInflater.from(mContext),
                R.layout.item_porcelain_tiles, parent, false))
    }

    override fun getItemCount(): Int = if (mDataList.size > 1) 2 else 0  //要么显示两个  要么一个都不显示

    override fun onBindViewHolder(holder: PorcelainTilesVH, position: Int) {

        holder.mBinding.apply {

            val mData = mDataList[position]

            rvPorcelainTiles.layoutManager = GridLayoutManager(mContext, SPAN_COUNT)
            rvPorcelainTiles.adapter = PorcelainTilesProductAdapter().apply {
                this.mOnItemClickListener = { bean,type ->

                    <EMAIL>?.invoke(mSuperData, mData,bean,type,position+1)
                }

                this.mDataList = (mData.capsuleCommonProductList?: arrayListOf()) as ArrayList<RowsBean>
            }

            Glide.with(mContext).load(mData.floorIcon).into(ivTitleTag)

            if (mData.isMainTypeImg()){
                tvTitle.text = ""
                ivImgTitle.isVisible = true

                mContext.glideLoadWithPlaceHolder(mData.mainTitle?:"",ivImgTitle,R.drawable.transparent,R.drawable.transparent,true)
            }else{
                ivImgTitle.isVisible = false

                tvTitle.text = mData.mainTitleText?.let {
                    //5个字显示完全 大于5个字显示4个字+...
                    if (it.length > 5) it.substring(0,4)+"..." else it
                }?:""
            }

            mData.mainTitleColor?.let {
                try {
                    tvTitle.setTextColor(Color.parseColor(it))
                }catch (e:Exception){
                    tvTitle.setTextColor(mContext.getColorById(R.color.color_292933))
                }
            }?: kotlin.run {
                tvTitle.setTextColor(mContext.getColorById(R.color.color_292933))
            }

            mData.mainTitleColorTransparency?.let {
                var alpha = 1f
                try {
                    alpha = it.toFloat()/100
                }catch (e:Exception){
                }
                tvTitle.alpha = alpha
            }?: kotlin.run {
                tvTitle.alpha = 1f
            }

            if (mData.isSubTypeImg()){
                tvDescribe.text = ""
                ivImgDescribe.isVisible = true

                mContext.glideLoadWithPlaceHolder(mData.subTitle?:"",ivImgDescribe,R.drawable.transparent,R.drawable.transparent,true)
            }else{
                ivImgDescribe.isVisible = false
                tvDescribe.text = mData.subTitleText?.let{
                    //4个字显示完全 大于4个字显示3个字+...
                    if (it.length > 4) it.substring(0,3)+"..." else it
                }?:""
            }

            mData.subTitleColor?.let {
                try {
                    tvDescribe.setTextColor(Color.parseColor(it))
                }catch (e:Exception){
                    tvDescribe.setTextColor(mContext.getColorById(R.color.color_9A9A9A))
                }
            }?: kotlin.run {
                tvDescribe.setTextColor(mContext.getColorById(R.color.color_9A9A9A))
            }
            mData.subTitleColorTransparency?.let {
                var alpha = 1f
                try {
                    alpha = it.toFloat() / 100
                } catch (e: Exception) {
                }
                tvDescribe.alpha = alpha
            }?:run {
                tvDescribe.alpha = 1f
            }

            mData.bgImg?.let{
                Glide.with(mContext).load(it).centerCrop().error(R.color.white).into(ivBg)
            }?: kotlin.run {
                mData.bgRes?.let {
                    try {
                        ivBg.setBackgroundColor(Color.parseColor(it))
                    } catch (e: Exception) {
                        e.printStackTrace()
                        ivBg.setBackgroundColor(mContext.getColorById(R.color.white))
                    }
                }?:run {
                    ivBg.setBackgroundColor(mContext.getColorById(R.color.white))

                }
            }

            tvTitle.setOnClickListener {
                mOnItemClickListener?.invoke(mSuperData, mData,TRACK_TYPE_MAIN,if (position == 0) TRACK_OFFSET_LEFT else TRACK_OFFSET_RIGHT,mData.mainTitleText?:"",WHERE_CLICK_MAIN_TITLE)
            }

            ivImgTitle.setOnClickListener {
                mOnItemClickListener?.invoke(mSuperData, mData,TRACK_TYPE_MAIN,if (position == 0) TRACK_OFFSET_LEFT else TRACK_OFFSET_RIGHT,mData.mainTitleText?:"",WHERE_CLICK_MAIN_TITLE)
            }

            clDescribe.setOnClickListener {
                mOnItemClickListener?.invoke(mSuperData, mData, TRACK_TYPE_DEPUTY,if (position == 0) TRACK_OFFSET_LEFT else TRACK_OFFSET_RIGHT,mData.subTitleText?:"", WHERE_CLICK_SUBTITLE_TITLE)
            }

            ivImgDescribe.setOnClickListener {
                mOnItemClickListener?.invoke(mSuperData, mData, TRACK_TYPE_DEPUTY,if (position == 0) TRACK_OFFSET_LEFT else TRACK_OFFSET_RIGHT,mData.subTitleText?:"", WHERE_CLICK_SUBTITLE_TITLE)
            }


            root.setOnClickListener {
                mOnItemClickListener?.invoke(mSuperData, mData, 0,0,"", WHERE_CLICK_BLANK)
            }
        }
    }

    class PorcelainTilesVH(val mBinding: ItemPorcelainTilesBinding) : RecyclerView.ViewHolder(mBinding.root)

    class PorcelainTilesItemDecoration : RecyclerView.ItemDecoration() {

        override fun getItemOffsets(
                outRect: Rect,
                view: View,
                parent: RecyclerView,
                state: RecyclerView.State
        ) {
            super.getItemOffsets(outRect, view, parent, state)

            val position = parent.getChildLayoutPosition(view)

            val marginLeft = if (position % 2 != 0) (3.5f).dp else 7.dp
            val marginTop = 0.dp
            val marginBottom = 0.dp
            val marginRight = if (position % 2 != 0) 7.dp else (3.5f).dp

            outRect.set(marginLeft, marginTop, marginRight, marginBottom)
        }
    }

}