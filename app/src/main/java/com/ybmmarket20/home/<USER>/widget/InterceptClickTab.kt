package com.ybmmarket20.home.newpage.widget

import com.google.android.material.tabs.TabLayout
import com.ybmmarket20.home.newpage.bean.TabBean

/**
 * @class   InterceptClickTab
 * <AUTHOR>
 * @date  2024/5/19
 * @description
 */

class InterceptClickTab : TabLayout.Tab() {

	override fun select() {
		val mTag = tag
		if (mTag is TabBean) {
			if (mTag.isH5Url()) { //链接的话 就不让去选中了 直接跳转
				//跳转的话在TabView的点击事件设置就行 就不会走OnSelect监听了
				return
			}
		}

		super.select()
	}
}

