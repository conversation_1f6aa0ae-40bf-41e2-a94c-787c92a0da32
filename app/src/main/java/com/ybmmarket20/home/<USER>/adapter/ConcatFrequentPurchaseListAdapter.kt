package com.ybmmarket20.home.newpage.adapter

import android.content.Context
import android.graphics.Color
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.StaggeredGridLayoutManager
import com.bumptech.glide.Glide
import com.ybmmarket20.R
import com.ybmmarket20.bean.RowsBean
import com.ybmmarket20.common.dp
import com.ybmmarket20.common.getColorById
import com.ybmmarket20.common.glideLoadWithPlaceHolder
import com.ybmmarket20.databinding.ItemPorcelainTilesFrequentBinding
import com.ybmmarket20.home.newpage.HomeTabCommonFragment.Companion.WHERE_CLICK_BLANK
import com.ybmmarket20.home.newpage.HomeTabCommonFragment.Companion.WHERE_CLICK_MAIN_TITLE
import com.ybmmarket20.home.newpage.HomeTabCommonFragment.Companion.WHERE_CLICK_SUBTITLE_TITLE
import com.ybmmarket20.home.newpage.bean.FrequentPurchase
import com.ybmmarket20.xyyreport.spm.TrackData

/**
 * @class   ConcatFrequentPurchaseListAdapter
 * <AUTHOR>
 * @date  2024/4/12
 * @description   常购清单
 */
class ConcatFrequentPurchaseListAdapter : HomeComponentAnalysisAdapter<ConcatFrequentPurchaseListAdapter.ConcatFrequentPurchaseVH>() {

    var mData: FrequentPurchase? = null
        set(value) {
            field = value
            notifyDataSetChanged()
        }

    var mOnProductItemClickListener: ((itemBean: FrequentPurchase, RowsBean, offset:Int) -> Unit)? = null
    var mOnItemClickListener: ((itemBean: FrequentPurchase, type: Int, content: String,where:Int) -> Unit)? = null

    private lateinit var mContext: Context
    //      value:当时埋点的时间戳
    private val resourceViewTrackMap = hashMapOf<String, Long>()
    var mHomeJgspid: String? = null
    var navigation = ""
    companion object {
        private const val SPAN_COUNT = 4
        private const val TRACK_TYPE_MAIN = 1
        private const val TRACK_TYPE_DEPUTY = 2
        private const val TRACK_DURATION = 2 * 60 * 1000 //2分钟内不上报
        private const val TRACK_RESOURCE_VIEW_KEY = "track_resource_view_key"
    }

    override fun onCreateViewHolder(
            parent: ViewGroup,
            viewType: Int
    ): ConcatFrequentPurchaseVH {
        mContext = parent.context
        return ConcatFrequentPurchaseVH(DataBindingUtil.inflate(LayoutInflater.from(mContext), R.layout.item_porcelain_tiles_frequent, parent, false))
    }

    override fun getItemCount(): Int = mData?.frequentPurchaseContent?.frequentPurchaseFloorList?.let { if (it.size > 0) 1 else 0 }
            ?: 0

    override fun onBindViewHolder(
            holder: ConcatFrequentPurchaseVH,
            position: Int
    ) {
        //该组件暂时不要qt埋点
//        onComponentExposure(mContext, mData?.trackData, position)
        holder.mBinding.apply {
            val data = mData?.frequentPurchaseContent?.frequentPurchaseFloorList?.let { if (it.size > 0) it[0] else return }
                    ?: return

            //因为这个布局是共用的 不能直接在xml里面改 就在这改
            root.layoutParams = (root.layoutParams as StaggeredGridLayoutManager.LayoutParams).apply {
                setMargins(7.dp, 7.dp, 7.dp, 0)
            }

            rvPorcelainTiles.layoutManager = GridLayoutManager(mContext, SPAN_COUNT)
            rvPorcelainTiles.adapter = PorcelainTilesProductAdapter().apply {
                this.mOnItemClickListener = { bean,type ->

                     mData?.let {
                         //商品的里面的下标就是这里需要的offset
                         <EMAIL>?.invoke(it,bean,type)
                         trackItemClick(data.trackData)
                     }
                }

                this.mDataList = (data.capsuleCommonProductList?: arrayListOf()) as ArrayList<RowsBean>
            }

            Glide.with(mContext).load(data.floorIcon).into(ivTitleTag)

            if (data.isMainTypeImg()){
                tvTitle.text = ""
                ivImgTitle.isVisible = true

                mContext.glideLoadWithPlaceHolder(data.mainTitle?:"",ivImgTitle,R.drawable.transparent,R.drawable.transparent,true)
            }else{
                ivImgTitle.isVisible = false
                tvTitle.text = data.mainTitleText?:""
            }

            data.mainTitleColor?.let {
                try {
                    tvTitle.setTextColor(Color.parseColor(it))
                } catch (e: Exception) {
                    tvTitle.setTextColor(mContext.getColorById(R.color.color_292933))
                }
            }

            data.mainTitleColorTransparency?.let {
                var alpha = 1f
                try {
                	alpha = it.toFloat()/100
                }catch (e:Exception){
                }
                tvTitle.alpha = alpha
            }

            if (data.isSubTypeImg()){
                tvDescribe.text = ""
                ivImgDescribe.isVisible = true

                mContext.glideLoadWithPlaceHolder(data.subTitle?:"",ivImgDescribe,R.drawable.transparent,R.drawable.transparent,true)
            }else{
                ivImgDescribe.isVisible = false
                tvDescribe.text = data.subTitleText?:""
            }

            data.subTitleColor?.let {
                try {
                    tvDescribe.setTextColor(Color.parseColor(it))
                } catch (e: Exception) {
                    tvDescribe.setTextColor(mContext.getColorById(R.color.color_9A9A9A))
                }
            }

            data.subTitleColorTransparency?.let {
                var alpha = 1f
                try {
                    alpha = it.toFloat()/100
                }catch (e:Exception){
                }
                tvDescribe.alpha = alpha
            }


            data.bgImg?.let{
                Glide.with(mContext).load(it).centerCrop().error(R.color.white).into(ivBg)
            }?: kotlin.run {
                data.bgRes?.let {
                    try {
                        ivBg.setBackgroundColor(Color.parseColor(it))
                    } catch (e: Exception) {
                        e.printStackTrace()
                        ivBg.setBackgroundColor(mContext.getColorById(R.color.white))
                    }
                }?:run {
                    ivBg.setBackgroundColor(mContext.getColorById(R.color.white))

                }
            }


            tvTitle.setOnClickListener {
                mData?.let {
                    mOnItemClickListener?.invoke(it,TRACK_TYPE_MAIN,data.mainTitleText?:"",WHERE_CLICK_MAIN_TITLE)
                    trackItemClick(data.trackData)
                }
            }

            ivImgTitle.setOnClickListener {
                mData?.let {
                    mOnItemClickListener?.invoke(it,TRACK_TYPE_MAIN,data.mainTitleText?:"",WHERE_CLICK_MAIN_TITLE)
                    trackItemClick(data.trackData)
                }
            }


            clDescribe.setOnClickListener {
                mData?.let {
                    mOnItemClickListener?.invoke(it, TRACK_TYPE_DEPUTY,data.subTitleText?:"", WHERE_CLICK_SUBTITLE_TITLE)
                }
            }

            ivImgDescribe.setOnClickListener {
                mData?.let {
                    mOnItemClickListener?.invoke(it, TRACK_TYPE_DEPUTY,data.subTitleText?:"", WHERE_CLICK_SUBTITLE_TITLE)
                    trackItemClick(data.trackData)
                }
            }

            root.setOnClickListener {
                mData?.let {
                    mOnItemClickListener?.invoke(it,0,"",WHERE_CLICK_BLANK)
                    trackItemClick(data.trackData)
                }
            }

            resourceViewTrackMap[TRACK_RESOURCE_VIEW_KEY]?.let {
                if (System.currentTimeMillis() - it > TRACK_DURATION){
                    resourceViewTrackMap[TRACK_RESOURCE_VIEW_KEY] = System.currentTimeMillis()
                }
            }?: kotlin.run {
                resourceViewTrackMap[TRACK_RESOURCE_VIEW_KEY] = System.currentTimeMillis()
            }
        }
    }

    private fun trackItemClick(trackData: TrackData?) {
        //该组件暂时不要qt埋点
//        onSubcomponentClick(mContext, trackData)
    }

    override fun onViewAttachedToWindow(holder: ConcatFrequentPurchaseVH) {
        super.onViewAttachedToWindow(holder)
        val lp: ViewGroup.LayoutParams = holder.itemView.layoutParams
        if (lp is StaggeredGridLayoutManager.LayoutParams) {
            lp.isFullSpan = true
        }
    }

    class ConcatFrequentPurchaseVH(val mBinding: ItemPorcelainTilesFrequentBinding) : RecyclerView.ViewHolder(mBinding.root)
}