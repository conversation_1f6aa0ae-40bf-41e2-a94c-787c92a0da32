package com.ybmmarket20.home.newpage.adapter

import android.graphics.Color
import android.graphics.drawable.GradientDrawable
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.ybmmarket20.R
import com.ybmmarket20.common.getColorById
import com.ybmmarket20.databinding.ItemBannerSearchContentBinding
import com.ybmmarket20.home.newpage.bean.HomeSearchContent
import com.ybmmarket20.home.newpage.bean.HomeSearchContentResponse
import com.youth.banner.adapter.BannerAdapter

/**
 * @class   SearchContentBannerAdapter
 * <AUTHOR>
 * @date  2024/4/22
 * @description  热搜词banner
 */
class SearchContentBannerAdapter(val mDataList:ArrayList<HomeSearchContent>) : BannerAdapter<HomeSearchContent, SearchContentBannerAdapter.FeedStreamBannerVH>(mDataList) {

	var mExposureCallBack: ((HomeSearchContentResponse?,Int)->Unit)? = null

	override fun onCreateHolder(
			parent: ViewGroup,
			viewType: Int): FeedStreamBannerVH {

		return FeedStreamBannerVH(ItemBannerSearchContentBinding.inflate(LayoutInflater.from(parent.context), parent, false))
	}

	override fun onBindView(
			holder: FeedStreamBannerVH,
			data: HomeSearchContent,
			position: Int,
			size: Int) {

		holder.mBinding.apply {
			tvSearchContent.text = data.content?.hotWord?:""
			if (data.content?.hotStyle != null && !data.content.hotStyle?.scanColor.isNullOrEmpty()) {
				try {
					tvSearchContent.setTextColor(Color.parseColor(data.content.hotStyle?.scanColor))
				} catch (e: Exception) {
					tvSearchContent.setTextColor(root.context.getColorById(R.color.text_color_333333))
				}
			} else if (!data.scanColor.isNullOrEmpty()) {
				try {
					tvSearchContent.setTextColor(Color.parseColor(data.scanColor))
				}catch (e:Exception){
					tvSearchContent.setTextColor(root.context.getColorById(R.color.text_color_333333))
				}
			} else {
				tvSearchContent.setTextColor(root.context.getColorById(R.color.text_color_333333))
			}

			if (data.content?.hotStyle != null && !data.content.hotStyle?.scanTransparency.isNullOrEmpty()) {
				try {
					tvSearchContent.alpha =
						data.content.hotStyle?.scanTransparency?.toFloat()?.div(100) ?: 1f
				} catch (e: Exception) {
					tvSearchContent.alpha = 1f
				}
			} else if (!data.scanTransparency.isNullOrEmpty()) {
				try {
					tvSearchContent.alpha = data.scanTransparency.toFloat() / 100
				} catch (e: Exception) {
					tvSearchContent.alpha = 1f
				}
			} else {
				tvSearchContent.alpha = 1f
			}
			if (data.content?.hotStyle != null && !TextUtils.isEmpty(data.content.hotStyle?.atmosphereIconText)) {
				tvHotContent.visibility = View.VISIBLE

				tvHotContent.text = data.content.hotStyle?.atmosphereIconText ?: ""
				if (!TextUtils.isEmpty(data.content.hotStyle?.atmosphereFontColor)) {
					try {
						tvHotContent.setTextColor(Color.parseColor(data.content.hotStyle?.atmosphereFontColor))
					} catch (e: Exception) {
						tvHotContent.setTextColor(root.context.getColorById(R.color.back_white))
					}
				} else {
					tvHotContent.setTextColor(root.context.getColorById(R.color.back_white))
				}
				var cornerRadius=8f
				if (!TextUtils.isEmpty(data.content.hotStyle?.atmosphereBackgroundColor)) {
					try {
						tvHotContent.apply {
							val shape = GradientDrawable()
							shape.cornerRadius = cornerRadius // 设置圆角半径
							shape.setColor(Color.parseColor(data.content.hotStyle?.atmosphereBackgroundColor)) // 设置背景颜色
							background = shape
						}
					} catch (e: Exception) {
						tvHotContent.apply {
							val shape = GradientDrawable()
							shape.cornerRadius = cornerRadius // 设置圆角半径
							shape.setColor(root.context.getColorById(R.color.color_FF2121)) // 设置背景颜色
							background = shape
						}
					}
				} else {
					tvHotContent.apply {
						val shape = GradientDrawable()
						shape.cornerRadius = cornerRadius // 设置圆角半径
						shape.setColor(root.context.getColorById(R.color.color_FF2121)) // 设置背景颜色
						background = shape
					}
				}
			} else {
				tvHotContent.visibility = View.GONE
			}
		}

	}

	class FeedStreamBannerVH(val mBinding: ItemBannerSearchContentBinding) : RecyclerView.ViewHolder(mBinding.root)


}

