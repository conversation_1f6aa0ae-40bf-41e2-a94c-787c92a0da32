package com.ybmmarket20.home.newpage.bean

import com.google.gson.annotations.SerializedName
import com.ybmmarket20.bean.RowsBean
import com.ybmmarket20.bean.homesteady.BaseModule
import com.ybmmarket20.bean.homesteady.FastEntry
import com.ybmmarket20.bean.homesteady.Tabbar
import com.ybmmarket20.xyyreport.spm.TrackData

/**
 * 首页相关Bean
 */


data class TabBeanResponse(
		val classBtnBackground:String?, //分类背景url
		val className:String?, //分类名称
		val tabList: MutableList<TabBean>?, //tab配置列表
		var timeStamp: Long = 0L, //非后端接口数据，本地记录的时间戳 做缓存时间比较用的
		val allProduct: AllProduct?
)

data class AllProduct(
	val trackData: TrackData?
)

data class TabBean(
		val tabId:String?,  //tabId
		val tabName:String?, //tab名称
		val tabType:String?, //tab类型 1首页 2tab页 3活动页
		val titleType:String?, //标题类型 1文字 2图片
		val tabImag:String?, //tab图片链接
		val jumpLink:String?, //跳转链接 如果tab类型是活动页 用来跳转
		val wordColor:String?, //文字颜色
		val wordTransparency:String?, //文字透明度
		val isEyeImg:String?, //醒目url
		val bgImg: String?,  //模块背景图片
		val bgRes: String?,  //模块背景颜色
		val messageButtonColor: String?, //消息按钮颜色
		val messageButtonTransparency: String?, //消息按钮透明度
		val scanColor: String?, //热词文字颜色
		val scanTransparency: String?, //热词文字透明度
		val messageButtonUrl: String?, //消息按钮图片
		val searchImageUrl: String?, //搜索图图片
		val searchBoxColor: String?, //搜索框颜色
		val searchBoxTransparency: String?, //搜索框透明度
		val messageSubColor: String?, //消息角标颜色
		val messageSubTransparency: String?, //消息角标透明度
		val searchImageColor: String?, //搜索图颜色
		val searchImageTransparency: String?, //搜索图透明度
		val sptype: String?, //来源类型
		val jgspid: String?, //来源ID
		val sid: String?, //追踪ID
		val pageType: String?,
		val pageId: String?,
		val pageName: String?,
		val componentPosition: String?,
		val componentName: String?,
		val componentTitle: String?,
		val trackData: TrackData?
) {
	//是否是图片类型
	fun isTabPic() = titleType == "2"

	fun isH5Url() = tabType == "3"
}

data class HomeSearchContentResponse(
		val hotWord:String?="", //搜索热词
		val hrefUrl:String?="", //跳转搜索页后跳转用的链接
		val type: String? = "",//热词类型1不挂链接2专题页3店铺页4动态商品页5凑单页
		val couponId: String? = "",//券id
		val trackData: TrackData?,
		var hotStyle: HotStyleBean?
)

data class HotStyleBean(
	val scanColor: String? = "", //字体颜色
	val scanTransparency: String?="",  //透明度

	val atmosphereIconText: String? = "",  //氛围图标词
	val atmosphereFontColor: String? = "", //氛围图提示词字体颜色
	val atmosphereBackgroundColor: String? = "" //氛围图提示词背景色
)
data class HomeSearchContent(
		val content: HomeSearchContentResponse?=null,
		val scanColor: String?="", //热词文字颜色
		val scanTransparency: String?="", //热词文字透明度
		val trackData: TrackData?
)

data class HomeModulesResponse(
		val licenseStatus: String? = "", //资质认证
		val modules: HomeModules? = null, //首页模块
		val trackData: TrackData?,
		var timeStamp: Long = 0L //非后端接口数据，本地记录的时间戳 做缓存时间比较用的
)

data class HomeModules(
		val newIndexSearch: NewSearchBox? = null, //搜索 - 推荐页专用
		val tabSearch: NewSearchBox? = null,
		val bannerCapsule: Capsule? = null, //胶囊位
		val iconEntry: FastEntry? = null, //快捷入口
		val recommendationProduct: RecommendCommodity? = null, //推荐商品
		val frequentPurchaseList: FrequentPurchase? = null, //常购清单 tab页没有常购清单
		val valueRecommendation: Affordable? = null, //超值清单
		val newIndexBottomView: BottomAdvertisement? = null, //吸底广告
		val newTabTopImage: AtmosphereImage? = null, //氛围图 - tab页用
		@SerializedName("iconEntryTab") val tabFastEntry: FastEntry? = null, //tab页-快捷入口
		@SerializedName("newLRGoodsPosition") val tabRecommendationProduct: RecommendCommodity? = null, //tab页-推荐商品
		@SerializedName("newTabDiscountRecomment") val tabValueRecommendation: Affordable? = null, //超值清单
		val newTabBottomView: BottomAdvertisement? = null, //tab页-吸底广告
		val newIndexTabbar:Tabbar? = null

)

data class BottomAdvertisement(
		@SerializedName("content") val bottomAdvertisementContent: BottomAdvertisementContent? = null,
        val name: String? = null,
        val title: String? = null,
        val instance_id: String? = null
)

data class BottomAdvertisementContent(
		val bgImg: String? = null, //背景图片
		val bgRes: String? = null, //背景颜色
		val showType: String? = null, // 图片展示机制 1：每次按顺序刷新 2：随机
		@SerializedName("list") val bottomAdvertisementList: MutableList<BottomAdvertisementItemBean>? = null
){

	fun isNeedRandom():Boolean = showType == "2"

}
data class BottomAdvertisementItemBean(
		val url: String? = null, //图片url
		val hrefUrl: String? = null, //跳转路径
		val position: String? = null, // 帧数
)

data class Affordable(
		@SerializedName("content") val affordableContent: AffordableContent? = null,
		val name: String? = null,
		val title: String? = null,
		val pageType: String? = null,
		val pageId: String? = null,
		val pageName: String? = null,
		val componentPosition: String? = null,
		val componentName: String? = null,
		val componentTitle: String? = null,
		val trackData: TrackData? = null
)

data class AffordableContent(
		@SerializedName("list") val affordableFloorList: MutableList<AffordableFloorContent>? = null,
)

data class AffordableFloorContent(
		@SerializedName("productList") val capsuleCommonProductList: MutableList<RowsBean>? = null,
		val bgImg: String? = null, //背景图片
		val bgRes: String? = null, //背景颜色
		val activityId: String? = null, //活动id
		val activityName: String? = null, //活动名称
		val floorIcon: String? = null, //楼层图标
		val mainTitleType: String? = null, //主标题选项
		val mainTitleText: String? = null, //主标题
		val mainTitleColor: String? = null, //主标题颜色
		val mainTitle: String? = null, //主标题设置图片
		val mainTitleColorTransparency: String? = null, //主标题透明度
		val subTitleType: String? = null, //复标题选项
		val subTitleText: String? = null, //复标题
		val subTitleColor: String? = null, //复标题颜色
		val subTitle: String? = null, //复标题设置图片
		val subTitleColorTransparency: String? = null, //复标题透明度
		val hrefType: String? = null,
		val hrefUrl: String? = null,
		val sptype: String? = null,
		val jgspid: String? = null,
		val sid: String? = null,
		val trackData: TrackData? = null
){

	//主标题是文字还是图片
	fun isMainTypeImg() = mainTitleType == "2"
	//主标题是文字还是图片
	fun isSubTypeImg() = subTitleType == "2"

}

data class FrequentPurchase(
		@SerializedName("content") val frequentPurchaseContent: FrequentPurchaseContent? = null,
		val pageType: String? = null,
		val pageId: String? = null,
		val pageName: String? = null,
		val componentPosition: String? = null,
		val componentName: String? = null,
		val componentTitle: String? = null,
		val trackData: TrackData? = null
): BaseModule()

data class FrequentPurchaseContent(
		@SerializedName("list") val frequentPurchaseFloorList: MutableList<FrequentPurchaseFloorContent>? = null
)

data class FrequentPurchaseFloorContent(
		@SerializedName("productList") val capsuleCommonProductList: MutableList<RowsBean>? = null,
		val bgImg: String? = null, //背景图片
		val bgRes: String? = null, //背景颜色
		val activityId: String? = null, //活动id
		val activityName: String? = null, //活动名称
		val floorIcon: String? = null, //楼层图标
		val mainTitleType: String? = null, //主标题选项
		val mainTitleText: String? = null, //主标题
		val mainTitleColor: String? = null, //主标题颜色
		val mainTitle: String? = null, //主标题设置图片
		val mainTitleColorTransparency: String? = null, //主标题透明度
		val subTitleType: String? = null, //复标题选项
		val subTitleText: String? = null, //复标题
		val subTitle: String? = null, //复标题设置图片
		val subTitleColor: String? = null, //复标题颜色
		val subTitleColorTransparency: String? = null, //复标题透明度
		val hrefType: String? = null,
		val hrefUrl: String? = null,
		val sptype: String? = null,
		val jgspid: String? = null,
		val sid: String? = null,
		val trackData: TrackData? = null
){

	//主标题是文字还是图片
	fun isMainTypeImg() = mainTitleType == "2"
	//主标题是文字还是图片
	fun isSubTypeImg() = subTitleType == "2"

}

//data class CapsuleCommonProductBean(
//		val id: String? = null, //商品ID
//		val showName: String? = null, //商品展示名称
//		val productUnit: String? = null, //商品单位
//		val imageUrl: String? = null,//图片
//		val fob: String? = null, //价格
//		val spec: String? = null, //规格
//		val status: String? = null, //状态
//		val sptype: String? = null,
//		val spid: String? = null,
//		val sid: String? = null,
//)

data class RecommendCommodity(
		@SerializedName("content") val recommendCommodityContent: RecommendCommodityContent? = null,
		val pageId: String? = null,
		val pageName: String? = null,
		val pageType: String? = null,
		val componentPosition: String? = null,
		val componentName: String? = null,
		val componentTitle: String? = null,
		val trackData: TrackData? = null
): BaseModule()

data class RecommendCommodityContent(
		@SerializedName("list") val recommendCommodityList: MutableList<RecommendCommodityItemBean>? = null
)

data class RecommendCommodityItemBean(
		val activityId: String? = null,
		val activityName: String? = null,
		val commendationPosition: String? = null, //推荐位置 1：左侧推荐区 2：右侧推荐区
		val floorIcon: String? = null, //楼层图标
		val mainTitleType: String? = null, //主标题选项
		val mainTitleText: String? = null, //主标题
		val mainTitleColor: String? = null, //主标题颜色
		val mainTitle: String? = null, //主标题设置成图片
		val mainTitleColorTransparency: String? = null, //主标题透明度
		val subTitleType: String? = null, //复标题选项
		val subTitleText: String? = null, //复标题
		val subTitle: String? = null, //复标题设置成图片
		val subTitleColor: String? = null, //复标题颜色
		val subTitleColorTransparency: String? = null, //复标题透明度
		val hrefType: String? = null, //跳转类型 1 内部跳转 2外部跳转
		val hrefUrl: String? = null, //跳转路径
		val bgImg: String? = null, //背景图片
		val bgRes: String? = null, //背景颜色
		val sptype: String? = null,
		val jgspid: String? = null,
		val sid: String? = null,
		val subModuleType: String? = null,
		val subModulePosition: String? = null,
		val subModuleName: String? = null,
		@SerializedName("productList") val capsuleCommonProductList: MutableList<RowsBean>? = null,
		val trackData: TrackData? = null

		){

	//主标题是文字还是图片
	fun isMainTypeImg() = mainTitleType == "2"
	//主标题是文字还是图片
	fun isSubTypeImg() = subTitleType == "2"
}

data class Capsule(
		@SerializedName("content") val capsuleContent: CapsuleContent? = null,
		val pageId: String? = null,
		val pageName: String? = null,
		val pageType: String? = null,
		val componentPosition: String? = null,
		val componentName: String? = null,
		val componentTitle: String? = null,
		val trackData: TrackData? = null
): BaseModule()

data class CapsuleContent(
		@SerializedName("list") val capsuleAdvertisementList: MutableList<CapsuleAdvertisementItemBean>? = null
)

data class CapsuleAdvertisementItemBean(
		val activityId: String? = "",
		val classType: String? = "",
		val activityName: String? = "", //图片名称
		val materialImg: String? = "", //预览图片
		val classBackgroundType: String? = "", //背景色配置 1 纯色 2 图片
		val classBackground: String? = "", //背景色 classBackgroundType为1，此字段为颜色，反则为图片（公共字段）
		val classBackgroundTransparency: String? = "", //透明度
		val hrefType: String? = "", //跳转类型 1 内部跳转 2 外部链接
		val hrefUrl: String? = "", //跳转路径
		val sptype: String? = "",
		val jgspid: String? = "",
		val sid: String? = "",
		val subModuleType: String?,
		val subModulePosition: String?,
		val subModuleName: String?,
		val trackData: TrackData?
){
	fun isColor():Boolean = classBackgroundType == "1"
}

data class NewSearchBox(
		@SerializedName("content") val newSearchBoxContent: NewSearchBoxContent? = null,
		val trackData: TrackData?
): BaseModule()

data class NewSearchBoxContent(
		val bgImg: String = "",
		val activityName: String = "",
		val hrefType: String = "",
		val hrefUrl: String = "",
)

data class AtmosphereImage(
		@SerializedName("content") val atmosphereImageContent: AtmosphereImageContent? = null,
		var componentPosition: String?,
		var componentName: String?,
		var componentTitle: String?,
		val trackData: TrackData?
): BaseModule()

data class AtmosphereImageContent(
		@SerializedName("list")val atmosphereImageContentList: MutableList<AtmosphereImageContentListBean>?
)

data class AtmosphereImageContentListBean(
		val imgUrl: String?, //图片链接
		val activityId: String?,
		val activityName: String?, //活动名称
		val hrefType: String?, //跳转类型
		val hrefUrl: String?, //跳转路径
		val sptype: String?,
		val jgspid: String?,
		val sid: String?,
		val trackData: TrackData?
)

data class HomeFeedStreamResponse(
		val pageNum: String?,
		val pageSize: String?,
		val isEnd: Boolean?,
		val isGuaranteed:Boolean?,
		@SerializedName("list") val feedStreamList: MutableList<HomeFeedStreamBean>?,
		var responseLocalTime: Long = 0L, //获取到秒杀信息的本地时间, 单位ms （不是后台返的  记录时间用）
		var pageType: String?,
		var pageId: String?,
		var pageName: String?,
		var componentPosition: String?,
		var componentName: String?,
		var componentTitle: String?,
		var timeStamp: Long = 0L, //非后端接口数据，本地记录的时间戳 做缓存时间比较用的
		val trackData: TrackData?,
		val requestParam: Map<String, String>?
)

data class HomeFeedStreamBean(
		val feedType: String?, //1 轮播图，2 feed流广告，3 feed流商品
		val bannerList: MutableList<HomeFeedBanner>?, //feed轮播图
		val advertisement: HomeFeedAD?, //feed广告
		val product: RowsBean?, //商品
		var reLoadTag: Int = 0
) {
	companion object {
		const val FEED_STREAM_BANNER_TYPE = 1
		const val FEED_STREAM_AD_TYPE = 2
		const val FEED_STREAM_PRODUCT_TYPE = 3
	}

	fun getFeedType(): Int {
		return when (feedType) {
			"1" -> FEED_STREAM_BANNER_TYPE
			"2" -> FEED_STREAM_AD_TYPE
			"3" -> FEED_STREAM_PRODUCT_TYPE
			else -> FEED_STREAM_BANNER_TYPE
		}
	}

	data class HomeFeedBanner(
			val activityId: String?, //活动id
			val activityName: String?, //活动名称
			val hrefUrlType: String?, //跳转链接类型
			val hrefUrls: String?, //跳转链接
			val bannerImgs: String?, //图片
			val bannerLocation: String?, //帧数
			val sptype: String?,
			val jgspid: String?,
			val sid: String?,
			val subModuleType: String?,
			val subModulePosition: String?,
			val subModuleName: String?,
		    val trackData: TrackData?
	)

	data class HomeFeedAD(
			val showType: String?, //1 顺序 2随机
			val advertisementDetailList: MutableList<AdvertisementDetailBean>?
	){
		fun isNeedRandom():Boolean = showType == "2"
	}

	data class AdvertisementDetailBean(
			val activityId: String?, //活动id
			val activityName: String?, //活动名称
			val hrefUrlType: String?, //跳转链接类型
			val hrefUrls: String?, //跳转链接
			val imageUrl: String?, //图片
			val position: String?, //帧数
			val sptype: String?,
			val jgspid: String?,
			val sid: String?,
			val subModuleType: String?,
			val subModulePosition: String?,
			val subModuleName: String?,
			val trackData: TrackData?
	)
}