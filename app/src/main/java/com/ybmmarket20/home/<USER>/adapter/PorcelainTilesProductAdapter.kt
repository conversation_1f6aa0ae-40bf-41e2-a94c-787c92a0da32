package com.ybmmarket20.home.newpage.adapter

import android.content.Context
import android.text.TextUtils
import android.util.TypedValue
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.RecyclerView
import com.ybmmarket20.R
import com.ybmmarket20.bean.RowsBean
import com.ybmmarket20.bean.getSingleStepSpannableForHome
import com.ybmmarket20.bean.isStep
import com.ybmmarket20.common.glideLoadWithPlaceHolder
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.databinding.ItemPorcelainTilesProductBinding

/**
 * @class   PorcelainTilesAdapter
 * <AUTHOR>
 * @date  2024/4/12
 * @description   瓷片中的item产品的Adapter
 */
class PorcelainTilesProductAdapter: RecyclerView.Adapter<PorcelainTilesProductAdapter.PorcelainTilesVH>() {

    var mDataList = ArrayList<RowsBean>()
    set(value) {
        field.clear()
        field.addAll(value)
        notifyDataSetChanged()
    }

    var mOnItemClickListener: ((bean:RowsBean,type:Int) -> Unit)? = null
    private lateinit var mContext: Context
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): PorcelainTilesVH {
        mContext = parent.context
        return PorcelainTilesVH(DataBindingUtil.inflate(LayoutInflater.from(mContext),
                R.layout.item_porcelain_tiles_product, parent, false))
    }

    override fun getItemCount(): Int = mDataList.size

    override fun onBindViewHolder(holder: PorcelainTilesVH, position: Int) {

        holder.mBinding.apply {
            val data = mDataList[position]
            val url = data.imageUrl ?: ""
            mContext.glideLoadWithPlaceHolder(AppNetConfig.LORD_IMAGE + url, ivProduct)
            // 增加价格展示逻辑, 包括控销、签署协议、是否符合协议标准展示价格
            if (!data.controlTitle.isNullOrEmpty()){
                tvPrice.setTextSize(TypedValue.COMPLEX_UNIT_DIP,12f)
            }else{
                tvPrice.setTextSize(TypedValue.COMPLEX_UNIT_DIP,11f)
            }
            if (data.rangePriceBean.isStep()) {
                tvPrice.text = data.rangePriceBean.getSingleStepSpannableForHome()
            } else {
                tvPrice.text = data.getShowPriceStrNew(false,false,11,16)
            }
            root.setOnClickListener {
                mOnItemClickListener?.invoke(data,position+1)
            }
        }
    }

    class PorcelainTilesVH(val mBinding: ItemPorcelainTilesProductBinding) : RecyclerView.ViewHolder(mBinding.root)
}