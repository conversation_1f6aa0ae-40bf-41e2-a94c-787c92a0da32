package com.ybmmarket20.home.newpage.viewmodel

import android.app.Application
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.RowsBean
import com.ybmmarket20.home.newpage.bean.TabBeanResponse
import com.ybmmarket20.network.request.FrequentPurchaseRequest
import com.ybmmarket20.viewmodel.BaseViewModel
import kotlinx.coroutines.launch

/**
 * @class   FrequentPurchaseListViewModel
 * <AUTHOR>
 * @date  2024/5/6
 * @description
 */
class FrequentPurchaseListViewModel(appLike:Application) : BaseViewModel(appLike) {

	companion object{
		private const val DEFAULT_PAGE_POSITION = 1
		private const val DEFAULT_PAGE_SIZE = 10
	}
	var pagePosition: Int = DEFAULT_PAGE_POSITION
	var pageSize:Int = DEFAULT_PAGE_SIZE

	//list列表
	private val _tabListLiveData = MutableLiveData<Pair<Boolean,MutableList<RowsBean>>>()
	val tabListLiveData: LiveData<Pair<Boolean,MutableList<RowsBean>>> = _tabListLiveData

	private val _canLoadLiveData = MutableLiveData<Boolean>()
	val canLoadLiveData: LiveData<Boolean> = _canLoadLiveData

	private val _refreshFinishedLiveData = MutableLiveData<Boolean>()
	val refreshFinishedLiveData: LiveData<Boolean> = _refreshFinishedLiveData

	fun requestListData(isRefresh: Boolean,anchorCsuIds:String) {
		if (isRefresh){
			showLoading()
		}
		if (isRefresh){
			pagePosition = DEFAULT_PAGE_POSITION
			_canLoadLiveData.value = true
		}

		viewModelScope.launch {
			val result = FrequentPurchaseRequest().requestFrequentPurchaseList(pagePosition,pageSize,anchorCsuIds)
			_tabListLiveData.value = Pair(isRefresh,result.data?.productList?: arrayListOf())
			_refreshFinishedLiveData.value = true
			dismissLoading()

			if (result.isSuccess){
				if (result.data?.isEnd == false){
					pagePosition++
					_canLoadLiveData.value = true
				}else{
					_canLoadLiveData.value = false
				}
			}
		}

	}

}