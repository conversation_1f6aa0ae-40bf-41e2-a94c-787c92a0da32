package com.ybmmarket20.home.newpage.adapter

import android.content.Context
import com.ybmmarket20.xyyreport.spm.ISpm
import com.ybmmarket20.xyyreport.spm.TrackData

interface IHomeComponentAnalysis {
    fun onComponentExposure(context: Context, trackData: TrackData?, position: Int, block: (()-> Unit)? = null)
    fun onSubcomponentClick(context: Context, trackData: TrackData?)
    fun resetExposureRecord()
    fun reComponentExposure()
}