package com.ybmmarket20.home.newpage.adapter

import android.annotation.SuppressLint
import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.StaggeredGridLayoutManager
import com.ybmmarket20.R
import com.ybmmarket20.bean.homesteady.FastEntry
import com.ybmmarket20.bean.homesteady.FastEntryItem
import com.ybmmarket20.common.JgTrackBean
import com.ybmmarket20.databinding.ItemConcatFastEntryBinding
import com.ybmmarket20.xyyreport.SpmLogUtil

/**
 * @class   ConcatFastEntryAdapter
 * <AUTHOR>
 * @date  2024/4/28
 * @description  首页快捷入口
 */
class ConcatFastEntryAdapter : HomeComponentAnalysisAdapter<ConcatFastEntryAdapter.FastEntryVH>() {

	var mData: FastEntry? = null
		set(value) {
			field = value
			notifyDataSetChanged()
		}

	var mOnItemClickTrackListener: ((mData: FastEntry?, model:Int, offset:String, url:String,item: FastEntryItem) -> Unit)? = null
	var mComponentExposureListener:((mData:FastEntry?)->Unit)? = null
	private lateinit var mContext: Context

	var pageId = 0
	var module = 0
	var navigation =""
	//      value:当时埋点的时间戳
	private val resourceViewTrackMap = hashMapOf<String, Long>()
	var mHomeJgspid: String? = null
	var jgTrackBean:JgTrackBean? = null
	companion object{
		private const val TRACK_DURATION = 2 * 60 * 1000 //2分钟内不上报
		private const val TRACK_RESOURCE_VIEW_KEY = "track_resource_view_key"
	}
	override fun onCreateViewHolder(
			parent: ViewGroup,
			viewType: Int
	): FastEntryVH {
		mContext = parent.context
		return FastEntryVH(DataBindingUtil.inflate(LayoutInflater.from(mContext), R.layout.item_concat_fast_entry, parent, false))
	}

	override fun getItemCount(): Int = mData?.fastEntryContent?.fastEntryItemList?.let { if (it.size > 0) 1 else 0 } ?: 0

	@SuppressLint("SuspiciousIndentation")
    override fun onBindViewHolder(
			holder: FastEntryVH,
			position: Int
	) {
		onComponentExposure(mContext, mData?.trackData, position) {
			SpmLogUtil.print("首页-组件-快捷入口曝光")
		}
		holder.mBinding.apply {
            mData?.fastEntryContent?.let {
				it.fastEntryItemList?.forEach { item->
					item.trackPageId = pageId
					item.trackModule = module
				}
				fastEntryView.mHomeJgspid = mHomeJgspid
				fastEntryView.mSuperData = mData
                fastEntryView.setFastEntryDataAll(it.fastEntryItemList?: arrayListOf(),it.entryRowNum)
	            fastEntryView.setAnalysisCallback(::fastEntryAnalysisCallback)
	            fastEntryView.jgTrackBean = <EMAIL>

	            resourceViewTrackMap[TRACK_RESOURCE_VIEW_KEY]?.let {
		            if (System.currentTimeMillis() - it > TRACK_DURATION){
			            resourceViewTrackMap[TRACK_RESOURCE_VIEW_KEY] = System.currentTimeMillis()
		            }
	            }?: kotlin.run {
		            resourceViewTrackMap[TRACK_RESOURCE_VIEW_KEY] = System.currentTimeMillis()
	            }

				mComponentExposureListener?.invoke(mData)

            }
		}
	}

	override fun onViewAttachedToWindow(holder: FastEntryVH) {
		super.onViewAttachedToWindow(holder)
		val lp: ViewGroup.LayoutParams = holder.itemView.layoutParams
		if (lp is StaggeredGridLayoutManager.LayoutParams) {
			lp.isFullSpan = true
		}
	}

	class FastEntryVH(val mBinding: ItemConcatFastEntryBinding) : RecyclerView.ViewHolder(mBinding.root)

	/**
	 * 快捷入口点击埋点回调
	 */
	private fun fastEntryAnalysisCallback(
			action: String,
			position: Int,
			item: FastEntryItem,
			view: View,
			size: Int,
			image_url: String?
	) {

		var model = 0 //1代表1排1页，2代表1排2页，3代表2排1页
		var offset = "" //1排1页为1,2,3,4,5依次类推，1排2页为1,2,3,4,5，..9； 2排位1-1；1-2；2-1；2-5;依次类推
		var url = ""
//		onSubcomponentClick(mContext, item.trackData)
		when(item.pages){
			1 ->{ //仅一页

				if (item.pageItemCount <=5){ //只有一行
					model = 1
					offset = (position+1).toString()
				}else{ //有两行
					if (position>=5){ //第一页第二行
						model = 3
						offset = "2-${position-4}" //position下标+1 又减去5  5个一行
					}else{  //第一行
						model = 3
						offset = "1-${position+1}"
					}
				}
			}

			2->{  //两页
				model = 2

				when(item.pageIndex){
					0->{
						offset = (position+1).toString()
					}

					else ->{
						offset = (position+6).toString() //position下标+1 又加5
					}
				}
			}
			else->{
				return
			}
		}

		mOnItemClickTrackListener?.invoke(mData, model,offset,action,item)
	}
}