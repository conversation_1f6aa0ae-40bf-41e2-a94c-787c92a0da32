package com.ybmmarket20.home.newpage.adapter

import android.content.Context
import android.graphics.Color
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.StaggeredGridLayoutManager
import com.bumptech.glide.Glide
import com.ybmmarket20.R
import com.ybmmarket20.bean.RowsBean
import com.ybmmarket20.common.dp
import com.ybmmarket20.common.getColorById
import com.ybmmarket20.common.glideLoadWithPlaceHolder
import com.ybmmarket20.databinding.ItemPorcelainTilesSuperValueBinding
import com.ybmmarket20.home.newpage.HomeTabCommonFragment
import com.ybmmarket20.home.newpage.bean.Affordable
import com.ybmmarket20.home.newpage.bean.AffordableFloorContent
import com.ybmmarket20.xyyreport.SpmLogUtil
import com.ybmmarket20.xyyreport.spm.TrackData

/**
 * @class   ConcatSuperValueListAdapter
 * <AUTHOR>
 * @date  2024/4/12
 * @description   超值清单
 */
class ConcatSuperValueListAdapter : HomeComponentAnalysisAdapter<ConcatSuperValueListAdapter.ConcatSuperValueFloorVH>() {

    var mData: Affordable? = null
        set(value) {
            field = value
            notifyDataSetChanged()
        }
    var mOnProductItemClickListener: ((superData: Affordable?, itemBean: AffordableFloorContent, RowsBean, offset:Int) -> Unit)? = null
    var mOnItemClickListener: ((superData: Affordable?,itemBean: AffordableFloorContent, type: Int, content: String, where:Int) -> Unit)? = null

    private lateinit var mContext: Context
    private val resourceViewTrackMap = hashMapOf<String, Long>()
    var mHomeJgspid: String? = null
    var navigation = ""

    companion object {
        private const val SPAN_COUNT = 4
        private const val TRACK_TYPE_MAIN = 1
        private const val TRACK_TYPE_DEPUTY = 2

        private const val TRACK_DURATION = 2 * 60 * 1000 //2分钟内不上报
        private const val TRACK_RESOURCE_VIEW_KEY = "track_resource_view_key"
    }

    override fun onCreateViewHolder(
            parent: ViewGroup,
            viewType: Int
    ): ConcatSuperValueFloorVH {
        mContext = parent.context
        return ConcatSuperValueFloorVH(DataBindingUtil.inflate(LayoutInflater.from(mContext), R.layout.item_porcelain_tiles_super_value, parent, false))
    }

    override fun getItemCount(): Int = mData?.affordableContent?.affordableFloorList?.let { if (it.size > 0) 1 else 0 }
            ?: 0

    override fun onBindViewHolder(
            holder: ConcatSuperValueFloorVH,
            position: Int
    ) {

        onComponentExposure(mContext, mData?.trackData, position) {
            SpmLogUtil.print("首页-组件-超值推荐曝光")
        }
        holder.mBinding.apply {
            val data = mData?.affordableContent?.affordableFloorList?.let { if (it.size > 0) it[0] else return }
                    ?: return

            //因为这个布局是共用的 不能直接在xml里面改 就在这改
            root.layoutParams = (root.layoutParams as StaggeredGridLayoutManager.LayoutParams).apply {
                setMargins(7.dp, 7.dp, 7.dp, 0)
            }

            rvPorcelainTiles.layoutManager = GridLayoutManager(mContext, SPAN_COUNT)
            rvPorcelainTiles.adapter = PorcelainTilesProductAdapter().apply {
                this.mOnItemClickListener = { bean,type ->

                    mData?.let {
                        //商品的里面的下标就是这里需要的offset
                        <EMAIL>?.invoke(mData, data,bean,type)
                    }
                    trackItemClick(data.trackData)
                }

                this.mDataList = (data.capsuleCommonProductList?: arrayListOf()) as ArrayList<RowsBean>
            }

            Glide.with(mContext).load(data.floorIcon).into(ivTitleTag)

            if (data.isMainTypeImg()){
                tvTitle.text = ""
                ivImgTitle.isVisible = true

                mContext.glideLoadWithPlaceHolder(data.mainTitle?:"",ivImgTitle,R.drawable.transparent,R.drawable.transparent,true)
            }else{
                ivImgTitle.isVisible = false
                tvTitle.text = data.mainTitleText?:""
            }
            data.mainTitleColor?.let {
                try {
                    tvTitle.setTextColor(Color.parseColor(it))
                } catch (e: Exception) {
                    tvTitle.setTextColor(mContext.getColorById(R.color.color_292933))
                }
            }
            data.mainTitleColorTransparency?.let {
                var alpha = 1f
                try {
                    alpha = it.toFloat()/100
                }catch (e:Exception){
                }
                tvTitle.alpha = alpha
            }

            if (data.isSubTypeImg()){
                tvDescribe.text = ""
                ivImgDescribe.isVisible = true

                mContext.glideLoadWithPlaceHolder(data.subTitle?:"",ivImgDescribe,R.drawable.transparent,R.drawable.transparent,true)
            }else{
                ivImgDescribe.isVisible = false

                tvDescribe.text = data.subTitleText?:""
            }
            data.subTitleColor?.let {
                try {
                    tvDescribe.setTextColor(Color.parseColor(it))
                } catch (e: Exception) {
                    tvDescribe.setTextColor(mContext.getColorById(R.color.color_9A9A9A))
                }
            }

            data.subTitleColorTransparency?.let {
                var alpha = 1f
                try {
                    alpha = it.toFloat()/100
                }catch (e:Exception){
                }
                tvDescribe.alpha = alpha
            }

            data.bgImg?.let{
                Glide.with(mContext).load(it).centerCrop().error(R.color.white).into(ivBg)
            }?: kotlin.run {
                data.bgRes?.let {
                    try {
                        ivBg.setBackgroundColor(Color.parseColor(it))
                    } catch (e: Exception) {
                        e.printStackTrace()
                        ivBg.setBackgroundColor(mContext.getColorById(R.color.white))
                    }
                }?:run {
                    ivBg.setBackgroundColor(mContext.getColorById(R.color.white))

                }
            }

            tvTitle.setOnClickListener {
                mData?.let {
                    mOnItemClickListener?.invoke(mData, data, TRACK_TYPE_MAIN,data.mainTitleText?:"", HomeTabCommonFragment.WHERE_CLICK_MAIN_TITLE)
                }
                trackItemClick(data.trackData)
            }

            ivImgTitle.setOnClickListener {
                mData?.let {
                    mOnItemClickListener?.invoke(mData, data, TRACK_TYPE_MAIN,data.mainTitleText?:"", HomeTabCommonFragment.WHERE_CLICK_MAIN_TITLE)
                }
                trackItemClick(data.trackData)
            }


            clDescribe.setOnClickListener {
                mData?.let {
                    mOnItemClickListener?.invoke(mData, data, TRACK_TYPE_DEPUTY,data.subTitleText?:"", HomeTabCommonFragment.WHERE_CLICK_SUBTITLE_TITLE)
                }
                trackItemClick(data.trackData)
            }

            ivImgDescribe.setOnClickListener {
                mData?.let {
                    mOnItemClickListener?.invoke(mData, data, TRACK_TYPE_DEPUTY,data.subTitleText?:"", HomeTabCommonFragment.WHERE_CLICK_SUBTITLE_TITLE)
                }
                trackItemClick(data.trackData)
            }

            root.setOnClickListener {
                mData?.let {
                    mOnItemClickListener?.invoke(mData, data,0,"", HomeTabCommonFragment.WHERE_CLICK_BLANK)
                }
                trackItemClick(data.trackData)
            }

            resourceViewTrackMap[TRACK_RESOURCE_VIEW_KEY]?.let {
                if (System.currentTimeMillis() - it > TRACK_DURATION){
                    resourceViewTrackMap[TRACK_RESOURCE_VIEW_KEY] = System.currentTimeMillis()
                }
            }?: kotlin.run {
                resourceViewTrackMap[TRACK_RESOURCE_VIEW_KEY] = System.currentTimeMillis()
            }
        }
    }

    private fun trackItemClick(trackData: TrackData?) {
        SpmLogUtil.print("首页-组件-超值推荐点击")
        onSubcomponentClick(mContext, trackData)
    }

    override fun onViewAttachedToWindow(holder: ConcatSuperValueFloorVH) {
        super.onViewAttachedToWindow(holder)
        val lp: ViewGroup.LayoutParams = holder.itemView.layoutParams
        if (lp is StaggeredGridLayoutManager.LayoutParams) {
            lp.isFullSpan = true
        }
    }

    class ConcatSuperValueFloorVH(val mBinding: ItemPorcelainTilesSuperValueBinding) : RecyclerView.ViewHolder(mBinding.root)

}