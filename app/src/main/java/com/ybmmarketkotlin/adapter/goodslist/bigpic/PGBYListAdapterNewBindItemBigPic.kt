package com.ybmmarketkotlin.adapter.goodslist.bigpic

import android.content.Context
import android.os.CountDownTimer
import android.text.TextUtils
import android.util.SparseArray
import android.view.View
import android.widget.ImageView
import android.widget.SeekBar
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarket20.R
import com.ybmmarket20.bean.ActPtBean
import com.ybmmarket20.bean.RowsBean
import com.ybmmarket20.common.splicingUrlWithParams
import com.ybmmarket20.constant.IntentCanst
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.view.ProductEditLayoutNew
import com.ybmmarket20.xyyreport.page.search.SearchProductReport
import com.ybmmarketkotlin.adapter.SpellGroupPopWindow
import com.ybmmarketkotlin.adapter.goodslist.findsamegoods.setFindSameGoodsCoupon
import com.ybmmarketkotlin.views.SeckillTimeView

/**
 * 批购包邮
 */
open class PGBYListAdapterNewBindItemBigPic(
    mContext: Context,
    baseViewHolder: YBMBaseHolder,
    rowsBean: RowsBean,
    countDownTimerMap: SparseArray<CountDownTimer>,
    val adapter: RecyclerView.Adapter<*>
) : AbstractGoodsListAdapterNewBindItemBigPic(mContext, baseViewHolder, rowsBean, countDownTimerMap) {


    override fun onGoodsNameWithTags() {
        super.onGoodsNameWithTags()
        baseViewHolder.getView<TextView>(R.id.shop_name).maxLines = 2
    }

    override fun onSpellGroupOrSeckill() {
//        super.onSpellGroupOrSeckill()
        processCountDown(baseViewHolder, rowsBean)
    }

    /**
     * @param baseViewHolder
     * @param rowsBean
     *
     *  1. 秒杀
     *  //2. 隐藏规格、厂家
     *  3. 替换拼团价、无折后价，增加划线价
     *  4. 增加拼购比例文字显示、进度显示、参团按钮
     *  5. 隐藏 数据标签、优惠标签、零售价、加购按钮、中包装
     *
     */
    private fun processCountDown(baseViewHolder: YBMBaseHolder, rowsBean: RowsBean) {

        // 隐藏 数据标签、优惠标签、零售价、加购按钮、中包装、有效期（后加）、售罄图标（如果在显示的话）
//        baseViewHolder.getView<ShopNameWithTagView?>(R.id.data_tag_list_view)?.visibility = View.GONE
//        val dataView = baseViewHolder.getView<ShopNameWithTagView?>(R.id.data_tag_list_view)
//        rowsBean.tags?.productTags?.let { dataView?.visibility = View.VISIBLE }
//        dataView?.bindData(rowsBean.tags?.productTags)
//        dataView.visibility = if (rowsBean.tags?.productTags.isNullOrEmpty()) View.GONE else View.VISIBLE
//        baseViewHolder.getView<ShopNameWithTagView?>(R.id.rl_icon_type)?.visibility = View.GONE
        baseViewHolder.getView<ImageView?>(R.id.iv_promotion_more)?.visibility = View.GONE
        baseViewHolder.getView<TextView?>(R.id.tv_retail_price)?.visibility = View.GONE
        baseViewHolder.getView<ProductEditLayoutNew?>(R.id.el_edit)?.apply {
            visibility = if (productNum <= 0) View.GONE else View.VISIBLE
        }
        baseViewHolder.getView<TextView?>(R.id.shop_price_tv)?.visibility = View.GONE
    }

    override fun onSellOut() {
        super.onSellOut()
        val skillBtn = baseViewHolder.getView<TextView>(R.id.tv_seckill_commit)
        if ((rowsBean.status == 2 || rowsBean.status == 4 || rowsBean.availableQty <= 0)) {
            skillBtn.visibility = View.GONE
        } else {
            skillBtn.visibility = View.VISIBLE
            skillBtn.text = "去抢购"
        }
    }

    override fun onGoodsPrice(showUnderlinePrice: Boolean, showPgbyUnderLineProce: Boolean) {
        // 增加价格展示逻辑, 包括控销、签署协议、是否符合协议标准展示价格
        baseViewHolder.setText(R.id.shop_price, rowsBean.getShowPgbyPriceStrNew(showPgbyUnderLineProce))
    }

    override fun onGoodsSpec() {
        super.onGoodsSpec()
        baseViewHolder.setVisible(R.id.tv_goods_spec, false)
        baseViewHolder.setVisible(R.id.iv_divider_of_spec_name, false)
    }

    override fun onFinal() {
        super.onFinal()
        setControl()
        try {
            setFindSameGoodsCoupon(false)
        } catch (e: Exception) {
            e.printStackTrace()
        }
        baseViewHolder.getView<TextView>(R.id.tv_seckill_commit).setOnClickListener {

            SearchProductReport.trackSearchItemBtnClickWholeSale(mContext, baseViewHolder.bindingAdapterPosition, rowsBean)
            if (rowsBean.actPgby != null && rowsBean.actPgby.isApplyListShowType) {
                val actPgby = rowsBean.actPgby
                val actPt: ActPtBean = ActPtBean().apply {
                    assemblePrice = actPgby.assemblePrice?: 0.0
                    skuStartNum = actPgby.skuStartNum?.toInt()?: 0
                    marketingId = actPgby.marketingId
                    supportSuiXinPin = actPgby.supportSuiXinPin
                    suiXinPinButtonText = actPgby.suiXinPinButtonText
                    suiXinPinButtonBubbleText = actPgby.suiXinPinButtonBubbleText
                    isApplyListShowType = actPgby.isApplyListShowType
                }
                rowsBean.actPt = actPt
                rowsBean.jgTrackBean = jgTrackBean
                val mPopWindowSpellGroup = SpellGroupPopWindow(
                    baseViewHolder.itemView.context,
                    rowsBean,
                    actPt,
                    true,
                    mIsList = true,
                    <EMAIL>,
                        jgTrackBean,
                        jgPageListCommonBean
                )
                rowsBean.actPt = null
//                mPopWindowSpellGroup.setData(rowsBean, rowsBean.actPt)
                mPopWindowSpellGroup.show(baseViewHolder.itemView)
            } else {
                var mUrl = "ybmpage://productdetail/" + rowsBean.id
                mUrl = splicingUrlWithParams(mUrl, hashMapOf(
                        Pair<String,Any>(IntentCanst.JG_ENTRANCE,jgTrackBean?.entrance?:""),
                        Pair<String,Any>(IntentCanst.JG_REFERRER,jgTrackBean?.jgReferrer?:""),
                        Pair<String,Any>(IntentCanst.JG_REFERRER_TITLE,jgTrackBean?.jgReferrerTitle?:""),
                        Pair<String,Any>(IntentCanst.JG_REFERRER_MODULE,jgTrackBean?.jgReferrerModule?:""),
                ))
                RoutersUtils.open(mUrl)
//                openUrl(
//                    "ybmpage://productdetail/" + rowsBean.id, mFlowData
//                )
            }

            //点击去抢购也认为是点击商品的埋点
            productClickTrackListener?.invoke(rowsBean,baseViewHolder.bindingAdapterPosition,null)
        }
    }

    //设置控销
    fun setControl() {
        if (!TextUtils.isEmpty(rowsBean.controlTitle)) {
            //秒杀商品隐藏秒杀加购按钮(未展开)
            val skillBtn = baseViewHolder.getView<TextView>(R.id.tv_seckill_commit)
            skillBtn.visibility = View.GONE
            //秒杀商品隐藏秒杀加购按钮(已展开)
            val skillBtn2 = baseViewHolder.getView<ProductEditLayoutNew>(R.id.el_edit)
            skillBtn2.visibility = View.GONE
            //倒计时
            val time = baseViewHolder.getView<SeckillTimeView>(R.id.st_countdown)
            time.visibility = View.GONE
            //进度条
            val progressBar = baseViewHolder.getView<SeekBar>(R.id.seckill_progress)
            progressBar.visibility = View.GONE
        }
        // <PRD-8>”未签署控销协议商品“显示价格 https://wiki.int.ybm100.com/pages/viewpage.action?pageId=776143984
        // 控销显示商品价格样式
        if (rowsBean.controlType == 5) {
            //秒杀商品隐藏秒杀加购按钮(未展开)
            val skillBtn = baseViewHolder.getView<TextView>(R.id.tv_seckill_commit)
            skillBtn.visibility = View.VISIBLE
            //秒杀商品隐藏秒杀加购按钮(已展开)
            val skillBtn2 = baseViewHolder.getView<ProductEditLayoutNew>(R.id.el_edit)
            skillBtn2.visibility = View.VISIBLE
            //倒计时
            val time = baseViewHolder.getView<SeckillTimeView>(R.id.st_countdown)
            time.visibility = View.VISIBLE
            //进度条
            val progressBar = baseViewHolder.getView<SeekBar>(R.id.seckill_progress)
            progressBar.visibility = View.VISIBLE
        }
    }

}