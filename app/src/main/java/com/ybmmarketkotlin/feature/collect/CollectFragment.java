package com.ybmmarketkotlin.feature.collect;

import android.content.Context;
import android.content.Intent;
import android.graphics.Rect;
import android.os.Bundle;

import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.recyclerview.widget.RecyclerView;

import android.text.TextUtils;
import android.view.View;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.apkfuns.logutils.LogUtils;
import com.google.gson.reflect.TypeToken;
import com.ybm.app.adapter.YBMBaseAdapter;
import com.ybm.app.bean.NetError;
import com.ybm.app.view.CommonRecyclerView;
import com.ybm.app.view.WrapLinearLayoutManager;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.BaseBean;
import com.ybmmarket20.bean.CollectBean;
import com.ybmmarket20.bean.EmptyBean;
import com.ybmmarket20.bean.RowsBean;
import com.ybmmarket20.common.AlertDialogEx;
import com.ybmmarket20.common.AppUtilKt;
import com.ybmmarket20.common.BaseResponse;
import com.ybmmarket20.common.JGTrackManager;
import com.ybmmarket20.common.JGTrackTopLevelKt;
import com.ybmmarket20.common.JgTrackBean;
import com.ybmmarket20.common.YBMAppLike;
import com.ybmmarket20.common.eventbus.EventBusUtil;
import com.ybmmarket20.common.util.ConvertUtils;
import com.ybmmarket20.network.HttpManager;
import com.ybmmarket20.common.LicenseStatusWithRefreshAndLazyFragment;
import com.ybmmarket20.common.RequestParams;
import com.ybmmarket20.common.util.ToastUtils;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.constant.IntentCanst;
import com.ybmmarket20.utils.AdapterUtils;
import com.ybmmarket20.utils.SpUtil;
import com.ybmmarket20.utils.analysis.FlowDataAnalysisManagerKt;
import com.ybmmarket20.utils.analysis.FlowDataEventAnalysisKt;
import com.ybmmarket20.utils.analysis.XyyIoUtil;
import com.ybmmarket20.view.MyFastScrollView;
import com.ybmmarketkotlin.utils.RouterJump;

import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.List;

import butterknife.Bind;
import butterknife.ButterKnife;
import butterknife.OnClick;

/**
 * 收藏
 */
public class CollectFragment extends LicenseStatusWithRefreshAndLazyFragment<RowsBean, CollectBean<RowsBean>> {

    @Bind(R.id.crv_refresh_common)
    CommonRecyclerView mList;
    @Bind(R.id.shop_check)
    CheckBox mShopCheck;
    @Bind(R.id.collect_ll_01)
    LinearLayout mCollectLl01;
    @Bind(R.id.collect_tv)
    TextView mCollectTv;
    @Bind(R.id.collect_btn)
    Button mCollectBtn;
    @Bind(R.id.collect_rl)
    RelativeLayout mCollectRl;
    @Bind(R.id.iv_fastscroll)
    MyFastScrollView mFastScroll;

    private int mTab;
    private int pageSize = 50;
    private CollectActivity mActivity;

    private boolean isTrackOpenPage = false;
    private String mEntrance = "我的-我的收藏";
    String tabName = "";

    @Override
    protected void initData(String content) {
        mTab = getArguments().getInt("tab");
        tabName = getArguments().getString("tabName");
        if (getCollectAdapter()!=null && getCollectAdapter().getJgTrackBean()!=null){
            JgTrackBean jgTrackBean = getCollectAdapter().getJgTrackBean();
            jgTrackBean.setNavigation_1(tabName);
            getCollectAdapter().setJgTrackBean(jgTrackBean);
        }
        //监听activity点击tab，点击取消编辑状态
        switch (mTab) {
            case 0:
                if (mActivity != null) {
                    mActivity.setFragment0ClickedListener(this::setVisible);
                }
                break;
            case 1:
                if (mActivity != null) {
                    mActivity.setFragment1ClickedListener(this::setVisible);
                }
                break;
            case 2:
                if (mActivity != null) {
                    mActivity.setFragment2ClickedListener(this::setVisible);
                }
                break;
            case 3:
                if (mActivity != null) {
                    mActivity.setFragment3ClickedListener(this::setVisible);
                }
                break;
        }
        initListener();
        getRecyclerView().setLayoutManager(new WrapLinearLayoutManager(getContext()));
        getRecyclerView().addItemDecoration(new CollectItemDecoration());
    }


    public static class CollectItemDecoration extends RecyclerView.ItemDecoration {
        private int space = ConvertUtils.dp2px(1);

        @Override
        public void getItemOffsets(Rect outRect, View view, RecyclerView parent, RecyclerView.State state) {
            if (parent.getChildAdapterPosition(view) == 0) {
                outRect.top = space * 10;
            } else {
                outRect.top = 0;
            }
            outRect.right = space * 10;
            outRect.bottom = space * 10;
            outRect.left = space * 10;
        }
    }

    @Override
    public String getTitle() {
        return null;
    }

    /*
     * 初始化监听
     * */
    private void initListener() {
        mCollectLl01.setOnClickListener(new CollectFragment.onClickListener(mShopCheck));
        mList.setOnScrollListener(new CommonRecyclerView.OnScrollListener() {
            @Override
            public void onScrollChanged(int x, int y) {

            }

            @Override
            public void onScrollRollingDistance(int y, int dy) {
                if (mFastScroll != null) {
                    mFastScroll.showFastScroll(mList, y, dy, null);
                }
            }

            @Override
            public void onScrollState(int i) {

            }

        });
        getCollectAdapter().setOnItemClickListener((adapter, view, position) -> {
            String url = "ybmpage://productdetail?" + IntentCanst.PRODUCTID + "=" + rows.get(position).getId();

            HashMap<String,Object> mParams = new HashMap<>();
            mParams.put(IntentCanst.JG_REFERRER,"com.ybmmarketkotlin.feature.collect.CollectFragment");
            mParams.put(IntentCanst.JG_REFERRER_TITLE,mEntrance);
            mParams.put(IntentCanst.JG_REFERRER_MODULE,mEntrance);
            mParams.put(IntentCanst.JG_ENTRANCE,mEntrance);
            RouterJump.INSTANCE.jump2ProductDetail(url,mParams);
        });


    }

    public CollectAdapterKt getCollectAdapter() {
        return getAdapter();
    }

    /**
     * 设置全选
     *
     * @param isAllSelected 是否全选
     */
    private void setEditSelected(boolean isAllSelected) {
        if (mShopCheck == null) {
            return;
        }
        mShopCheck.setChecked(isAllSelected);
    }

    /**
     * 当前为编辑状态，点击其他tab关闭编辑状态
     *
     * @param isEdit 是否显示编辑状态的checkbox
     */
    private void setVisible(boolean isEdit) {
        if (mCollectRl == null) {
            return;
        }
        if (getCollectAdapter() != null) {
            getCollectAdapter().setShowChecked(isEdit);
            getCollectAdapter().notifyDataSetChanged();
        }
        mCollectRl.setVisibility(isEdit ? View.VISIBLE : View.GONE);
        if (!isEdit) {
            cancelAll();
        }
    }

    @Override
    protected void initTitle() {

    }

    @Override
    public int getLayoutId() {
        return R.layout.fragment_callect;
    }

    public static CollectFragment getInstance(int tab,String tabName) {
        CollectFragment fragment = new CollectFragment();
        fragment.setArguments(setArguments(tab,tabName));
        return fragment;
    }

    public static Bundle setArguments(int tab,String tabName) {
        Bundle bundle = new Bundle();
        bundle.putInt("tab", tab);
        bundle.putString("tabName", tabName);
        return bundle;
    }

    @Override
    public void onAttach(Context context) {
        super.onAttach(context);
        mActivity = (CollectActivity) context;
    }

    /*
     * 编辑-全取消
     * */
    private void cancelAll() {
        if (null != rows) {
            if (rows.size() > 0) {
                for (int i = 0; i < rows.size(); i++) {
                    getCollectAdapter().getSelectPosition().clear();
                }
            }
            if (getCollectAdapter() != null) {
                getCollectAdapter().notifyDataSetChanged();
            }
            mShopCheck.setChecked(false);
        }
    }

    /*
     * 编辑-全选
     * */
    private void selectedAll() {
        if (null != rows) {
            getCollectAdapter().setShowChecked(true);
            if (rows.size() > 0) {
                for (int i = 0; i < rows.size(); i++) {
                    getCollectAdapter().getSelectPosition().add(i);
                }
            }
            if (getCollectAdapter() != null) {
                getCollectAdapter().notifyDataSetChanged();
            }
            mShopCheck.setChecked(true);
        }
    }

    @Override
    public boolean onLicenseStatusEnable() {
        return true;
    }

    @Override
    public void handleLicenseStatusChange(int status) {

    }


    @Override
    public void beforeAddDataToList(CollectBean<RowsBean> rowsBeanCollectBean) {
        // 添加埋点
        if (!TextUtils.isEmpty(rowsBeanCollectBean.getSptype()) || !TextUtils.isEmpty(rowsBeanCollectBean.getSpid()) || !TextUtils.isEmpty(rowsBeanCollectBean.getSid())) {
            FlowDataAnalysisManagerKt.updateFlowData(mFlowData, rowsBeanCollectBean.getSptype(), rowsBeanCollectBean.getSpid(), rowsBeanCollectBean.getSid(), null);
            getCollectAdapter().setFlowData(mFlowData);
        }
        // 为拼团数据添加本地时间
        AdapterUtils.INSTANCE.addLocalTimeForRows(rowsBeanCollectBean.getRows());
    }

    @Override
    public void afterAddDataToList(List<RowsBean> rowsList) {
        // 请求并更新折后价
        AdapterUtils.INSTANCE.getAfterDiscountPrice(rowsList, getCollectAdapter());
    }

    @Override
    public void afterData(CollectBean<RowsBean> data) {
        super.afterData(data);
        if (!isTrackOpenPage) {
            isTrackOpenPage = true;
            HashMap<String, String> trackParams = new HashMap<>();
            trackParams.put("sptype", data.getSptype());
            trackParams.put("spid", data.getSpid());
            trackParams.put("sid", data.getSid());
            XyyIoUtil.track("page_CommodityList", trackParams);
        }
    }

    /**
     * 全选按钮点击事件
     */
    private class onClickListener implements View.OnClickListener {

        private CheckBox cb;

        private onClickListener(CheckBox cb) {
            this.cb = cb;
        }

        @Override
        public void onClick(View v) {

            switch (v.getId()) {
                case R.id.collect_ll_01:

                    if (rows != null && rows.size() > 0) {
                        if (getCollectAdapter().getSelectPosition().size() == rows.size()) {
                            cancelAll();
                        } else {
                            selectedAll();
                        }
                    } else {
                        mCollectLl01.setEnabled(false);
                        mShopCheck.setChecked(false);
                    }
                    break;
            }
        }
    }

    @OnClick({R.id.collect_btn})
    public void clickTab(View view) {
        switch (view.getId()) {
            case R.id.collect_btn:
                deleteCollectItem();
                break;
        }
    }

    /**
     * 批量删除
     */
    private void deleteCollectItem() {

        StringBuffer ids = new StringBuffer();
        long id = -1;

        if (getCollectAdapter() != null && getCollectAdapter().getSelectPosition().size() > 0 && rows != null && rows.size() > 0) {

            for (int i = 0; i < getCollectAdapter().getSelectPosition().size(); i++) {
                Integer position = getCollectAdapter().getSelectPosition().get(i);
                RowsBean rowsBean = rows.get(position);
                id = rowsBean.getId();
                if (id != -1) {
                    ids.append(id);
                    ids.append(",");
                }
            }

        }

        if (ids.length() > 0) {
            ids.deleteCharAt(ids.length() - 1);
        }

        if (TextUtils.isEmpty(ids)) {
            ToastUtils.showShort("请选中要取消收藏的商品");
        } else {
            LogUtils.d("取消收藏的商品id为 ： " + ids);
            showSaveDialog((dialog, button) -> showDialogDelete(ids));
        }
    }

    /**
     * @param listener
     */
    private void showSaveDialog(AlertDialogEx.OnClickListener listener) {

        String str = "你确定删除该商品吗?";
        AlertDialogEx dialogEx = new AlertDialogEx(getNotNullActivity());
        dialogEx.setMessage(str).setCancelButton("取消", new AlertDialogEx.OnClickListener() {
            @Override
            public void onClick(AlertDialogEx dialog, int button) {
                dialog.dismiss();
            }
        }).setCancelable(false).setConfirmButton("确定", listener).setCanceledOnTouchOutside(false).setTitle(null).show();
    }


    /**
     * 批量删除收藏商品
     *
     * @param skuIdList id集合
     */
    private void showDialogDelete(StringBuffer skuIdList) {

        String merchantid = SpUtil.getMerchantid();
        RequestParams params = new RequestParams();
        params.put("merchantId", merchantid);
        if (!TextUtils.isEmpty(skuIdList)) {
            params.put("skuIdList", skuIdList.toString());
        }
        showProgress();
        HttpManager.getInstance().post(AppNetConfig.CANCEL_ATTENTION_BATH_DELETE, params, new BaseResponse<EmptyBean>() {

            @Override
            public void onSuccess(String content, BaseBean<EmptyBean> obj, EmptyBean baseBean) {
                dismissProgress();
                if (null != obj) {
                    if (obj.isSuccess()) {
                        getCollectAdapter().getSelectPosition().clear();
                        // 增加取消收藏的提示
                        if (!TextUtils.isEmpty(obj.msg)) {
                            ToastUtils.showShort(obj.msg);
                        }
                        showProgress();
                        onRefresh();
                        LocalBroadcastManager.getInstance(YBMAppLike.getAppContext()).sendBroadcast(new Intent(IntentCanst.ACTION_DEL_FAVORITE));
                    }
                }
            }

            @Override
            public void onFailure(NetError error) {
                dismissProgress();
            }
        });
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        ButterKnife.unbind(this);
    }

    @Override
    protected RequestParams getRequestParams() {
        RequestParams params = new RequestParams();
        switch (mTab) {
            case 0:
            default:
                break;
            case 1:
                params.put("businessType", "2");
                break;
            case 2:
                params.put("businessType", "1");
                break;
            case 3:
                params.put("businessType", "3");
                break;
        }
        FlowDataAnalysisManagerKt.addAnalysisRequestParams(params, mFlowData);
        return params;
    }

    @Override
    protected YBMBaseAdapter<RowsBean> getAdapter(List<RowsBean> rows) {
        if (getAdapter() == null) {
            // guanchong 收藏
//    	    CollectAdapter adapter = new CollectAdapter(R.layout.fragment_collect_item, rows, false);
            CollectAdapterKt adapter = new CollectAdapterKt(R.layout.item_goods_new_collect, rows);
            adapter.setFlowData(FlowDataAnalysisManagerKt.generateDefaultBaseFlowData());
            JgTrackBean jgTrackBean = new JgTrackBean();
            jgTrackBean.setPageId(JGTrackManager.TrackMineCollect.PAGE_ID);
            jgTrackBean.setTitle(JGTrackManager.TrackMineCollect.TITLE);
            jgTrackBean.setModule(JGTrackManager.Common.MODULE_PRODUCT_LIST);
            jgTrackBean.setJgReferrerModule(JGTrackManager.TrackMineCollect.TITLE);
            jgTrackBean.setJgReferrerTitle(JGTrackManager.TrackMineCollect.TITLE);
            jgTrackBean.setJgReferrer("com.ybmmarketkotlin.feature.collect.CollectFragment");
            jgTrackBean.setUrl("com.ybmmarketkotlin.feature.collect.CollectFragment");
            jgTrackBean.setNavigation_1(tabName);
            jgTrackBean.setEntrance(mEntrance);
            adapter.setJgTrackBean(jgTrackBean);

            adapter.setResourceViewTrackListener((rowsBean, integer,pageListCommonBean) -> {
                StringBuilder productTag = new StringBuilder();
                String productId = "";
                String productName = "";
                String productType = "";
                double productPrice = 0.0;

                if (rowsBean != null) {
                    if (rowsBean.getProductId() != null) {
                        productId = rowsBean.getProductId();
                    }
                    if (rowsBean.getProductName() != null) {
                        productName = rowsBean.getProductName();
                    }
                    productPrice = rowsBean.getJgProductPrice();
                }
                if (rowsBean!=null){
                    productType = rowsBean.getJgProductType();
                }

                if (rowsBean != null && rowsBean.tags != null && rowsBean.tags.productTags != null && rowsBean.tags.productTags.size() > 0) {
                    for (int i = 0; i < rowsBean.tags.productTags.size(); i++) {
                        if (i != rowsBean.tags.productTags.size() - 1) {
                            productTag.append(rowsBean.tags.productTags.get(i)).append("，");
                        } else {
                            productTag.append(rowsBean.tags.productTags.get(i));
                        }
                    }
                }
                if (rowsBean != null && rowsBean.tags != null && rowsBean.tags.dataTags != null && rowsBean.tags.dataTags.size() > 0) {
                    if (productTag.length()>0){
                        productTag.append("，");
                    }
                    for (int i = 0; i < rowsBean.tags.dataTags.size(); i++) {
                        if (i != rowsBean.tags.dataTags.size() - 1) {
                            productTag.append(rowsBean.tags.dataTags.get(i).text).append("，");
                        } else {
                            productTag.append(rowsBean.tags.dataTags.get(i).text);
                        }
                    }
                }
                return null;
            });
            adapter.setProductClickTrackListener((rowsBean, integer,isBtnClick,mContent,number) -> {
                StringBuilder productTag = new StringBuilder();
                String productId = "";
                String productName = "";
                String productType = "";
                double productPrice = 0.0;

                if (rowsBean != null) {
                    if (rowsBean.getProductId() != null) {
                        productId = rowsBean.getProductId();
                    }
                    if (rowsBean.getProductName() != null) {
                        productName = rowsBean.getProductName();
                    }
                    productPrice = rowsBean.getJgProductPrice();
                }

                if (rowsBean!=null){
                    productType = rowsBean.getJgProductType();
                }

                if (rowsBean != null && rowsBean.tags != null && rowsBean.tags.productTags != null && rowsBean.tags.productTags.size() > 0) {
                    for (int i = 0; i < rowsBean.tags.productTags.size(); i++) {
                        if (i != rowsBean.tags.productTags.size() - 1) {
                            productTag.append(rowsBean.tags.productTags.get(i).text).append("，");
                        } else {
                            productTag.append(rowsBean.tags.productTags.get(i).text);
                        }
                    }
                }
                if (rowsBean != null && rowsBean.tags != null && rowsBean.tags.dataTags != null && rowsBean.tags.dataTags.size() > 0) {
                    if (productTag.length()>0){
                        productTag.append("，");
                    }
                    for (int i = 0; i < rowsBean.tags.dataTags.size(); i++) {
                        if (i != rowsBean.tags.dataTags.size() - 1) {
                            productTag.append(rowsBean.tags.dataTags.get(i).text).append("，");
                        } else {
                            productTag.append(rowsBean.tags.dataTags.get(i).text);
                        }
                    }
                }
                return null;
            });

            return adapter;
        }
        return getAdapter();
    }

    @Override
    protected Type getType() {
        return new TypeToken<BaseBean<CollectBean<RowsBean>>>() {
        }.getType();
    }

    @Override
    protected String getUrl() {
        return AppNetConfig.COLLECTPAGER;
    }

    @Override
    protected boolean enableRefresh() {
        return false;
    }

    @Override
    protected int getStartPage() {
        return 0;
    }

    @Override
    protected int getLimit() {
        return pageSize;
    }

    @Override
    protected String getEmptyMsg() {
        return "这里已经空空如也";
    }

    @Override
    protected int getEmptyImg() {
        return R.drawable.icon_empty;
    }
}
